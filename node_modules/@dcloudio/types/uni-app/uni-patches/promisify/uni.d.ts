interface Uni {
    startFacialRecognitionVerify<T extends UniNamespace.StartFacialRecognitionVerifyOption = UniNamespace.StartFacialRecognitionVerifyOption>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.StartFacialRecognitionVerifyOption>;
    getBatteryInfo<T extends UniNamespace.GetBatteryInfoOption = UniNamespace.GetBatteryInfoOption>(option?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetBatteryInfoOption>;
    startWifi<T extends UniNamespace.StartWifiOption = UniNamespace.StartWifiOption>(option?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.StartWifiOption>;
    stopWifi<T extends UniNamespace.StopWifiOption = UniNamespace.StopWifiOption>(option?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.StopWifiOption>;
    connectWifi<T extends UniNamespace.ConnectWifiOption = UniNamespace.ConnectWifiOption>(option: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.ConnectWifiOption>;
    getConnectedWifi<T extends UniNamespace.GetConnectedWifiOption = UniNamespace.GetConnectedWifiOption>(option?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetConnectedWifiOption>;
    getWifiList<T extends UniNamespace.GetWifiListOption = UniNamespace.GetWifiListOption>(option?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetWifiListOption>;
    configMTLS<T extends UniNamespace.ConfigMTLSOptions = UniNamespace.ConfigMTLSOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.ConfigMTLSOptions>;
    sendSocketMessage<T extends UniNamespace.SendSocketMessageOptions = UniNamespace.SendSocketMessageOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.SendSocketMessageOptions>;
    closeSocket<T extends UniNamespace.CloseSocketOptions = UniNamespace.CloseSocketOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.CloseSocketOptions>;
    chooseImage<T extends UniNamespace.ChooseImageOptions = UniNamespace.ChooseImageOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.ChooseImageOptions>;
    chooseFile<T extends UniNamespace.ChooseFileOptions = UniNamespace.ChooseFileOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.ChooseFileOptions>;
    previewImage<T extends UniNamespace.PreviewImageOptions = UniNamespace.PreviewImageOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.PreviewImageOptions>;
    closePreviewImage<T extends UniNamespace.CallBackOptions = UniNamespace.CallBackOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.CallBackOptions>;
    getImageInfo<T extends UniNamespace.GetImageInfoOptions = UniNamespace.GetImageInfoOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetImageInfoOptions>;
    saveImageToPhotosAlbum<T extends UniNamespace.SaveImageToPhotosAlbumOptions = UniNamespace.SaveImageToPhotosAlbumOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.SaveImageToPhotosAlbumOptions>;
    compressImage<T extends UniNamespace.CompressImageOptions = UniNamespace.CompressImageOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.CompressImageOptions>;
    chooseVideo<T extends UniNamespace.ChooseVideoOptions = UniNamespace.ChooseVideoOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.ChooseVideoOptions>;
    compressVideo<T extends UniNamespace.CompressVideoOptions = UniNamespace.CompressVideoOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.CompressVideoOptions>;
    getVideoInfo<T extends UniNamespace.GetVideoInfoOptions = UniNamespace.GetVideoInfoOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetVideoInfoOptions>;
    openVideoEditor<T extends UniNamespace.OpenVideoEditorOptions = UniNamespace.OpenVideoEditorOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.OpenVideoEditorOptions>;
    saveVideoToPhotosAlbum<T extends UniNamespace.SaveVideoToPhotosAlbumOptions = UniNamespace.SaveVideoToPhotosAlbumOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.SaveVideoToPhotosAlbumOptions>;
    saveFile<T extends UniNamespace.SaveFileOptions = UniNamespace.SaveFileOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.SaveFileOptions>;
    getFileInfo<T extends UniNamespace.GetFileInfoOptions = UniNamespace.GetFileInfoOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetFileInfoOptions>;
    getSavedFileList<T extends UniNamespace.GetSavedFileListOptions = UniNamespace.GetSavedFileListOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetSavedFileListOptions>;
    getSavedFileInfo<T extends UniNamespace.GetSavedFileInfoOptions = UniNamespace.GetSavedFileInfoOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetSavedFileInfoOptions>;
    removeSavedFile<T extends UniNamespace.RemoveSavedFileOptions = UniNamespace.RemoveSavedFileOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.RemoveSavedFileOptions>;
    openDocument<T extends UniNamespace.OpenDocumentOptions = UniNamespace.OpenDocumentOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.OpenDocumentOptions>;
    setStorage<T extends UniNamespace.SetStorageOptions = UniNamespace.SetStorageOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.SetStorageOptions>;
    getStorage<T = any, U extends UniNamespace.GetStorageOptions<T> = UniNamespace.GetStorageOptions<T>>(options: U): UniNamespace.PromisifySuccessResult<U, UniNamespace.GetStorageOptions<T>>;
    getStorageInfo<T extends UniNamespace.GetStorageInfoOptions = UniNamespace.GetStorageInfoOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetStorageInfoOptions>;
    removeStorage<T extends UniNamespace.RemoveStorageOptions = UniNamespace.RemoveStorageOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.RemoveStorageOptions>;
    getLocation<T extends UniNamespace.GetLocationOptions = UniNamespace.GetLocationOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetLocationOptions>;
    chooseLocation<T extends UniNamespace.ChooseLocationOptions = UniNamespace.ChooseLocationOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.ChooseLocationOptions>;
    openLocation<T extends UniNamespace.OpenLocationOptions = UniNamespace.OpenLocationOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.OpenLocationOptions>;
    getSystemInfo<T extends UniNamespace.GetSystemInfoOptions = UniNamespace.GetSystemInfoOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetSystemInfoOptions>;
    getNetworkType<T extends UniNamespace.GetNetworkTypeOptions = UniNamespace.GetNetworkTypeOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetNetworkTypeOptions>;
    startAccelerometer<T extends UniNamespace.StartAccelerometerOptions = UniNamespace.StartAccelerometerOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.StartAccelerometerOptions>;
    stopAccelerometer<T extends UniNamespace.StopAccelerometerOptions = UniNamespace.StopAccelerometerOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.StopAccelerometerOptions>;
    startCompass<T extends UniNamespace.StartCompassOptions = UniNamespace.StartCompassOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.StartCompassOptions>;
    stopCompass<T extends UniNamespace.StopCompassOptions = UniNamespace.StopCompassOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.StopCompassOptions>;
    makePhoneCall<T extends UniNamespace.MakePhoneCallOptions = UniNamespace.MakePhoneCallOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.MakePhoneCallOptions>;
    scanCode<T extends UniNamespace.ScanCodeOptions = UniNamespace.ScanCodeOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.ScanCodeOptions>;
    setClipboardData<T extends UniNamespace.SetClipboardDataOptions = UniNamespace.SetClipboardDataOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.SetClipboardDataOptions>;
    getClipboardData<T extends UniNamespace.GetClipboardDataOptions = UniNamespace.GetClipboardDataOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetClipboardDataOptions>;
    openAppAuthorizeSetting<T extends UniNamespace.CallBackOptions = UniNamespace.CallBackOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.CallBackOptions>;
    getSelectedTextRange<T extends UniNamespace.GetSelectedTextRangeOptions = UniNamespace.GetSelectedTextRangeOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetSelectedTextRangeOptions>;
    setScreenBrightness<T extends UniNamespace.SetScreenBrightnessOptions = UniNamespace.SetScreenBrightnessOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.SetScreenBrightnessOptions>;
    getScreenBrightness<T extends UniNamespace.GetScreenBrightnessOptions = UniNamespace.GetScreenBrightnessOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetScreenBrightnessOptions>;
    setKeepScreenOn<T extends UniNamespace.SetKeepScreenOnOptions = UniNamespace.SetKeepScreenOnOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.SetKeepScreenOnOptions>;
    vibrate<T extends UniNamespace.VibrateOptions = UniNamespace.VibrateOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.VibrateOptions>;
    vibrateLong<T extends UniNamespace.VibrateLongOptions = UniNamespace.VibrateLongOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.VibrateLongOptions>;
    vibrateShort<T extends UniNamespace.VibrateShortOptions = UniNamespace.VibrateShortOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.VibrateShortOptions>;
    addPhoneContact<T extends UniNamespace.AddPhoneContactOptions = UniNamespace.AddPhoneContactOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.AddPhoneContactOptions>;
    getBeacons<T extends UniNamespace.GetBeaconsOptions = UniNamespace.GetBeaconsOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetBeaconsOptions>;
    startBeaconDiscovery<T extends UniNamespace.StartBeaconDiscoveryOptions = UniNamespace.StartBeaconDiscoveryOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.StartBeaconDiscoveryOptions>;
    stopBeaconDiscovery<T extends UniNamespace.StopBeaconDiscoveryOptions = UniNamespace.StopBeaconDiscoveryOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.StopBeaconDiscoveryOptions>;
    closeBluetoothAdapter<T extends UniNamespace.CloseBluetoothAdapterOptions = UniNamespace.CloseBluetoothAdapterOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.CloseBluetoothAdapterOptions>;
    getBluetoothAdapterState<T extends UniNamespace.GetBluetoothAdapterStateOptions = UniNamespace.GetBluetoothAdapterStateOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetBluetoothAdapterStateOptions>;
    getBluetoothDevices<T extends UniNamespace.GetBluetoothDevicesOptions = UniNamespace.GetBluetoothDevicesOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetBluetoothDevicesOptions>;
    getConnectedBluetoothDevices<T extends UniNamespace.GetConnectedBluetoothDevicesOptions = UniNamespace.GetConnectedBluetoothDevicesOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetConnectedBluetoothDevicesOptions>;
    openBluetoothAdapter<T extends UniNamespace.OpenBluetoothAdapterOptions = UniNamespace.OpenBluetoothAdapterOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.OpenBluetoothAdapterOptions>;
    startBluetoothDevicesDiscovery<T extends UniNamespace.StartBluetoothDevicesDiscoveryOptions = UniNamespace.StartBluetoothDevicesDiscoveryOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.StartBluetoothDevicesDiscoveryOptions>;
    stopBluetoothDevicesDiscovery<T extends UniNamespace.StopBluetoothDevicesDiscoveryOptions = UniNamespace.StopBluetoothDevicesDiscoveryOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.StopBluetoothDevicesDiscoveryOptions>;
    closeBLEConnection<T extends UniNamespace.CloseBLEConnectionOptions = UniNamespace.CloseBLEConnectionOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.CloseBLEConnectionOptions>;
    createBLEConnection<T extends UniNamespace.CreateBLEConnectionOptions = UniNamespace.CreateBLEConnectionOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.CreateBLEConnectionOptions>;
    getBLEDeviceCharacteristics<T extends UniNamespace.GetBLEDeviceCharacteristicsOptions = UniNamespace.GetBLEDeviceCharacteristicsOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetBLEDeviceCharacteristicsOptions>;
    getBLEDeviceServices<T extends UniNamespace.GetBLEDeviceServicesOptions = UniNamespace.GetBLEDeviceServicesOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetBLEDeviceServicesOptions>;
    notifyBLECharacteristicValueChange<T extends UniNamespace.NotifyBLECharacteristicValueChangeOptions = UniNamespace.NotifyBLECharacteristicValueChangeOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.NotifyBLECharacteristicValueChangeOptions>;
    readBLECharacteristicValue<T extends UniNamespace.ReadBLECharacteristicValueOptions = UniNamespace.ReadBLECharacteristicValueOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.ReadBLECharacteristicValueOptions>;
    writeBLECharacteristicValue<T extends UniNamespace.WriteBLECharacteristicValueOptions = UniNamespace.WriteBLECharacteristicValueOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.WriteBLECharacteristicValueOptions>;
    setBLEMTU<T extends UniNamespace.SetBLEMTUOptions = UniNamespace.SetBLEMTUOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.SetBLEMTUOptions>;
    getBLEDeviceRSSI<T extends UniNamespace.GetBLEDeviceRSSIOptions = UniNamespace.GetBLEDeviceRSSIOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetBLEDeviceRSSIOptions>;
    showToast<T extends UniNamespace.ShowToastOptions = UniNamespace.ShowToastOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.ShowToastOptions>;
    showLoading<T extends UniNamespace.ShowLoadingOptions = UniNamespace.ShowLoadingOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.ShowLoadingOptions>;
    showModal<T extends UniNamespace.ShowModalOptions = UniNamespace.ShowModalOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.ShowModalOptions>;
    showActionSheet<T extends UniNamespace.ShowActionSheetOptions = UniNamespace.ShowActionSheetOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.ShowActionSheetOptions>;
    setNavigationBarTitle<T extends UniNamespace.SetNavigationBarTitleOptions = UniNamespace.SetNavigationBarTitleOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.SetNavigationBarTitleOptions>;
    setNavigationBarColor<T extends UniNamespace.SetNavigationbarColorOptions = UniNamespace.SetNavigationbarColorOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.SetNavigationbarColorOptions>;
    setTabBarItem<T extends UniNamespace.SetTabBarItemOptions = UniNamespace.SetTabBarItemOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.SetTabBarItemOptions>;
    setTabBarStyle<T extends UniNamespace.SetTabBarStyleOptions = UniNamespace.SetTabBarStyleOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.SetTabBarStyleOptions>;
    hideTabBar<T extends UniNamespace.HideTabBarOptions = UniNamespace.HideTabBarOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.HideTabBarOptions>;
    showTabBar<T extends UniNamespace.ShowTabBarOptions = UniNamespace.ShowTabBarOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.ShowTabBarOptions>;
    setTabBarBadge<T extends UniNamespace.SetTabBarBadgeOptions = UniNamespace.SetTabBarBadgeOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.SetTabBarBadgeOptions>;
    removeTabBarBadge<T extends UniNamespace.RemoveTabBarBadgeOptions = UniNamespace.RemoveTabBarBadgeOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.RemoveTabBarBadgeOptions>;
    showTabBarRedDot<T extends UniNamespace.ShowTabBarRedDotOptions = UniNamespace.ShowTabBarRedDotOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.ShowTabBarRedDotOptions>;
    hideTabBarRedDot<T extends UniNamespace.HideTabBarRedDotOptions = UniNamespace.HideTabBarRedDotOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.HideTabBarRedDotOptions>;
    navigateTo<T extends UniNamespace.NavigateToOptions = UniNamespace.NavigateToOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.NavigateToOptions>;
    redirectTo<T extends UniNamespace.RedirectToOptions = UniNamespace.RedirectToOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.RedirectToOptions>;
    reLaunch<T extends UniNamespace.ReLaunchOptions = UniNamespace.ReLaunchOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.ReLaunchOptions>;
    switchTab<T extends UniNamespace.SwitchTabOptions = UniNamespace.SwitchTabOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.SwitchTabOptions>;
    navigateBack<T extends UniNamespace.NavigateBackOptions = UniNamespace.NavigateBackOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.NavigateBackOptions>;
    preloadPage<T extends UniNamespace.PreloadPageOptions = UniNamespace.PreloadPageOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.PreloadPageOptions>;
    pageScrollTo<T extends UniNamespace.PageScrollToOptions = UniNamespace.PageScrollToOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.PageScrollToOptions>;
    startPullDownRefresh<T extends UniNamespace.StartPullDownRefreshOptions = UniNamespace.StartPullDownRefreshOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.StartPullDownRefreshOptions>;
    canvasToTempFilePath<T extends UniNamespace.CanvasToTempFilePathOptions = UniNamespace.CanvasToTempFilePathOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.CanvasToTempFilePathOptions>;
    canvasGetImageData<T extends UniNamespace.CanvasGetImageDataOptions = UniNamespace.CanvasGetImageDataOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.CanvasGetImageDataOptions>;
    canvasPutImageData<T extends UniNamespace.CanvasPutImageDataOptions = UniNamespace.CanvasPutImageDataOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.CanvasPutImageDataOptions>;
    showTopWindow<T extends UniNamespace.CommonOptions = UniNamespace.CommonOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.CommonOptions>;
    hideTopWindow<T extends UniNamespace.CommonOptions = UniNamespace.CommonOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.CommonOptions>;
    showLeftWindow<T extends UniNamespace.CommonOptions = UniNamespace.CommonOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.CommonOptions>;
    hideLeftWindow<T extends UniNamespace.CommonOptions = UniNamespace.CommonOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.CommonOptions>;
    showRightWindow<T extends UniNamespace.CommonOptions = UniNamespace.CommonOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.CommonOptions>;
    hideRightWindow<T extends UniNamespace.CommonOptions = UniNamespace.CommonOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.CommonOptions>;
    getProvider<T extends UniNamespace.GetProviderOptions = UniNamespace.GetProviderOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetProviderOptions>;
    login<T extends UniNamespace.LoginOptions = UniNamespace.LoginOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.LoginOptions>;
    checkSession<T extends UniNamespace.CheckSessionOptions = UniNamespace.CheckSessionOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.CheckSessionOptions>;
    getUserInfo<T extends UniNamespace.GetUserInfoOptions = UniNamespace.GetUserInfoOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetUserInfoOptions>;
    getUserProfile<T extends UniNamespace.GetUserProfileOptions = UniNamespace.GetUserProfileOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetUserProfileOptions>;
    preLogin<T extends UniNamespace.PreLoginOptions = UniNamespace.PreLoginOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.PreLoginOptions>;
    getCheckBoxState<T extends UniNamespace.GetCheckBoxStateOptions = UniNamespace.GetCheckBoxStateOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetCheckBoxStateOptions>;
    share<T extends UniNamespace.ShareOptions = UniNamespace.ShareOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.ShareOptions>;
    shareWithSystem<T extends UniNamespace.ShareWithSystemOptions = UniNamespace.ShareWithSystemOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.ShareWithSystemOptions>;
    getPushClientId<T extends UniNamespace.GetPushClientIdOptions = UniNamespace.GetPushClientIdOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetPushClientIdOptions>;
    requestPayment<T extends UniNamespace.RequestPaymentOptions = UniNamespace.RequestPaymentOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.RequestPaymentOptions>;
    authorize<T extends UniNamespace.AuthorizeOptions = UniNamespace.AuthorizeOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.AuthorizeOptions>;
    openSetting<T extends UniNamespace.OpenSettingOptions = UniNamespace.OpenSettingOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.OpenSettingOptions>;
    getSetting<T extends UniNamespace.GetSettingOptions = UniNamespace.GetSettingOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetSettingOptions>;
    chooseAddress<T extends UniNamespace.ChooseAddressOptions = UniNamespace.ChooseAddressOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.ChooseAddressOptions>;
    chooseInvoiceTitle<T extends UniNamespace.ChooseInvoiceTitleOptions = UniNamespace.ChooseInvoiceTitleOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.ChooseInvoiceTitleOptions>;
    navigateToMiniProgram<T extends UniNamespace.NavigateToMiniProgramOptions = UniNamespace.NavigateToMiniProgramOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.NavigateToMiniProgramOptions>;
    navigateBackMiniProgram<T extends UniNamespace.NavigateBackMiniProgramOptions = UniNamespace.NavigateBackMiniProgramOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.NavigateBackMiniProgramOptions>;
    setEnableDebug<T extends UniNamespace.SetEnableDebugOptions = UniNamespace.SetEnableDebugOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.SetEnableDebugOptions>;
    getExtConfig<T extends UniNamespace.GetExtConfigOptions = UniNamespace.GetExtConfigOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.GetExtConfigOptions>;
    showShareMenu<T extends UniNamespace.ShowShareMenuOptions = UniNamespace.ShowShareMenuOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.ShowShareMenuOptions>;
    hideShareMenu<T extends UniNamespace.HideShareMenuOptions = UniNamespace.HideShareMenuOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.HideShareMenuOptions>;
    setBackgroundColor<T extends UniNamespace.SetBackgroundColorOptions = UniNamespace.SetBackgroundColorOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.SetBackgroundColorOptions>;
    setBackgroundTextStyle<T extends UniNamespace.SetBackgroundTextStyleOptions = UniNamespace.SetBackgroundTextStyleOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.SetBackgroundTextStyleOptions>;
    startGyroscope<T extends UniNamespace.StartGyroscopeOptions = UniNamespace.StartGyroscopeOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.StartGyroscopeOptions>;
    stopGyroscope<T extends UniNamespace.StopGyroscopeOptions = UniNamespace.StopGyroscopeOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.StopGyroscopeOptions>;
    loadFontFace<T extends UniNamespace.LoadFontFaceOptions = UniNamespace.LoadFontFaceOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.LoadFontFaceOptions>;
    startSoterAuthentication<T extends UniNamespace.StartSoterAuthenticationOptions = UniNamespace.StartSoterAuthenticationOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.StartSoterAuthenticationOptions>;
    checkIsSupportSoterAuthentication<T extends UniNamespace.CheckIsSupportSoterAuthenticationOptions = UniNamespace.CheckIsSupportSoterAuthenticationOptions>(options?: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.CheckIsSupportSoterAuthenticationOptions>;
    checkIsSoterEnrolledInDevice<T extends UniNamespace.CheckIsSoterEnrolledInDeviceOptions = UniNamespace.CheckIsSoterEnrolledInDeviceOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.CheckIsSoterEnrolledInDeviceOptions>;
    connectSocket<T extends UniNamespace.ConnectSocketOption = UniNamespace.ConnectSocketOption>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.ConnectSocketOption, UniNamespace.SocketTask>;
    request<T extends UniNamespace.RequestOptions = UniNamespace.RequestOptions>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.RequestOptions, UniNamespace.RequestTask>;
    uploadFile<T extends UniNamespace.UploadFileOption = UniNamespace.UploadFileOption>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.UploadFileOption, UniNamespace.UploadTask>;
    downloadFile<T extends UniNamespace.DownloadFileOption = UniNamespace.DownloadFileOption>(options: T): UniNamespace.PromisifySuccessResult<T, UniNamespace.DownloadFileOption, UniNamespace.DownloadTask>;
}
