<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>View</title>
    <link rel="stylesheet" href="app.css" />
    <script>/*__uniConfig*/</script>
    <script>
      var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
        CSS.supports('top: constant(a)'))
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
        (coverSupport ? ', viewport-fit=cover' : '') + '" />')
    </script>
  </head>
  <body>
    <div id="app"></div>
    <script src="uni-app-view.umd.js"></script>
    <!--wxsCode-->
    <!--renderjsCode-->
    <!--automatorCode-->
  </body>
</html>
