export declare function initDefine(stringifyBoolean?: boolean): {
    'process.env.NODE_ENV': string;
    'process.env.UNI_DEBUG': string | boolean;
    'process.env.UNI_APP_ID': string;
    'process.env.UNI_APP_NAME': string;
    'process.env.UNI_APP_VERSION_NAME': string;
    'process.env.UNI_APP_VERSION_CODE': string;
    'process.env.UNI_PLATFORM': string;
    'process.env.UNI_SUB_PLATFORM': string;
    'process.env.UNI_MP_PLUGIN': string;
    'process.env.UNI_SUBPACKAGE': string;
    'process.env.UNI_COMPILER_VERSION': string;
    'process.env.RUN_BY_HBUILDERX': string | boolean;
    'process.env.UNI_AUTOMATOR_WS_ENDPOINT': string;
    'process.env.UNI_AUTOMATOR_APP_WEBVIEW_SRC': string;
    'process.env.UNI_CLOUD_PROVIDER': string;
    'process.env.UNICLOUD_DEBUG': string;
    'process.env.VUE_APP_PLATFORM': string;
    'process.env.VUE_APP_DARK_MODE': string;
};
