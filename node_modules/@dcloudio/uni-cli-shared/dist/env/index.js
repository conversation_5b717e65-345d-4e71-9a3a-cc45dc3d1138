"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initH5Provide = exports.initAppProvide = exports.initDefine = void 0;
var define_1 = require("./define");
Object.defineProperty(exports, "initDefine", { enumerable: true, get: function () { return define_1.initDefine; } });
var provide_1 = require("./provide");
Object.defineProperty(exports, "initAppProvide", { enumerable: true, get: function () { return provide_1.initAppProvide; } });
Object.defineProperty(exports, "initH5Provide", { enumerable: true, get: function () { return provide_1.initH5Provide; } });
