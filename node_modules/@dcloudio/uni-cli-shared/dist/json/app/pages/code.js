"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.globalCode = exports.restoreGlobalCode = exports.polyfillCode = exports.arrayBufferCode = void 0;
exports.arrayBufferCode = `
if (typeof uni !== 'undefined' && uni && uni.requireGlobal) {
  const global = uni.requireGlobal()
  ArrayBuffer = global.ArrayBuffer
  Int8Array = global.Int8Array
  Uint8Array = global.Uint8Array
  Uint8ClampedArray = global.Uint8ClampedArray
  Int16Array = global.Int16Array
  Uint16Array = global.Uint16Array
  Int32Array = global.Int32Array
  Uint32Array = global.Uint32Array
  Float32Array = global.Float32Array
  Float64Array = global.Float64Array
  BigInt64Array = global.BigInt64Array
  BigUint64Array = global.BigUint64Array
};
`;
exports.polyfillCode = `
if (typeof Promise !== 'undefined' && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor
    return this.then(
      value => promise.resolve(callback()).then(() => value),
      reason => promise.resolve(callback()).then(() => {
        throw reason
      })
    )
  }
};
${exports.arrayBufferCode}
`;
exports.restoreGlobalCode = `
if(uni.restoreGlobal){
  uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval)
}
`;
const GLOBALS = [
    'global',
    'window',
    'document',
    'frames',
    'self',
    'location',
    'navigator',
    'localStorage',
    'history',
    'Caches',
    'screen',
    'alert',
    'confirm',
    'prompt',
    'fetch',
    'XMLHttpRequest',
    'WebSocket',
    'webkit',
    'print',
];
exports.globalCode = GLOBALS.map((g) => `${g}:u`).join(',');
