"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.uniOptions = void 0;
const shared_1 = require("@vue/shared");
const uni_shared_1 = require("@dcloudio/uni-shared");
const uni_cli_shared_1 = require("@dcloudio/uni-cli-shared");
const compiler = __importStar(require("@dcloudio/uni-mp-compiler"));
function uniOptions({ copyOptions, miniProgram, customElements, compilerOptions, }) {
    const manifest = (0, uni_cli_shared_1.parseManifestJsonOnce)(process.env.UNI_INPUT_DIR);
    const platformOptions = manifest[process.env.UNI_PLATFORM] || {};
    return {
        copyOptions,
        compiler: compiler,
        compilerOptions: {
            root: process.env.UNI_INPUT_DIR,
            miniProgram: (0, shared_1.extend)({}, miniProgram, {
                component: (0, shared_1.extend)({}, miniProgram.component, {
                    mergeVirtualHostAttributes: platformOptions.mergeVirtualHostAttributes,
                }),
            }),
            isNativeTag: uni_shared_1.isMiniProgramNativeTag,
            isCustomElement: (0, uni_shared_1.createIsCustomElement)(customElements),
            ...compilerOptions,
            nodeTransforms: [
                uni_cli_shared_1.transformPageHead,
                ...(compilerOptions?.nodeTransforms || []),
            ],
        },
    };
}
exports.uniOptions = uniOptions;
