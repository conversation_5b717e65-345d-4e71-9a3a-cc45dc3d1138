"use strict";var t=require("./index.js");const n=new t.Automator;let s,i=!1;try{s=require("jest-environment-node")}catch(t){s=require(require.resolve("jest-environment-node",{paths:[process.cwd()]}))}s&&s.TestEnvironment&&(i=!0,s=s.TestEnvironment);module.exports=class extends s{constructor(t,n){var s,o;super(i?{projectConfig:t}:t,n),process.env.UNI_AUTOMATOR_CONFIG?this.launchOptions=require(process.env.UNI_AUTOMATOR_CONFIG):this.launchOptions=t.testEnvironmentOptions?t.testEnvironmentOptions:t.projectConfig.testEnvironmentOptions,(null===(s=this.launchOptions)||void 0===s?void 0:s.web)&&Object.assign(this.launchOptions,{h5:this.launchOptions.web}),(null===(o=this.launchOptions)||void 0===o?void 0:o.app)&&Object.assign(this.launchOptions,{"app-plus":this.launchOptions.app})}async setup(){await super.setup();const s=global;if(s.__init__){if(!s.program)throw Error("Program init failed")}else s.__init__=!0,this.launchOptions.platform=this.launchOptions.platform||process.env.UNI_PLATFORM,s.program=await n.launch(this.launchOptions),this.launchOptions.devtools&&this.launchOptions.devtools.remote&&await s.program.remote(!0);this.global.program=s.program,this.global.uni=t.initUni(s.program)}async teardown(){await super.teardown()}};
