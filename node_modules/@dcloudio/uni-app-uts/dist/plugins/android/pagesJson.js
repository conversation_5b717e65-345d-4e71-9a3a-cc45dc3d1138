"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.uniAppPagesPlugin = void 0;
const path_1 = __importDefault(require("path"));
const fs_extra_1 = __importDefault(require("fs-extra"));
const uni_cli_shared_1 = require("@dcloudio/uni-cli-shared");
const utils_1 = require("./utils");
const utils_2 = require("../utils");
function uniAppPagesPlugin() {
    const pagesJsonPath = path_1.default.resolve(process.env.UNI_INPUT_DIR, 'pages.json');
    const pagesJsonUTSPath = path_1.default.resolve(process.env.UNI_INPUT_DIR, uni_cli_shared_1.PAGES_JSON_UTS);
    let imports = [];
    let routes = [];
    let globalStyle = 'new Map()';
    let tabBar = 'null';
    let launchPage = 'null';
    let conditionUrl = '';
    let uniIdRouter = 'new Map()';
    let themeConfig = '';
    const codes = [];
    return {
        name: 'uni:app-pages',
        apply: 'build',
        resolveId(id) {
            if ((0, utils_2.isPages)(id)) {
                return pagesJsonUTSPath;
            }
        },
        load(id) {
            if ((0, utils_2.isPages)(id)) {
                return fs_extra_1.default.readFileSync(pagesJsonPath, 'utf8');
            }
        },
        transform(code, id) {
            if ((0, utils_2.isPages)(id)) {
                this.addWatchFile(path_1.default.resolve(process.env.UNI_INPUT_DIR, 'pages.json'));
                this.addWatchFile(path_1.default.resolve(process.env.UNI_INPUT_DIR, 'theme.json'));
                let pagesJson = {
                    pages: [],
                    globalStyle: {
                        navigationBar: {},
                    },
                };
                // 调整换行符，确保 parseTree 的loc正确
                code = code.replace(/\r\n/g, '\n');
                try {
                    pagesJson = (0, uni_cli_shared_1.normalizeUniAppXAppPagesJson)(code);
                }
                catch (err) {
                    if (err.loc) {
                        const error = (0, uni_cli_shared_1.createRollupError)('uni:app-pages', pagesJsonPath, err, code);
                        this.error(error);
                    }
                    else {
                        throw err;
                    }
                }
                imports = [];
                routes = [];
                process.env.UNI_APP_X_PAGE_COUNT = pagesJson.pages.length + '';
                (0, utils_2.setGlobalPageOrientation)(pagesJson.globalStyle?.pageOrientation || '');
                pagesJson.pages.forEach((page, index) => {
                    const className = (0, uni_cli_shared_1.genUTSClassName)(page.path);
                    let isQuit = index === 0;
                    imports.push(page.path);
                    routes.push(`{ path: "${page.path}", component: ${className}Class, meta: { isQuit: ${isQuit} } as UniPageMeta, style: ${stringifyPageStyle(page.style)}${page.needLogin === undefined
                        ? ''
                        : ', needLogin: ' + page.needLogin} } as UniPageRoute`);
                });
                if (pagesJson.globalStyle) {
                    globalStyle = stringifyPageStyle(pagesJson.globalStyle);
                }
                if (pagesJson.tabBar) {
                    tabBar = (0, utils_1.stringifyMap)(pagesJson.tabBar);
                }
                if (pagesJson.condition) {
                    const conditionInfo = (0, uni_cli_shared_1.parseArguments)(pagesJson);
                    if (conditionInfo) {
                        const { path, query } = JSON.parse(conditionInfo);
                        conditionUrl = `${path}${query ? '?' + query : ''}`;
                    }
                }
                if (pagesJson.uniIdRouter) {
                    uniIdRouter = (0, utils_1.stringifyMap)(pagesJson.uniIdRouter);
                }
                launchPage = stringifyLaunchPage(pagesJson.pages[0]);
                codes.length = 0;
                // theme.json
                themeConfig = readThemeJSONFileAsStringifyMap();
                if (themeConfig) {
                    codes.push(`__uniConfig.themeConfig = ${themeConfig}`);
                }
                return {
                    code: `${imports
                        .map((p) => `import './${p}.uvue?type=page'`)
                        .join('\n')}
          export default 'pages.json'`,
                    map: null,
                };
            }
        },
        generateBundle(_, bundle) {
            if (bundle[(0, utils_1.ENTRY_FILENAME)()]) {
                const asset = bundle[(0, utils_1.ENTRY_FILENAME)()];
                asset.source =
                    asset.source +
                        `
${imports
                            .map((p) => {
                            const className = (0, uni_cli_shared_1.genUTSClassName)(p);
                            return `import ${className}Class from './${p}.uvue?type=page'`;
                        })
                            .join('\n')}
function definePageRoutes() {
${routes.map((route) => `__uniRoutes.push(${route})`).join('\n')}
}
const __uniTabBar: Map<string, any | null> | null = ${tabBar}
const __uniLaunchPage: Map<string, any | null> = ${launchPage}
function defineAppConfig(){
  __uniConfig.entryPagePath = '/${imports[0]}'
  __uniConfig.globalStyle = ${globalStyle}
  __uniConfig.tabBar = __uniTabBar as Map<string, any> | null
  __uniConfig.conditionUrl = '${conditionUrl}'
  __uniConfig.uniIdRouter = ${uniIdRouter}
  ${codes.join('\n  ')}
  __uniConfig.ready = true
}
`;
            }
        },
    };
}
exports.uniAppPagesPlugin = uniAppPagesPlugin;
function stringifyLaunchPage(launchPage) {
    return (0, utils_1.stringifyMap)({
        url: launchPage.path,
        style: launchPage.style,
    }, true);
}
function stringifyPageStyle(pageStyle) {
    return (0, utils_1.stringifyMap)(pageStyle);
}
// function readUniSassAsStringifyMap() {
//   const uniScssPath = path.resolve(process.env.UNI_INPUT_DIR, 'uni.scss')
//   let result = {}
//   if (fs.existsSync(uniScssPath)) {
//     const content = fs.readFileSync(uniScssPath, 'utf8')
//     const parser = new ThemeSassParser()
//     result = parser.parse(content)
//   }
//   return stringifyMap(result)
// }
function readThemeJSONFileAsStringifyMap() {
    const themeJsonPath = path_1.default.resolve(process.env.UNI_INPUT_DIR, 'theme.json');
    if (fs_extra_1.default.existsSync(themeJsonPath)) {
        return (0, utils_1.stringifyMap)(JSON.parse(fs_extra_1.default.readFileSync(themeJsonPath, 'utf8')));
    }
    return '';
}
