"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.transformMain = void 0;
const path_1 = __importDefault(require("path"));
const source_map_js_1 = require("source-map-js");
const trace_mapping_1 = require("@jridgewell/trace-mapping");
const gen_mapping_1 = require("@jridgewell/gen-mapping");
const uni_cli_shared_1 = require("@dcloudio/uni-cli-shared");
const descriptorCache_1 = require("../descriptorCache");
const script_1 = require("./script");
const utils_1 = require("../../utils");
const script_2 = require("../code/script");
const normalScript_1 = require("./compiler/script/normalScript");
async function transformMain(code, filename, options, pluginContext // 该 transformMain 方法被vuejs-core使用，编译框架内置组件了，此时不会传入pluginContext
) {
    if (!options.compiler) {
        options.compiler = require('@vue/compiler-sfc');
    }
    const { descriptor, errors } = (0, descriptorCache_1.createDescriptor)(filename, code, options);
    const relativeFilename = descriptor.relativeFilename;
    let easyComInstance = '';
    if (options.genDefaultAs) {
        const imports = (0, uni_cli_shared_1.getUTSEasyComAutoImports)()['@/' + relativeFilename];
        if (imports && imports.length === 1) {
            easyComInstance = imports[0][1];
        }
    }
    if (errors.length) {
        if (pluginContext) {
            errors.forEach((error) => pluginContext.error((0, uni_cli_shared_1.createRollupError)('', filename, error, code)));
        }
        return null;
    }
    const className = (0, uni_cli_shared_1.genUTSClassName)(relativeFilename, options.classNamePrefix);
    // script
    const scriptOptions = {
        ...options,
        className,
    };
    let { code: scriptCode, map: scriptMap, bindingMetadata, } = await genScriptCode(descriptor, scriptOptions);
    let templatePreambleCode = '';
    let templateCode = '';
    let templateMap = undefined;
    if (options.componentType !== 'app') {
        // template
        const isInline = !!descriptor.scriptSetup;
        if (!isInline) {
            const { code, map, preamble } = (0, normalScript_1.processTemplate)(descriptor, {
                relativeFilename,
                bindingMetadata: bindingMetadata,
                rootDir: options.root,
                className,
            });
            templateCode = code;
            templateMap = map;
            templatePreambleCode = preamble || '';
        }
    }
    // styles
    const stylesCode = await genStyleCode(descriptor, pluginContext);
    const utsOutput = [
        scriptCode || (0, script_2.genDefaultScriptCode)(options.genDefaultAs),
        templateCode,
        easyComInstance
            ? `export type ${easyComInstance} = InstanceType<typeof __sfc__>;`
            : '',
        `/*${className}Styles*/\n`,
    ];
    if (templatePreambleCode) {
        utsOutput.push(templatePreambleCode);
    }
    let resolvedMap = undefined;
    if (options.sourceMap) {
        if (scriptMap && templateMap) {
            // if the template is inlined into the main module (indicated by the presence
            // of templateMap), we need to concatenate the two source maps.
            const gen = (0, gen_mapping_1.fromMap)(
            // version property of result.map is declared as string
            // but actually it is `3`
            scriptMap);
            const tracer = new trace_mapping_1.TraceMap(
            // same above
            templateMap);
            const offset = (scriptCode.match(/\r?\n/g)?.length ?? 0) + 1;
            (0, trace_mapping_1.eachMapping)(tracer, (m) => {
                if (m.source == null)
                    return;
                (0, gen_mapping_1.addMapping)(gen, {
                    source: m.source,
                    original: { line: m.originalLine, column: m.originalColumn },
                    generated: {
                        line: m.generatedLine + offset,
                        column: m.generatedColumn,
                    },
                });
            });
            // same above
            resolvedMap = (0, gen_mapping_1.toEncodedMap)(gen);
            // if this is a template only update, we will be reusing a cached version
            // of the main module compile result, which has outdated sourcesContent.
            // resolvedMap.sourcesContent = templateMap.sourcesContent
        }
        else {
            // if one of `scriptMap` and `templateMap` is empty, use the other one
            resolvedMap = scriptMap ?? templateMap;
        }
    }
    // handle TS transpilation
    let utsCode = utsOutput.filter(Boolean).join('\n');
    const jsCodes = [templatePreambleCode];
    // 处理自动导入(主要是easyCom的组件类型)
    const { matchedImports } = await (0, utils_1.detectAutoImports)(utsCode, descriptor.filename, easyComInstance ? [easyComInstance] : []);
    if (matchedImports.length) {
        const autoImportCode = (0, utils_1.genAutoImportsCode)(matchedImports);
        if (autoImportCode) {
            utsCode += '\n' + autoImportCode;
            // 给 script 增加自动导入，让下边的 jsCode 可以 parse 到
            scriptCode += '\n' + autoImportCode;
        }
    }
    if (resolvedMap && pluginContext) {
        pluginContext.emitFile({
            type: 'asset',
            fileName: (0, uni_cli_shared_1.normalizeEmitAssetFileName)(relativeFilename) + '.map',
            source: JSON.stringify(resolvedMap),
        });
        if (process.env.UNI_APP_X_TSC !== 'true') {
            utsCode += `
//# sourceMappingURL=${path_1.default.basename((0, uni_cli_shared_1.normalizeEmitAssetFileName)(relativeFilename))}.map`;
        }
    }
    if (scriptCode) {
        jsCodes.push(await (0, utils_1.parseImports)(scriptCode, resolvedMap && pluginContext
            ? createTryResolve(filename, pluginContext.resolve, resolvedMap, 
            // 仅需要再解析script中的import，template上边已经加入了
            (source) => source.includes('/.uvue/') || source.includes('/.tsc/'))
            : undefined));
        pluginContext?.emitFile({
            type: 'asset',
            fileName: (0, uni_cli_shared_1.normalizeEmitAssetFileName)(relativeFilename),
            source: utsCode,
        });
    }
    if (stylesCode) {
        jsCodes.push(stylesCode);
    }
    jsCodes.push(`export default "${className}"
${easyComInstance ? `export const ${easyComInstance} = {}` : ''}`);
    let jsCode = jsCodes.filter(Boolean).join('\n');
    return {
        code: processJsCodeImport(jsCode),
        map: {
            mappings: '',
        },
        // 这些都是 vuejs-core 需要的
        errors,
        uts: utsCode,
        descriptor,
    };
}
exports.transformMain = transformMain;
function processJsCodeImport(jsCode) {
    if (jsCode.includes('@/node-modules/@dcloudio/uni-components/lib-x')) {
        return jsCode.replaceAll('@/node-modules/@dcloudio/uni-components/lib-x', (0, uni_cli_shared_1.normalizePath)((0, uni_cli_shared_1.resolveComponentsLibPath)()));
    }
    return jsCode;
}
async function genScriptCode(descriptor, options) {
    let scriptCode = (0, script_2.genDefaultScriptCode)(options.genDefaultAs);
    let map;
    const script = (0, script_1.resolveScript)(descriptor, options);
    if (script) {
        scriptCode = script.content;
        map = script.map;
    }
    return {
        code: scriptCode,
        map,
        bindingMetadata: script?.bindings,
    };
}
async function genStyleCode(descriptor, pluginContext) {
    let stylesCode = ``;
    if (descriptor.styles.length) {
        for (let i = 0; i < descriptor.styles.length; i++) {
            const style = descriptor.styles[i];
            if (style.src && pluginContext) {
                await linkSrcToDescriptor(style.src, descriptor, pluginContext);
            }
            const src = style.src || descriptor.filename;
            // do not include module in default query, since we use it to indicate
            // that the module needs to export the modules json
            const attrsQuery = attrsToQuery(style.attrs, 'css');
            style.scoped = false; // fixed by xxxxxx 强制不scoped
            const srcQuery = style.src
                ? style.scoped
                    ? `&src=${descriptor.id}`
                    : '&src=true'
                : '';
            const directQuery = ``;
            const scopedQuery = style.scoped ? `&scoped=${descriptor.id}` : ``;
            const query = `?vue&type=style&index=${i}${srcQuery}${directQuery}${scopedQuery}`;
            const styleRequest = src + query + attrsQuery;
            stylesCode += `\nimport ${JSON.stringify(styleRequest)}`;
        }
        // TODO SSR critical CSS collection
    }
    return stylesCode;
}
/**
 * For blocks with src imports, it is important to link the imported file
 * with its owner SFC descriptor so that we can get the information about
 * the owner SFC when compiling that file in the transform phase.
 */
async function linkSrcToDescriptor(src, descriptor, pluginContext) {
    const srcFile = (await pluginContext.resolve(src, descriptor.filename))?.id || src;
    // #1812 if the src points to a dep file, the resolved id may contain a
    // version query.
    (0, descriptorCache_1.setSrcDescriptor)(srcFile.replace(/\?.*$/, ''), descriptor);
}
// these are built-in query parameters so should be ignored
// if the user happen to add them as attrs
const ignoreList = [
    'id',
    'index',
    'src',
    'type',
    'lang',
    'module',
    'scoped',
    'generic',
];
function attrsToQuery(attrs, langFallback, forceLangFallback = false) {
    let query = ``;
    for (const name in attrs) {
        const value = attrs[name];
        if (!ignoreList.includes(name)) {
            query += `&${encodeURIComponent(name)}${value ? `=${encodeURIComponent(value)}` : ``}`;
        }
    }
    if (langFallback || attrs.lang) {
        query +=
            `lang` in attrs
                ? forceLangFallback
                    ? `&lang.${langFallback}`
                    : `&lang.${attrs.lang}`
                : `&lang.${langFallback}`;
    }
    return query;
}
function createTryResolve(importer, resolve, resolvedMap, ignore) {
    return async (source, code, { ss, se }) => {
        if (ignore && ignore(source)) {
            return false;
        }
        const resolved = await (0, utils_1.wrapResolve)(resolve)(source, importer);
        if (!resolved) {
            const { start, end } = (0, uni_cli_shared_1.offsetToStartAndEnd)(code, ss, se);
            const consumer = new source_map_js_1.SourceMapConsumer(resolvedMap);
            const startPos = consumer.originalPositionFor({
                line: start.line,
                column: start.column,
            });
            if (startPos.line != null &&
                startPos.column != null &&
                startPos.source != null) {
                const endPos = consumer.originalPositionFor({
                    line: end.line,
                    column: end.column,
                });
                if (endPos.line != null && endPos.column != null) {
                    startPos.column = startPos.column + 1;
                    endPos.column = endPos.column + 1;
                    throw (0, utils_1.createResolveError)(consumer.sourceContentFor(startPos.source), (0, uni_cli_shared_1.createResolveErrorMsg)(source, importer), startPos, endPos);
                }
            }
        }
    };
}
