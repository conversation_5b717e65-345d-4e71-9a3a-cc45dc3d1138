"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.genModelProps = exports.processDefineModel = exports.DEFINE_MODEL = void 0;
const resolveType_1 = require("./resolveType");
const utils_1 = require("./utils");
const compiler_dom_1 = require("@vue/compiler-dom");
// import { warnOnce } from '../warn'
exports.DEFINE_MODEL = 'defineModel';
function processDefineModel(ctx, node, declId) {
    if (!(0, utils_1.isCallOf)(node, exports.DEFINE_MODEL)) {
        return false;
    }
    // if (!ctx.options.defineModel) {
    //   warnOnce(
    //     `defineModel() is an experimental feature and disabled by default.\n` +
    //       `To enable it, follow the RFC at https://github.com/vuejs/rfcs/discussions/503.`
    //   )
    //   return false
    // }
    // warnOnce(
    //   `This project is using defineModel(), which is an experimental ` +
    //     `feature. It may receive breaking changes or be removed in the future, so ` +
    //     `use at your own risk.\n` +
    //     `To stay updated, follow the RFC at https://github.com/vuejs/rfcs/discussions/503.`
    // )
    ctx.hasDefineModelCall = true;
    const type = (node.typeParameters && node.typeParameters.params[0]) || undefined;
    let modelName;
    let options;
    const arg0 = node.arguments[0] && (0, utils_1.unwrapTSNode)(node.arguments[0]);
    if (arg0 && arg0.type === 'StringLiteral') {
        modelName = arg0.value;
        options = node.arguments[1];
    }
    else {
        modelName = 'modelValue';
        options = arg0;
    }
    if (ctx.modelDecls[modelName]) {
        ctx.error(`duplicate model name ${JSON.stringify(modelName)}`, node);
    }
    if (options) {
        if (options.type !== 'ObjectExpression') {
            ctx.error(`options must be an object expression`, options);
        }
        if (options.properties.find((p) => p.type === 'SpreadElement')) {
            ctx.error(`options does not support spread properties.`, options);
        }
    }
    const optionsString = options && ctx.getString(options);
    ctx.modelDecls[modelName] = {
        type,
        options: optionsString,
        identifier: declId && declId.type === 'Identifier' ? declId.name : undefined,
    };
    // register binding type
    ctx.bindingMetadata[modelName] = compiler_dom_1.BindingTypes.PROPS;
    const runtimeTypes = type && (0, resolveType_1.inferRuntimeType)(ctx, type, 'defineModel');
    let runtimeType = runtimeTypes && runtimeTypes.length === 1 ? runtimeTypes[0] : undefined;
    let runtimeOptions = '';
    if (options) {
        if (options.type === 'ObjectExpression') {
            if (!runtimeType) {
                // 未指定泛型，但指定了 options 中的 type
                const type = options.properties.find((p) => p.type === 'ObjectProperty' &&
                    ((p.key.type === 'Identifier' && p.key.name === 'type') ||
                        (p.key.type === 'StringLiteral' && p.key.value === 'type')));
                if (type) {
                    runtimeType = ctx.getString(type.value);
                }
            }
            const local = options.properties.find((p) => p.type === 'ObjectProperty' &&
                ((p.key.type === 'Identifier' && p.key.name === 'local') ||
                    (p.key.type === 'StringLiteral' && p.key.value === 'local')));
            if (local) {
                runtimeOptions = `{ ${ctx.getString(local)} }`;
            }
            else {
                for (const p of options.properties) {
                    if (p.type === 'SpreadElement' || p.computed) {
                        runtimeOptions = optionsString;
                        break;
                    }
                }
            }
        }
        else {
            runtimeOptions = optionsString;
        }
    }
    ctx.s.overwrite(ctx.startOffset + node.start, ctx.startOffset + node.end, `${ctx.helper('useModel')}<${runtimeType || 'any'}>(__ins.props, ${JSON.stringify(modelName)}${runtimeOptions ? `, ${runtimeOptions}` : ``})`);
    return true;
}
exports.processDefineModel = processDefineModel;
function genModelProps(ctx) {
    if (!ctx.hasDefineModelCall)
        return;
    const isProd = !!ctx.options.isProd;
    let modelPropsDecl = '';
    for (const [name, { type, options }] of Object.entries(ctx.modelDecls)) {
        let skipCheck = false;
        let runtimeTypes = type && (0, resolveType_1.inferRuntimeType)(ctx, type, 'defineModel');
        if (runtimeTypes) {
            const hasUnknownType = runtimeTypes.includes(utils_1.UNKNOWN_TYPE);
            runtimeTypes = runtimeTypes.filter((el) => {
                if (el === utils_1.UNKNOWN_TYPE)
                    return false;
                return isProd
                    ? el === 'Boolean' || (el === 'Function' && options)
                    : true;
            });
            skipCheck = !isProd && hasUnknownType && runtimeTypes.length > 0;
        }
        let runtimeType = (runtimeTypes &&
            runtimeTypes.length > 0 &&
            (0, utils_1.toRuntimeTypeString)(runtimeTypes)) ||
            undefined;
        const codegenOptions = (0, utils_1.concatStrings)([
            runtimeType && `type: ${runtimeType}`,
            skipCheck && 'skipCheck: true',
        ]);
        let decl;
        if (runtimeType && options) {
            decl = `{ ${codegenOptions}, ...${options} }`;
        }
        else {
            decl = options || (runtimeType ? `{ ${codegenOptions} }` : '{}');
        }
        modelPropsDecl += `\n    ${JSON.stringify(name)}: ${decl},`;
    }
    return `{${modelPropsDecl}\n  }`;
}
exports.genModelProps = genModelProps;
