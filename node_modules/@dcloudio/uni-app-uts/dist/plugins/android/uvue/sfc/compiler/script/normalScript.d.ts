/// <reference types="source-map-js" />
import type { BindingMetadata, SFCDescriptor } from '@vue/compiler-sfc';
import type { ScriptCompileContext } from './context';
export declare function processNormalScript(ctx: ScriptCompileContext, _scopeId: string): import("@vue/compiler-sfc").SFCScriptBlock;
export declare function processTemplate(sfc: SFCDescriptor, { relativeFilename, bindingMetadata, className, rootDir, }: {
    relativeFilename: string;
    bindingMetadata?: BindingMetadata;
    className: string;
    rootDir: string;
}): {
    code: string;
    map: import("source-map-js").RawSourceMap | undefined;
    preamble: string | undefined;
};
