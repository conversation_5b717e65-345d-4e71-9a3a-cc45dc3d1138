"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.uniAppUVuePlugin = void 0;
const fs_extra_1 = __importDefault(require("fs-extra"));
const shared_1 = require("@vue/shared");
const uni_cli_shared_1 = require("@dcloudio/uni-cli-shared");
const descriptorCache_1 = require("./descriptorCache");
const utils_1 = require("../utils");
const style_1 = require("./code/style");
const main_1 = require("./sfc/main");
function uniAppUVuePlugin() {
    const options = (0, descriptorCache_1.getResolvedOptions)();
    const appVue = (0, uni_cli_shared_1.resolveAppVue)(process.env.UNI_INPUT_DIR);
    function isAppVue(id) {
        return (0, uni_cli_shared_1.normalizePath)(id) === appVue;
    }
    return {
        name: 'uni:app-uvue',
        apply: 'build',
        async resolveId(id) {
            // serve sub-part requests (*?vue) as virtual modules
            if ((0, uni_cli_shared_1.parseVueRequest)(id).query.vue) {
                return id;
            }
        },
        load(id) {
            const { filename, query } = (0, uni_cli_shared_1.parseVueRequest)(id);
            // select corresponding block for sub-part virtual modules
            if (query.vue) {
                if (query.src) {
                    return fs_extra_1.default.readFileSync(filename, 'utf-8');
                }
                const descriptor = (0, descriptorCache_1.getDescriptor)(filename, options);
                let block;
                if (query.type === 'style') {
                    block = descriptor.styles[query.index];
                }
                else if (query.index != null) {
                    block = descriptor.customBlocks[query.index];
                }
                if (block) {
                    return {
                        code: block.content,
                        map: block.map,
                    };
                }
            }
        },
        async transform(code, id) {
            const { filename, query } = (0, uni_cli_shared_1.parseVueRequest)(id);
            if (!(0, utils_1.isVue)(filename)) {
                return;
            }
            if (!query.vue) {
                // main request
                return (0, main_1.transformMain)((0, utils_1.transformUniCloudMixinDataCom)(code), filename, {
                    ...options,
                    componentType: isAppVue(filename)
                        ? 'app'
                        : query.type === 'page'
                            ? 'page'
                            : 'component',
                }, this);
            }
            else {
                // sub block request
                const descriptor = query.src
                    ? (0, descriptorCache_1.getSrcDescriptor)(filename)
                    : (0, descriptorCache_1.getDescriptor)(filename, options);
                if (query.type === 'style') {
                    return (0, style_1.transformStyle)(code, descriptor, Number(query.index), options, this, filename);
                }
            }
        },
        generateBundle(_, bundle) {
            // 遍历vue文件，填充style，尽量减少全局变量
            Object.keys(bundle).forEach((name) => {
                const file = bundle[name];
                if (file &&
                    file.type === 'asset' &&
                    isVueFile(file.fileName) &&
                    (0, shared_1.isString)(file.source)) {
                    let fileName = (0, uni_cli_shared_1.normalizePath)(file.fileName);
                    if (process.env.UNI_APP_X_TSC === 'true') {
                        fileName = fileName.replace('.ts', '');
                    }
                    const classNameComment = `/*${(0, uni_cli_shared_1.genUTSClassName)(fileName, options.classNamePrefix)}Styles*/`;
                    if (file.source.includes(classNameComment)) {
                        const styleAssetName = fileName + '.style.uts';
                        const styleAsset = bundle[styleAssetName];
                        if (styleAsset &&
                            styleAsset.type === 'asset' &&
                            (0, shared_1.isString)(styleAsset.source)) {
                            file.source = file.source.replace(classNameComment, styleAsset.source.replace('export ', ''));
                            delete bundle[styleAssetName];
                        }
                    }
                }
            });
        },
    };
}
exports.uniAppUVuePlugin = uniAppUVuePlugin;
function isVueFile(filename) {
    return (filename.endsWith('.uvue') ||
        filename.endsWith('.vue') ||
        filename.endsWith('.uvue.ts') ||
        filename.endsWith('.vue.ts'));
}
