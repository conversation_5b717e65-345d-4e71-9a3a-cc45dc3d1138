"use strict";Object.defineProperty(exports,"__esModule",{value:!0});"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self&&self;function e(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function t(e,t,n){return e(n={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&n.path)}},n.exports),n.exports}var n=t((function(e,t){var n;e.exports=(n=n||function(e,t){var n=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),s={},r=s.lib={},i=r.Base={extend:function(e){var t=n(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},o=r.WordArray=i.extend({init:function(e,n){e=this.words=e||[],this.sigBytes=n!=t?n:4*e.length},toString:function(e){return(e||c).stringify(this)},concat:function(e){var t=this.words,n=e.words,s=this.sigBytes,r=e.sigBytes;if(this.clamp(),s%4)for(var i=0;i<r;i++){var o=n[i>>>2]>>>24-i%4*8&255;t[s+i>>>2]|=o<<24-(s+i)%4*8}else for(i=0;i<r;i+=4)t[s+i>>>2]=n[i>>>2];return this.sigBytes+=r,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=i.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var n,s=[],r=function(t){t=t;var n=987654321,s=4294967295;return function(){var r=((n=36969*(65535&n)+(n>>16)&s)<<16)+(t=18e3*(65535&t)+(t>>16)&s)&s;return r/=4294967296,(r+=.5)*(e.random()>.5?1:-1)}},i=0;i<t;i+=4){var a=r(4294967296*(n||e.random()));n=987654071*a(),s.push(4294967296*a()|0)}return new o.init(s,t)}}),a=s.enc={},c=a.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,s=[],r=0;r<n;r++){var i=t[r>>>2]>>>24-r%4*8&255;s.push((i>>>4).toString(16)),s.push((15&i).toString(16))}return s.join("")},parse:function(e){for(var t=e.length,n=[],s=0;s<t;s+=2)n[s>>>3]|=parseInt(e.substr(s,2),16)<<24-s%8*4;return new o.init(n,t/2)}},u=a.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,s=[],r=0;r<n;r++){var i=t[r>>>2]>>>24-r%4*8&255;s.push(String.fromCharCode(i))}return s.join("")},parse:function(e){for(var t=e.length,n=[],s=0;s<t;s++)n[s>>>2]|=(255&e.charCodeAt(s))<<24-s%4*8;return new o.init(n,t)}},l=a.Utf8={stringify:function(e){try{return decodeURIComponent(escape(u.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return u.parse(unescape(encodeURIComponent(e)))}},h=r.BufferedBlockAlgorithm=i.extend({reset:function(){this._data=new o.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=l.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n=this._data,s=n.words,r=n.sigBytes,i=this.blockSize,a=r/(4*i),c=(a=t?e.ceil(a):e.max((0|a)-this._minBufferSize,0))*i,u=e.min(4*c,r);if(c){for(var l=0;l<c;l+=i)this._doProcessBlock(s,l);var h=s.splice(0,c);n.sigBytes-=u}return new o.init(h,u)},clone:function(){var e=i.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});r.Hasher=h.extend({cfg:i.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){h.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new d.HMAC.init(e,n).finalize(t)}}});var d=s.algo={};return s}(Math),n)})),s=n,r=(t((function(e,t){var n;e.exports=(n=s,function(e){var t=n,s=t.lib,r=s.WordArray,i=s.Hasher,o=t.algo,a=[];!function(){for(var t=0;t<64;t++)a[t]=4294967296*e.abs(e.sin(t+1))|0}();var c=o.MD5=i.extend({_doReset:function(){this._hash=new r.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var s=t+n,r=e[s];e[s]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8)}var i=this._hash.words,o=e[t+0],c=e[t+1],p=e[t+2],f=e[t+3],g=e[t+4],m=e[t+5],y=e[t+6],_=e[t+7],w=e[t+8],v=e[t+9],I=e[t+10],S=e[t+11],b=e[t+12],k=e[t+13],A=e[t+14],P=e[t+15],C=i[0],T=i[1],x=i[2],O=i[3];C=u(C,T,x,O,o,7,a[0]),O=u(O,C,T,x,c,12,a[1]),x=u(x,O,C,T,p,17,a[2]),T=u(T,x,O,C,f,22,a[3]),C=u(C,T,x,O,g,7,a[4]),O=u(O,C,T,x,m,12,a[5]),x=u(x,O,C,T,y,17,a[6]),T=u(T,x,O,C,_,22,a[7]),C=u(C,T,x,O,w,7,a[8]),O=u(O,C,T,x,v,12,a[9]),x=u(x,O,C,T,I,17,a[10]),T=u(T,x,O,C,S,22,a[11]),C=u(C,T,x,O,b,7,a[12]),O=u(O,C,T,x,k,12,a[13]),x=u(x,O,C,T,A,17,a[14]),C=l(C,T=u(T,x,O,C,P,22,a[15]),x,O,c,5,a[16]),O=l(O,C,T,x,y,9,a[17]),x=l(x,O,C,T,S,14,a[18]),T=l(T,x,O,C,o,20,a[19]),C=l(C,T,x,O,m,5,a[20]),O=l(O,C,T,x,I,9,a[21]),x=l(x,O,C,T,P,14,a[22]),T=l(T,x,O,C,g,20,a[23]),C=l(C,T,x,O,v,5,a[24]),O=l(O,C,T,x,A,9,a[25]),x=l(x,O,C,T,f,14,a[26]),T=l(T,x,O,C,w,20,a[27]),C=l(C,T,x,O,k,5,a[28]),O=l(O,C,T,x,p,9,a[29]),x=l(x,O,C,T,_,14,a[30]),C=h(C,T=l(T,x,O,C,b,20,a[31]),x,O,m,4,a[32]),O=h(O,C,T,x,w,11,a[33]),x=h(x,O,C,T,S,16,a[34]),T=h(T,x,O,C,A,23,a[35]),C=h(C,T,x,O,c,4,a[36]),O=h(O,C,T,x,g,11,a[37]),x=h(x,O,C,T,_,16,a[38]),T=h(T,x,O,C,I,23,a[39]),C=h(C,T,x,O,k,4,a[40]),O=h(O,C,T,x,o,11,a[41]),x=h(x,O,C,T,f,16,a[42]),T=h(T,x,O,C,y,23,a[43]),C=h(C,T,x,O,v,4,a[44]),O=h(O,C,T,x,b,11,a[45]),x=h(x,O,C,T,P,16,a[46]),C=d(C,T=h(T,x,O,C,p,23,a[47]),x,O,o,6,a[48]),O=d(O,C,T,x,_,10,a[49]),x=d(x,O,C,T,A,15,a[50]),T=d(T,x,O,C,m,21,a[51]),C=d(C,T,x,O,b,6,a[52]),O=d(O,C,T,x,f,10,a[53]),x=d(x,O,C,T,I,15,a[54]),T=d(T,x,O,C,c,21,a[55]),C=d(C,T,x,O,w,6,a[56]),O=d(O,C,T,x,P,10,a[57]),x=d(x,O,C,T,y,15,a[58]),T=d(T,x,O,C,k,21,a[59]),C=d(C,T,x,O,g,6,a[60]),O=d(O,C,T,x,S,10,a[61]),x=d(x,O,C,T,p,15,a[62]),T=d(T,x,O,C,v,21,a[63]),i[0]=i[0]+C|0,i[1]=i[1]+T|0,i[2]=i[2]+x|0,i[3]=i[3]+O|0},_doFinalize:function(){var t=this._data,n=t.words,s=8*this._nDataBytes,r=8*t.sigBytes;n[r>>>5]|=128<<24-r%32;var i=e.floor(s/4294967296),o=s;n[15+(r+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),n[14+(r+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),t.sigBytes=4*(n.length+1),this._process();for(var a=this._hash,c=a.words,u=0;u<4;u++){var l=c[u];c[u]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}return a},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});function u(e,t,n,s,r,i,o){var a=e+(t&n|~t&s)+r+o;return(a<<i|a>>>32-i)+t}function l(e,t,n,s,r,i,o){var a=e+(t&s|n&~s)+r+o;return(a<<i|a>>>32-i)+t}function h(e,t,n,s,r,i,o){var a=e+(t^n^s)+r+o;return(a<<i|a>>>32-i)+t}function d(e,t,n,s,r,i,o){var a=e+(n^(t|~s))+r+o;return(a<<i|a>>>32-i)+t}t.MD5=i._createHelper(c),t.HmacMD5=i._createHmacHelper(c)}(Math),n.MD5)})),t((function(e,t){var n;e.exports=(n=s,void function(){var e=n,t=e.lib.Base,s=e.enc.Utf8;e.algo.HMAC=t.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=s.parse(t));var n=e.blockSize,r=4*n;t.sigBytes>r&&(t=e.finalize(t)),t.clamp();for(var i=this._oKey=t.clone(),o=this._iKey=t.clone(),a=i.words,c=o.words,u=0;u<n;u++)a[u]^=1549556828,c[u]^=909522486;i.sigBytes=o.sigBytes=r,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}})}())})),t((function(e,t){e.exports=s.HmacMD5}))),i=t((function(e,t){e.exports=s.enc.Utf8})),o=t((function(e,t){var n;e.exports=(n=s,function(){var e=n,t=e.lib.WordArray;function s(e,n,s){for(var r=[],i=0,o=0;o<n;o++)if(o%4){var a=s[e.charCodeAt(o-1)]<<o%4*2,c=s[e.charCodeAt(o)]>>>6-o%4*2;r[i>>>2]|=(a|c)<<24-i%4*8,i++}return t.create(r,i)}e.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,s=this._map;e.clamp();for(var r=[],i=0;i<n;i+=3)for(var o=(t[i>>>2]>>>24-i%4*8&255)<<16|(t[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|t[i+2>>>2]>>>24-(i+2)%4*8&255,a=0;a<4&&i+.75*a<n;a++)r.push(s.charAt(o>>>6*(3-a)&63));var c=s.charAt(64);if(c)for(;r.length%4;)r.push(c);return r.join("")},parse:function(e){var t=e.length,n=this._map,r=this._reverseMap;if(!r){r=this._reverseMap=[];for(var i=0;i<n.length;i++)r[n.charCodeAt(i)]=i}var o=n.charAt(64);if(o){var a=e.indexOf(o);-1!==a&&(t=a)}return s(e,t,r)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),n.enc.Base64)}));const a="FUNCTION",c="OBJECT",u="CLIENT_DB",l="pending",h="fulfilled",d="rejected";function p(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function f(e){return"object"===p(e)}function g(e){return"function"==typeof e}function m(e){return function(){try{return e.apply(e,arguments)}catch(e){console.error(e)}}}const y="REJECTED",_="NOT_PENDING";class w{constructor({createPromise:e,retryRule:t=y}={}){this.createPromise=e,this.status=null,this.promise=null,this.retryRule=t}get needRetry(){if(!this.status)return!0;switch(this.retryRule){case y:return this.status===d;case _:return this.status!==l}}exec(){return this.needRetry?(this.status=l,this.promise=this.createPromise().then((e=>(this.status=h,Promise.resolve(e))),(e=>(this.status=d,Promise.reject(e)))),this.promise):this.promise}}function v(e){return e&&"string"==typeof e?JSON.parse(e):e}const I="development"===process.env.NODE_ENV,S=process.env.UNI_PLATFORM,b="true"===process.env.UNI_SECURE_NETWORK_ENABLE||!0===process.env.UNI_SECURE_NETWORK_ENABLE,k=v(process.env.UNI_SECURE_NETWORK_CONFIG),A="h5"===S?"web":"app-plus"===S?"app":S,P=v(process.env.UNICLOUD_DEBUG),C=v(process.env.UNI_CLOUD_PROVIDER)||[],T=process.env.RUN_BY_HBUILDERX;let x="";try{x=process.env.UNI_APP_ID||""}catch(e){}let O={};function E(e,t={}){var n,s;return n=O,s=e,Object.prototype.hasOwnProperty.call(n,s)||(O[e]=t),O[e]}const L=["invoke","success","fail","complete"],R=E("_globalUniCloudInterceptor");function U(e,t){R[e]||(R[e]={}),f(t)&&Object.keys(t).forEach((n=>{L.indexOf(n)>-1&&function(e,t,n){let s=R[e][t];s||(s=R[e][t]=[]),-1===s.indexOf(n)&&g(n)&&s.push(n)}(e,n,t[n])}))}function N(e,t){R[e]||(R[e]={}),f(t)?Object.keys(t).forEach((n=>{L.indexOf(n)>-1&&function(e,t,n){const s=R[e][t];if(!s)return;const r=s.indexOf(n);r>-1&&s.splice(r,1)}(e,n,t[n])})):delete R[e]}function D(e,t){return e&&0!==e.length?e.reduce(((e,n)=>e.then((()=>n(t)))),Promise.resolve()):Promise.resolve()}function M(e,t){return R[e]&&R[e][t]||[]}function q(e){U("callObject",e)}const F=E("_globalUniCloudListener"),K="response",j="needLogin",$="refreshToken",B="clientdb",W="cloudfunction",H="cloudobject";function J(e){return F[e]||(F[e]=[]),F[e]}function z(e,t){const n=J(e);n.includes(t)||n.push(t)}function V(e,t){const n=J(e),s=n.indexOf(t);-1!==s&&n.splice(s,1)}function G(e,t){const n=J(e);for(let e=0;e<n.length;e++){(0,n[e])(t)}}let Y,Q=!1;function X(){return Y||(Y=new Promise((e=>{Q&&e(),function t(){if("function"==typeof getCurrentPages){const t=getCurrentPages();t&&t[0]&&(Q=!0,e())}Q||setTimeout((()=>{t()}),30)}()})),Y)}function Z(e){const t={};for(const n in e){const s=e[n];g(s)&&(t[n]=m(s))}return t}class ee extends Error{constructor(e){super(e.message),this.errMsg=e.message||e.errMsg||"unknown system error",this.code=this.errCode=e.code||e.errCode||"SYSTEM_ERROR",this.errSubject=this.subject=e.subject||e.errSubject,this.cause=e.cause,this.requestId=e.requestId}toJson(e=0){if(!(e>=10))return e++,{errCode:this.errCode,errMsg:this.errMsg,errSubject:this.errSubject,cause:this.cause&&this.cause.toJson?this.cause.toJson(e):this.cause}}}var te={request:e=>uni.request(e),uploadFile:e=>uni.uploadFile(e),setStorageSync:(e,t)=>uni.setStorageSync(e,t),getStorageSync:e=>uni.getStorageSync(e),removeStorageSync:e=>uni.removeStorageSync(e),clearStorageSync:()=>uni.clearStorageSync(),connectSocket:e=>uni.connectSocket(e)};function ne(e){return e&&ne(e.__v_raw)||e}function se(){return{token:te.getStorageSync("uni_id_token")||te.getStorageSync("uniIdToken"),tokenExpired:te.getStorageSync("uni_id_token_expired")}}function re({token:e,tokenExpired:t}={}){e&&te.setStorageSync("uni_id_token",e),t&&te.setStorageSync("uni_id_token_expired",t)}let ie,oe;function ae(){return ie||(ie=uni.getSystemInfoSync()),ie}function ce(){let e,t;try{if(uni.getLaunchOptionsSync){if(uni.getLaunchOptionsSync.toString().indexOf("not yet implemented")>-1)return;const{scene:n,channel:s}=uni.getLaunchOptionsSync();e=s,t=n}}catch(e){}return{channel:e,scene:t}}let ue={};function le(){const e=uni.getLocale&&uni.getLocale()||"en";if(oe)return{...ue,...oe,locale:e,LOCALE:e};const t=ae(),{deviceId:n,osName:s,uniPlatform:r,appId:i}=t,o=["appId","appLanguage","appName","appVersion","appVersionCode","appWgtVersion","browserName","browserVersion","deviceBrand","deviceId","deviceModel","deviceType","osName","osVersion","romName","romVersion","ua","hostName","hostVersion","uniPlatform","uniRuntimeVersion","uniRuntimeVersionCode","uniCompilerVersion","uniCompilerVersionCode"];for(const e in t)Object.hasOwnProperty.call(t,e)&&-1===o.indexOf(e)&&delete t[e];return oe={PLATFORM:r,OS:s,APPID:i,DEVICEID:n,...ce(),...t},{...ue,...oe,locale:e,LOCALE:e}}var he={sign:function(e,t){let n="";return Object.keys(e).sort().forEach((function(t){e[t]&&(n=n+"&"+t+"="+e[t])})),n=n.slice(1),r(n,t).toString()},wrappedRequest:function(e,t){return new Promise(((n,s)=>{t(Object.assign(e,{complete(e){e||(e={}),I&&"web"===A&&e.errMsg&&0===e.errMsg.indexOf("request:fail")&&console.warn("发布H5，需要在uniCloud后台操作，绑定安全域名，否则会因为跨域问题而无法访问。教程参考：https://uniapp.dcloud.io/uniCloud/quickstart?id=useinh5");const t=e.data&&e.data.header&&e.data.header["x-serverless-request-id"]||e.header&&e.header["request-id"];if(!e.statusCode||e.statusCode>=400){const n=e.data&&e.data.error&&e.data.error.code||"SYS_ERR",r=e.data&&e.data.error&&e.data.error.message||e.errMsg||"request:fail";return s(new ee({code:n,message:r,requestId:t}))}const r=e.data;if(r.error)return s(new ee({code:r.error.code,message:r.error.message,requestId:t}));r.result=r.data,r.requestId=t,delete r.data,n(r)}}))}))},toBase64:function(e){return o.stringify(i.parse(e))}};var de=class{constructor(e){["spaceId","clientSecret"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),this.config=Object.assign({},{endpoint:0===e.spaceId.indexOf("mp-")?"https://api.next.bspapp.com":"https://api.bspapp.com"},e),this.config.provider="aliyun",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.config.accessTokenKey="access_token_"+this.config.spaceId,this.adapter=te,this._getAccessTokenPromiseHub=new w({createPromise:()=>this.requestAuth(this.setupRequest({method:"serverless.auth.user.anonymousAuthorize",params:"{}"},"auth")).then((e=>{if(!e.result||!e.result.accessToken)throw new ee({code:"AUTH_FAILED",message:"获取accessToken失败"});this.setAccessToken(e.result.accessToken)})),retryRule:_})}get hasAccessToken(){return!!this.accessToken}setAccessToken(e){this.accessToken=e}requestWrapped(e){return he.wrappedRequest(e,this.adapter.request)}requestAuth(e){return this.requestWrapped(e)}request(e,t){return Promise.resolve().then((()=>this.hasAccessToken?t?this.requestWrapped(e):this.requestWrapped(e).catch((t=>new Promise(((e,n)=>{!t||"GATEWAY_INVALID_TOKEN"!==t.code&&"InvalidParameter.InvalidToken"!==t.code?n(t):e()})).then((()=>this.getAccessToken())).then((()=>{const t=this.rebuildRequest(e);return this.request(t,!0)})))):this.getAccessToken().then((()=>{const t=this.rebuildRequest(e);return this.request(t,!0)}))))}rebuildRequest(e){const t=Object.assign({},e);return t.data.token=this.accessToken,t.header["x-basement-token"]=this.accessToken,t.header["x-serverless-sign"]=he.sign(t.data,this.config.clientSecret),t}setupRequest(e,t){const n=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),s={"Content-Type":"application/json"};return"auth"!==t&&(n.token=this.accessToken,s["x-basement-token"]=this.accessToken),s["x-serverless-sign"]=he.sign(n,this.config.clientSecret),{url:this.config.requestUrl,method:"POST",data:n,dataType:"json",header:s}}getAccessToken(){return this._getAccessTokenPromiseHub.exec()}async authorize(){await this.getAccessToken()}callFunction(e){const t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request({...this.setupRequest(t),timeout:e.timeout})}getOSSUploadOptionsFromPath(e){const t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}uploadFileToOSS({url:e,formData:t,name:n,filePath:s,fileType:r,onUploadProgress:i}){return new Promise(((o,a)=>{const c=this.adapter.uploadFile({url:e,formData:t,name:n,filePath:s,fileType:r,header:{"X-OSS-server-side-encrpytion":"AES256"},success(e){e&&e.statusCode<400?o(e):a(new ee({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){a(new ee({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof i&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((e=>{i({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}reportOSSUpload(e){const t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}async uploadFile({filePath:e,cloudPath:t,fileType:n="image",cloudPathAsRealPath:s=!1,onUploadProgress:r,config:i}){if("string"!==p(t))throw new ee({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(t=t.trim()))throw new ee({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(t))throw new ee({code:"INVALID_PARAM",message:"cloudPath不合法"});const o=i&&i.envType||this.config.envType;if(s&&("/"!==t[0]&&(t="/"+t),t.indexOf("\\")>-1))throw new ee({code:"INVALID_PARAM",message:"使用cloudPath作为路径时，cloudPath不可包含“\\”"});const a=(await this.getOSSUploadOptionsFromPath({env:o,filename:s?t.split("/").pop():t,fileId:s?t:void 0})).result,c="https://"+a.cdnDomain+"/"+a.ossPath,{securityToken:u,accessKeyId:l,signature:h,host:d,ossPath:f,id:g,policy:m,ossCallbackUrl:y}=a,_={"Cache-Control":"max-age=2592000","Content-Disposition":"attachment",OSSAccessKeyId:l,Signature:h,host:d,id:g,key:f,policy:m,success_action_status:200};if(u&&(_["x-oss-security-token"]=u),y){const e=JSON.stringify({callbackUrl:y,callbackBody:JSON.stringify({fileId:g,spaceId:this.config.spaceId}),callbackBodyType:"application/json"});_.callback=he.toBase64(e)}const w={url:"https://"+a.host,formData:_,fileName:"file",name:"file",filePath:e,fileType:n};if(await this.uploadFileToOSS(Object.assign({},w,{onUploadProgress:r})),y)return{success:!0,filePath:e,fileID:c};if((await this.reportOSSUpload({id:g})).success)return{success:!0,filePath:e,fileID:c};throw new ee({code:"UPLOAD_FAILED",message:"文件上传失败"})}getTempFileURL({fileList:e}={}){return new Promise(((t,n)=>{Array.isArray(e)&&0!==e.length||n(new ee({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"})),t({fileList:e.map((e=>({fileID:e,tempFileURL:e})))})}))}async getFileInfo({fileList:e}={}){if(!Array.isArray(e)||0===e.length)throw new ee({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const t={method:"serverless.file.resource.info",params:JSON.stringify({id:e.map((e=>e.split("?")[0])).join(",")})};return{fileList:(await this.request(this.setupRequest(t))).result}}};var pe={init(e){const t=new de(e),n={signInAnonymously:function(){return t.authorize()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}};const fe="undefined"!=typeof location&&"http:"===location.protocol?"http:":"https:";var ge;!function(e){e.local="local",e.none="none",e.session="session"}(ge||(ge={}));var me=function(){},ye=t((function(e,t){var n;e.exports=(n=s,function(e){var t=n,s=t.lib,r=s.WordArray,i=s.Hasher,o=t.algo,a=[],c=[];!function(){function t(t){for(var n=e.sqrt(t),s=2;s<=n;s++)if(!(t%s))return!1;return!0}function n(e){return 4294967296*(e-(0|e))|0}for(var s=2,r=0;r<64;)t(s)&&(r<8&&(a[r]=n(e.pow(s,.5))),c[r]=n(e.pow(s,1/3)),r++),s++}();var u=[],l=o.SHA256=i.extend({_doReset:function(){this._hash=new r.init(a.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,s=n[0],r=n[1],i=n[2],o=n[3],a=n[4],l=n[5],h=n[6],d=n[7],p=0;p<64;p++){if(p<16)u[p]=0|e[t+p];else{var f=u[p-15],g=(f<<25|f>>>7)^(f<<14|f>>>18)^f>>>3,m=u[p-2],y=(m<<15|m>>>17)^(m<<13|m>>>19)^m>>>10;u[p]=g+u[p-7]+y+u[p-16]}var _=s&r^s&i^r&i,w=(s<<30|s>>>2)^(s<<19|s>>>13)^(s<<10|s>>>22),v=d+((a<<26|a>>>6)^(a<<21|a>>>11)^(a<<7|a>>>25))+(a&l^~a&h)+c[p]+u[p];d=h,h=l,l=a,a=o+v|0,o=i,i=r,r=s,s=v+(w+_)|0}n[0]=n[0]+s|0,n[1]=n[1]+r|0,n[2]=n[2]+i|0,n[3]=n[3]+o|0,n[4]=n[4]+a|0,n[5]=n[5]+l|0,n[6]=n[6]+h|0,n[7]=n[7]+d|0},_doFinalize:function(){var t=this._data,n=t.words,s=8*this._nDataBytes,r=8*t.sigBytes;return n[r>>>5]|=128<<24-r%32,n[14+(r+64>>>9<<4)]=e.floor(s/4294967296),n[15+(r+64>>>9<<4)]=s,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=i._createHelper(l),t.HmacSHA256=i._createHmacHelper(l)}(Math),n.SHA256)})),_e=ye,we=t((function(e,t){e.exports=s.HmacSHA256}));const ve=()=>{let e;if(!Promise){e=()=>{},e.promise={};const t=()=>{throw new ee({message:'Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.'})};return Object.defineProperty(e.promise,"then",{get:t}),Object.defineProperty(e.promise,"catch",{get:t}),e}const t=new Promise(((t,n)=>{e=(e,s)=>e?n(e):t(s)}));return e.promise=t,e};function Ie(e){return void 0===e}function Se(e){return"[object Null]"===Object.prototype.toString.call(e)}var be;function ke(e){const t=(n=e,"[object Array]"===Object.prototype.toString.call(n)?e:[e]);var n;for(const e of t){const{isMatch:t,genAdapter:n,runtime:s}=e;if(t())return{adapter:n(),runtime:s}}}!function(e){e.WEB="web",e.WX_MP="wx_mp"}(be||(be={}));const Ae={adapter:null,runtime:void 0},Pe=["anonymousUuidKey"];class Ce extends me{constructor(){super(),Ae.adapter.root.tcbObject||(Ae.adapter.root.tcbObject={})}setItem(e,t){Ae.adapter.root.tcbObject[e]=t}getItem(e){return Ae.adapter.root.tcbObject[e]}removeItem(e){delete Ae.adapter.root.tcbObject[e]}clear(){delete Ae.adapter.root.tcbObject}}function Te(e,t){switch(e){case"local":return t.localStorage||new Ce;case"none":return new Ce;default:return t.sessionStorage||new Ce}}class xe{constructor(e){if(!this._storage){this._persistence=Ae.adapter.primaryStorage||e.persistence,this._storage=Te(this._persistence,Ae.adapter);const t=`access_token_${e.env}`,n=`access_token_expire_${e.env}`,s=`refresh_token_${e.env}`,r=`anonymous_uuid_${e.env}`,i=`login_type_${e.env}`,o=`user_info_${e.env}`;this.keys={accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:s,anonymousUuidKey:r,loginTypeKey:i,userInfoKey:o}}}updatePersistence(e){if(e===this._persistence)return;const t="local"===this._persistence;this._persistence=e;const n=Te(e,Ae.adapter);for(const e in this.keys){const s=this.keys[e];if(t&&Pe.includes(e))continue;const r=this._storage.getItem(s);Ie(r)||Se(r)||(n.setItem(s,r),this._storage.removeItem(s))}this._storage=n}setStore(e,t,n){if(!this._storage)return;const s={version:n||"localCachev1",content:t},r=JSON.stringify(s);try{this._storage.setItem(e,r)}catch(e){throw e}}getStore(e,t){try{if(!this._storage)return}catch(e){return""}t=t||"localCachev1";const n=this._storage.getItem(e);if(!n)return"";if(n.indexOf(t)>=0){return JSON.parse(n).content}return""}removeStore(e){this._storage.removeItem(e)}}const Oe={},Ee={};function Le(e){return Oe[e]}class Re{constructor(e,t){this.data=t||null,this.name=e}}class Ue extends Re{constructor(e,t){super("error",{error:e,data:t}),this.error=e}}const Ne=new class{constructor(){this._listeners={}}on(e,t){return function(e,t,n){n[e]=n[e]||[],n[e].push(t)}(e,t,this._listeners),this}off(e,t){return function(e,t,n){if(n&&n[e]){const s=n[e].indexOf(t);-1!==s&&n[e].splice(s,1)}}(e,t,this._listeners),this}fire(e,t){if(e instanceof Ue)return console.error(e.error),this;const n="string"==typeof e?new Re(e,t||{}):e;const s=n.name;if(this._listens(s)){n.target=this;const e=this._listeners[s]?[...this._listeners[s]]:[];for(const t of e)t.call(this,n)}return this}_listens(e){return this._listeners[e]&&this._listeners[e].length>0}};function De(e,t){Ne.on(e,t)}function Me(e,t={}){Ne.fire(e,t)}function qe(e,t){Ne.off(e,t)}const Fe="loginStateChanged",Ke="loginStateExpire",je="loginTypeChanged",$e="anonymousConverted",Be="refreshAccessToken";var We;!function(e){e.ANONYMOUS="ANONYMOUS",e.WECHAT="WECHAT",e.WECHAT_PUBLIC="WECHAT-PUBLIC",e.WECHAT_OPEN="WECHAT-OPEN",e.CUSTOM="CUSTOM",e.EMAIL="EMAIL",e.USERNAME="USERNAME",e.NULL="NULL"}(We||(We={}));const He=["auth.getJwt","auth.logout","auth.signInWithTicket","auth.signInAnonymously","auth.signIn","auth.fetchAccessTokenWithRefreshToken","auth.signUpWithEmailAndPassword","auth.activateEndUserMail","auth.sendPasswordResetEmail","auth.resetPasswordWithToken","auth.isUsernameRegistered"],Je={"X-SDK-Version":"1.3.5"};function ze(e,t,n){const s=e[t];e[t]=function(t){const r={},i={};n.forEach((n=>{const{data:s,headers:o}=n.call(e,t);Object.assign(r,s),Object.assign(i,o)}));const o=t.data;return o&&(()=>{var e;if(e=o,"[object FormData]"!==Object.prototype.toString.call(e))t.data={...o,...r};else for(const e in r)o.append(e,r[e])})(),t.headers={...t.headers||{},...i},s.call(e,t)}}function Ve(){const e=Math.random().toString(16).slice(2);return{data:{seqId:e},headers:{...Je,"x-seqid":e}}}class Ge{constructor(e={}){var t;this.config=e,this._reqClass=new Ae.adapter.reqClass({timeout:this.config.timeout,timeoutMsg:`请求在${this.config.timeout/1e3}s内未完成，已中断`,restrictedMethods:["post"]}),this._cache=Le(this.config.env),this._localCache=(t=this.config.env,Ee[t]),ze(this._reqClass,"post",[Ve]),ze(this._reqClass,"upload",[Ve]),ze(this._reqClass,"download",[Ve])}async post(e){return await this._reqClass.post(e)}async upload(e){return await this._reqClass.upload(e)}async download(e){return await this._reqClass.download(e)}async refreshAccessToken(){let e,t;this._refreshAccessTokenPromise||(this._refreshAccessTokenPromise=this._refreshAccessToken());try{e=await this._refreshAccessTokenPromise}catch(e){t=e}if(this._refreshAccessTokenPromise=null,this._shouldRefreshAccessTokenHook=null,t)throw t;return e}async _refreshAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,refreshTokenKey:n,loginTypeKey:s,anonymousUuidKey:r}=this._cache.keys;this._cache.removeStore(e),this._cache.removeStore(t);let i=this._cache.getStore(n);if(!i)throw new ee({message:"未登录CloudBase"});const o={refresh_token:i},a=await this.request("auth.fetchAccessTokenWithRefreshToken",o);if(a.data.code){const{code:e}=a.data;if("SIGN_PARAM_INVALID"===e||"REFRESH_TOKEN_EXPIRED"===e||"INVALID_REFRESH_TOKEN"===e){if(this._cache.getStore(s)===We.ANONYMOUS&&"INVALID_REFRESH_TOKEN"===e){const e=this._cache.getStore(r),t=this._cache.getStore(n),s=await this.send("auth.signInAnonymously",{anonymous_uuid:e,refresh_token:t});return this.setRefreshToken(s.refresh_token),this._refreshAccessToken()}Me(Ke),this._cache.removeStore(n)}throw new ee({code:a.data.code,message:`刷新access token失败：${a.data.code}`})}if(a.data.access_token)return Me(Be),this._cache.setStore(e,a.data.access_token),this._cache.setStore(t,a.data.access_token_expire+Date.now()),{accessToken:a.data.access_token,accessTokenExpire:a.data.access_token_expire};a.data.refresh_token&&(this._cache.removeStore(n),this._cache.setStore(n,a.data.refresh_token),this._refreshAccessToken())}async getAccessToken(){const{accessTokenKey:e,accessTokenExpireKey:t,refreshTokenKey:n}=this._cache.keys;if(!this._cache.getStore(n))throw new ee({message:"refresh token不存在，登录状态异常"});let s=this._cache.getStore(e),r=this._cache.getStore(t),i=!0;return this._shouldRefreshAccessTokenHook&&!await this._shouldRefreshAccessTokenHook(s,r)&&(i=!1),(!s||!r||r<Date.now())&&i?this.refreshAccessToken():{accessToken:s,accessTokenExpire:r}}async request(e,t,n){const s=`x-tcb-trace_${this.config.env}`;let r="application/x-www-form-urlencoded";const i={action:e,env:this.config.env,dataVersion:"2019-08-16",...t};if(-1===He.indexOf(e)){const{refreshTokenKey:e}=this._cache.keys;this._cache.getStore(e)&&(i.access_token=(await this.getAccessToken()).accessToken)}let o;if("storage.uploadFile"===e){o=new FormData;for(let e in o)o.hasOwnProperty(e)&&void 0!==o[e]&&o.append(e,i[e]);r="multipart/form-data"}else{r="application/json",o={};for(let e in i)void 0!==i[e]&&(o[e]=i[e])}let a={headers:{"content-type":r}};n&&n.timeout&&(a.timeout=n.timeout),n&&n.onUploadProgress&&(a.onUploadProgress=n.onUploadProgress);const c=this._localCache.getStore(s);c&&(a.headers["X-TCB-Trace"]=c);const{parse:u,inQuery:l,search:h}=t;let d={env:this.config.env};u&&(d.parse=!0),l&&(d={...l,...d});let p=function(e,t,n={}){const s=/\?/.test(t);let r="";for(let e in n)""===r?!s&&(t+="?"):r+="&",r+=`${e}=${encodeURIComponent(n[e])}`;return/^http(s)?\:\/\//.test(t+=r)?t:`${e}${t}`}(fe,"//tcb-api.tencentcloudapi.com/web",d);h&&(p+=h);const f=await this.post({url:p,data:o,...a}),g=f.header&&f.header["x-tcb-trace"];if(g&&this._localCache.setStore(s,g),200!==Number(f.status)&&200!==Number(f.statusCode)||!f.data)throw new ee({code:"NETWORK_ERROR",message:"network request error"});return f}async send(e,t={},n={}){const s=await this.request(e,t,{...n,onUploadProgress:t.onUploadProgress});if("ACCESS_TOKEN_EXPIRED"===s.data.code&&-1===He.indexOf(e)){await this.refreshAccessToken();const s=await this.request(e,t,{...n,onUploadProgress:t.onUploadProgress});if(s.data.code)throw new ee({code:s.data.code,message:s.data.message});return s.data}if(s.data.code)throw new ee({code:s.data.code,message:s.data.message});return s.data}setRefreshToken(e){const{accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:s}=this._cache.keys;this._cache.removeStore(t),this._cache.removeStore(n),this._cache.setStore(s,e)}}const Ye={};function Qe(e){return Ye[e]}class Xe{constructor(e){this.config=e,this._cache=Le(e.env),this._request=Qe(e.env)}setRefreshToken(e){const{accessTokenKey:t,accessTokenExpireKey:n,refreshTokenKey:s}=this._cache.keys;this._cache.removeStore(t),this._cache.removeStore(n),this._cache.setStore(s,e)}setAccessToken(e,t){const{accessTokenKey:n,accessTokenExpireKey:s}=this._cache.keys;this._cache.setStore(n,e),this._cache.setStore(s,t)}async refreshUserInfo(){const{data:e}=await this._request.send("auth.getUserInfo",{});return this.setLocalUserInfo(e),e}setLocalUserInfo(e){const{userInfoKey:t}=this._cache.keys;this._cache.setStore(t,e)}}class Ze{constructor(e){if(!e)throw new ee({code:"PARAM_ERROR",message:"envId is not defined"});this._envId=e,this._cache=Le(this._envId),this._request=Qe(this._envId),this.setUserInfo()}linkWithTicket(e){if("string"!=typeof e)throw new ee({code:"PARAM_ERROR",message:"ticket must be string"});return this._request.send("auth.linkWithTicket",{ticket:e})}linkWithRedirect(e){e.signInWithRedirect()}updatePassword(e,t){return this._request.send("auth.updatePassword",{oldPassword:t,newPassword:e})}updateEmail(e){return this._request.send("auth.updateEmail",{newEmail:e})}updateUsername(e){if("string"!=typeof e)throw new ee({code:"PARAM_ERROR",message:"username must be a string"});return this._request.send("auth.updateUsername",{username:e})}async getLinkedUidList(){const{data:e}=await this._request.send("auth.getLinkedUidList",{});let t=!1;const{users:n}=e;return n.forEach((e=>{e.wxOpenId&&e.wxPublicId&&(t=!0)})),{users:n,hasPrimaryUid:t}}setPrimaryUid(e){return this._request.send("auth.setPrimaryUid",{uid:e})}unlink(e){return this._request.send("auth.unlink",{platform:e})}async update(e){const{nickName:t,gender:n,avatarUrl:s,province:r,country:i,city:o}=e,{data:a}=await this._request.send("auth.updateUserInfo",{nickName:t,gender:n,avatarUrl:s,province:r,country:i,city:o});this.setLocalUserInfo(a)}async refresh(){const{data:e}=await this._request.send("auth.getUserInfo",{});return this.setLocalUserInfo(e),e}setUserInfo(){const{userInfoKey:e}=this._cache.keys,t=this._cache.getStore(e);["uid","loginType","openid","wxOpenId","wxPublicId","unionId","qqMiniOpenId","email","hasPassword","customUserId","nickName","gender","avatarUrl"].forEach((e=>{this[e]=t[e]})),this.location={country:t.country,province:t.province,city:t.city}}setLocalUserInfo(e){const{userInfoKey:t}=this._cache.keys;this._cache.setStore(t,e),this.setUserInfo()}}class et{constructor(e){if(!e)throw new ee({code:"PARAM_ERROR",message:"envId is not defined"});this._cache=Le(e);const{refreshTokenKey:t,accessTokenKey:n,accessTokenExpireKey:s}=this._cache.keys,r=this._cache.getStore(t),i=this._cache.getStore(n),o=this._cache.getStore(s);this.credential={refreshToken:r,accessToken:i,accessTokenExpire:o},this.user=new Ze(e)}get isAnonymousAuth(){return this.loginType===We.ANONYMOUS}get isCustomAuth(){return this.loginType===We.CUSTOM}get isWeixinAuth(){return this.loginType===We.WECHAT||this.loginType===We.WECHAT_OPEN||this.loginType===We.WECHAT_PUBLIC}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}}class tt extends Xe{async signIn(){this._cache.updatePersistence("local");const{anonymousUuidKey:e,refreshTokenKey:t}=this._cache.keys,n=this._cache.getStore(e)||void 0,s=this._cache.getStore(t)||void 0,r=await this._request.send("auth.signInAnonymously",{anonymous_uuid:n,refresh_token:s});if(r.uuid&&r.refresh_token){this._setAnonymousUUID(r.uuid),this.setRefreshToken(r.refresh_token),await this._request.refreshAccessToken(),Me(Fe),Me(je,{env:this.config.env,loginType:We.ANONYMOUS,persistence:"local"});const e=new et(this.config.env);return await e.user.refresh(),e}throw new ee({message:"匿名登录失败"})}async linkAndRetrieveDataWithTicket(e){const{anonymousUuidKey:t,refreshTokenKey:n}=this._cache.keys,s=this._cache.getStore(t),r=this._cache.getStore(n),i=await this._request.send("auth.linkAndRetrieveDataWithTicket",{anonymous_uuid:s,refresh_token:r,ticket:e});if(i.refresh_token)return this._clearAnonymousUUID(),this.setRefreshToken(i.refresh_token),await this._request.refreshAccessToken(),Me($e,{env:this.config.env}),Me(je,{loginType:We.CUSTOM,persistence:"local"}),{credential:{refreshToken:i.refresh_token}};throw new ee({message:"匿名转化失败"})}_setAnonymousUUID(e){const{anonymousUuidKey:t,loginTypeKey:n}=this._cache.keys;this._cache.removeStore(t),this._cache.setStore(t,e),this._cache.setStore(n,We.ANONYMOUS)}_clearAnonymousUUID(){this._cache.removeStore(this._cache.keys.anonymousUuidKey)}}class nt extends Xe{async signIn(e){if("string"!=typeof e)throw new ee({code:"PARAM_ERROR",message:"ticket must be a string"});const{refreshTokenKey:t}=this._cache.keys,n=await this._request.send("auth.signInWithTicket",{ticket:e,refresh_token:this._cache.getStore(t)||""});if(n.refresh_token)return this.setRefreshToken(n.refresh_token),await this._request.refreshAccessToken(),Me(Fe),Me(je,{env:this.config.env,loginType:We.CUSTOM,persistence:this.config.persistence}),await this.refreshUserInfo(),new et(this.config.env);throw new ee({message:"自定义登录失败"})}}class st extends Xe{async signIn(e,t){if("string"!=typeof e)throw new ee({code:"PARAM_ERROR",message:"email must be a string"});const{refreshTokenKey:n}=this._cache.keys,s=await this._request.send("auth.signIn",{loginType:"EMAIL",email:e,password:t,refresh_token:this._cache.getStore(n)||""}),{refresh_token:r,access_token:i,access_token_expire:o}=s;if(r)return this.setRefreshToken(r),i&&o?this.setAccessToken(i,o):await this._request.refreshAccessToken(),await this.refreshUserInfo(),Me(Fe),Me(je,{env:this.config.env,loginType:We.EMAIL,persistence:this.config.persistence}),new et(this.config.env);throw s.code?new ee({code:s.code,message:`邮箱登录失败: ${s.message}`}):new ee({message:"邮箱登录失败"})}async activate(e){return this._request.send("auth.activateEndUserMail",{token:e})}async resetPasswordWithToken(e,t){return this._request.send("auth.resetPasswordWithToken",{token:e,newPassword:t})}}class rt extends Xe{async signIn(e,t){if("string"!=typeof e)throw new ee({code:"PARAM_ERROR",message:"username must be a string"});"string"!=typeof t&&(t="",console.warn("password is empty"));const{refreshTokenKey:n}=this._cache.keys,s=await this._request.send("auth.signIn",{loginType:We.USERNAME,username:e,password:t,refresh_token:this._cache.getStore(n)||""}),{refresh_token:r,access_token_expire:i,access_token:o}=s;if(r)return this.setRefreshToken(r),o&&i?this.setAccessToken(o,i):await this._request.refreshAccessToken(),await this.refreshUserInfo(),Me(Fe),Me(je,{env:this.config.env,loginType:We.USERNAME,persistence:this.config.persistence}),new et(this.config.env);throw s.code?new ee({code:s.code,message:`用户名密码登录失败: ${s.message}`}):new ee({message:"用户名密码登录失败"})}}class it{constructor(e){this.config=e,this._cache=Le(e.env),this._request=Qe(e.env),this._onAnonymousConverted=this._onAnonymousConverted.bind(this),this._onLoginTypeChanged=this._onLoginTypeChanged.bind(this),De(je,this._onLoginTypeChanged)}get currentUser(){const e=this.hasLoginState();return e&&e.user||null}get loginType(){return this._cache.getStore(this._cache.keys.loginTypeKey)}anonymousAuthProvider(){return new tt(this.config)}customAuthProvider(){return new nt(this.config)}emailAuthProvider(){return new st(this.config)}usernameAuthProvider(){return new rt(this.config)}async signInAnonymously(){return new tt(this.config).signIn()}async signInWithEmailAndPassword(e,t){return new st(this.config).signIn(e,t)}signInWithUsernameAndPassword(e,t){return new rt(this.config).signIn(e,t)}async linkAndRetrieveDataWithTicket(e){this._anonymousAuthProvider||(this._anonymousAuthProvider=new tt(this.config)),De($e,this._onAnonymousConverted);return await this._anonymousAuthProvider.linkAndRetrieveDataWithTicket(e)}async signOut(){if(this.loginType===We.ANONYMOUS)throw new ee({message:"匿名用户不支持登出操作"});const{refreshTokenKey:e,accessTokenKey:t,accessTokenExpireKey:n}=this._cache.keys,s=this._cache.getStore(e);if(!s)return;const r=await this._request.send("auth.logout",{refresh_token:s});return this._cache.removeStore(e),this._cache.removeStore(t),this._cache.removeStore(n),Me(Fe),Me(je,{env:this.config.env,loginType:We.NULL,persistence:this.config.persistence}),r}async signUpWithEmailAndPassword(e,t){return this._request.send("auth.signUpWithEmailAndPassword",{email:e,password:t})}async sendPasswordResetEmail(e){return this._request.send("auth.sendPasswordResetEmail",{email:e})}onLoginStateChanged(e){De(Fe,(()=>{const t=this.hasLoginState();e.call(this,t)}));const t=this.hasLoginState();e.call(this,t)}onLoginStateExpired(e){De(Ke,e.bind(this))}onAccessTokenRefreshed(e){De(Be,e.bind(this))}onAnonymousConverted(e){De($e,e.bind(this))}onLoginTypeChanged(e){De(je,(()=>{const t=this.hasLoginState();e.call(this,t)}))}async getAccessToken(){return{accessToken:(await this._request.getAccessToken()).accessToken,env:this.config.env}}hasLoginState(){const{refreshTokenKey:e}=this._cache.keys;return this._cache.getStore(e)?new et(this.config.env):null}async isUsernameRegistered(e){if("string"!=typeof e)throw new ee({code:"PARAM_ERROR",message:"username must be a string"});const{data:t}=await this._request.send("auth.isUsernameRegistered",{username:e});return t&&t.isRegistered}getLoginState(){return Promise.resolve(this.hasLoginState())}async signInWithTicket(e){return new nt(this.config).signIn(e)}shouldRefreshAccessToken(e){this._request._shouldRefreshAccessTokenHook=e.bind(this)}getUserInfo(){return this._request.send("auth.getUserInfo",{}).then((e=>e.code?e:{...e.data,requestId:e.seqId}))}getAuthHeader(){const{refreshTokenKey:e,accessTokenKey:t}=this._cache.keys,n=this._cache.getStore(e);return{"x-cloudbase-credentials":this._cache.getStore(t)+"/@@/"+n}}_onAnonymousConverted(e){const{env:t}=e.data;t===this.config.env&&this._cache.updatePersistence(this.config.persistence)}_onLoginTypeChanged(e){const{loginType:t,persistence:n,env:s}=e.data;s===this.config.env&&(this._cache.updatePersistence(n),this._cache.setStore(this._cache.keys.loginTypeKey,t))}}const ot=function(e,t){t=t||ve();const n=Qe(this.config.env),{cloudPath:s,filePath:r,onUploadProgress:i,fileType:o="image"}=e;return n.send("storage.getUploadMetadata",{path:s}).then((e=>{const{data:{url:a,authorization:c,token:u,fileId:l,cosFileId:h},requestId:d}=e,p={key:s,signature:c,"x-cos-meta-fileid":h,success_action_status:"201","x-cos-security-token":u};n.upload({url:a,data:p,file:r,name:s,fileType:o,onUploadProgress:i}).then((e=>{201===e.statusCode?t(null,{fileID:l,requestId:d}):t(new ee({code:"STORAGE_REQUEST_FAIL",message:`STORAGE_REQUEST_FAIL: ${e.data}`}))})).catch((e=>{t(e)}))})).catch((e=>{t(e)})),t.promise},at=function(e,t){t=t||ve();const n=Qe(this.config.env),{cloudPath:s}=e;return n.send("storage.getUploadMetadata",{path:s}).then((e=>{t(null,e)})).catch((e=>{t(e)})),t.promise},ct=function({fileList:e},t){if(t=t||ve(),!e||!Array.isArray(e))return{code:"INVALID_PARAM",message:"fileList必须是非空的数组"};for(let t of e)if(!t||"string"!=typeof t)return{code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"};const n={fileid_list:e};return Qe(this.config.env).send("storage.batchDeleteFile",n).then((e=>{e.code?t(null,e):t(null,{fileList:e.data.delete_list,requestId:e.requestId})})).catch((e=>{t(e)})),t.promise},ut=function({fileList:e},t){t=t||ve(),e&&Array.isArray(e)||t(null,{code:"INVALID_PARAM",message:"fileList必须是非空的数组"});let n=[];for(let s of e)"object"==typeof s?(s.hasOwnProperty("fileID")&&s.hasOwnProperty("maxAge")||t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是包含fileID和maxAge的对象"}),n.push({fileid:s.fileID,max_age:s.maxAge})):"string"==typeof s?n.push({fileid:s}):t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是字符串"});const s={file_list:n};return Qe(this.config.env).send("storage.batchGetDownloadUrl",s).then((e=>{e.code?t(null,e):t(null,{fileList:e.data.download_list,requestId:e.requestId})})).catch((e=>{t(e)})),t.promise},lt=async function({fileID:e},t){const n=(await ut.call(this,{fileList:[{fileID:e,maxAge:600}]})).fileList[0];if("SUCCESS"!==n.code)return t?t(n):new Promise((e=>{e(n)}));const s=Qe(this.config.env);let r=n.download_url;if(r=encodeURI(r),!t)return s.download({url:r});t(await s.download({url:r}))},ht=function({name:e,data:t,query:n,parse:s,search:r,timeout:i},o){const a=o||ve();let c;try{c=t?JSON.stringify(t):""}catch(e){return Promise.reject(e)}if(!e)return Promise.reject(new ee({code:"PARAM_ERROR",message:"函数名不能为空"}));const u={inQuery:n,parse:s,search:r,function_name:e,request_data:c};return Qe(this.config.env).send("functions.invokeFunction",u,{timeout:i}).then((e=>{if(e.code)a(null,e);else{let t=e.data.response_data;if(s)a(null,{result:t,requestId:e.requestId});else try{t=JSON.parse(e.data.response_data),a(null,{result:t,requestId:e.requestId})}catch(e){a(new ee({message:"response data must be json"}))}}return a.promise})).catch((e=>{a(e)})),a.promise},dt={timeout:15e3,persistence:"session"},pt={};class ft{constructor(e){this.config=e||this.config,this.authObj=void 0}init(e){switch(Ae.adapter||(this.requestClient=new Ae.adapter.reqClass({timeout:e.timeout||5e3,timeoutMsg:`请求在${(e.timeout||5e3)/1e3}s内未完成，已中断`})),this.config={...dt,...e},!0){case this.config.timeout>6e5:console.warn("timeout大于可配置上限[10分钟]，已重置为上限数值"),this.config.timeout=6e5;break;case this.config.timeout<100:console.warn("timeout小于可配置下限[100ms]，已重置为下限数值"),this.config.timeout=100}return new ft(this.config)}auth({persistence:e}={}){if(this.authObj)return this.authObj;const t=e||Ae.adapter.primaryStorage||dt.persistence;var n;return t!==this.config.persistence&&(this.config.persistence=t),function(e){const{env:t}=e;Oe[t]=new xe(e),Ee[t]=new xe({...e,persistence:"local"})}(this.config),n=this.config,Ye[n.env]=new Ge(n),this.authObj=new it(this.config),this.authObj}on(e,t){return De.apply(this,[e,t])}off(e,t){return qe.apply(this,[e,t])}callFunction(e,t){return ht.apply(this,[e,t])}deleteFile(e,t){return ct.apply(this,[e,t])}getTempFileURL(e,t){return ut.apply(this,[e,t])}downloadFile(e,t){return lt.apply(this,[e,t])}uploadFile(e,t){return ot.apply(this,[e,t])}getUploadMetadata(e,t){return at.apply(this,[e,t])}registerExtension(e){pt[e.name]=e}async invokeExtension(e,t){const n=pt[e];if(!n)throw new ee({message:`扩展${e} 必须先注册`});return await n.invoke(t,this)}useAdapters(e){const{adapter:t,runtime:n}=ke(e)||{};t&&(Ae.adapter=t),n&&(Ae.runtime=n)}}var gt=new ft;function mt(e,t,n){void 0===n&&(n={});var s=/\?/.test(t),r="";for(var i in n)""===r?!s&&(t+="?"):r+="&",r+=i+"="+encodeURIComponent(n[i]);return/^http(s)?:\/\//.test(t+=r)?t:""+e+t}class yt{post(e){const{url:t,data:n,headers:s,timeout:r}=e;return new Promise(((e,i)=>{te.request({url:mt("https:",t),data:n,method:"POST",header:s,timeout:r,success(t){e(t)},fail(e){i(e)}})}))}upload(e){return new Promise(((t,n)=>{const{url:s,file:r,data:i,headers:o,fileType:a}=e,c=te.uploadFile({url:mt("https:",s),name:"file",formData:Object.assign({},i),filePath:r,fileType:a,header:o,success(e){const n={statusCode:e.statusCode,data:e.data||{}};200===e.statusCode&&i.success_action_status&&(n.statusCode=parseInt(i.success_action_status,10)),t(n)},fail(e){n(new Error(e.errMsg||"uploadFile:fail"))}});"function"==typeof e.onUploadProgress&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((t=>{e.onUploadProgress({loaded:t.totalBytesSent,total:t.totalBytesExpectedToSend})}))}))}}const _t={setItem(e,t){te.setStorageSync(e,t)},getItem:e=>te.getStorageSync(e),removeItem(e){te.removeStorageSync(e)},clear(){te.clearStorageSync()}};var wt={genAdapter:function(){return{root:{},reqClass:yt,localStorage:_t,primaryStorage:"local"}},isMatch:function(){return!0},runtime:"uni_app"};gt.useAdapters(wt);const vt=gt,It=vt.init;vt.init=function(e){e.env=e.spaceId;const t=It.call(this,e);t.config.provider="tencent",t.config.spaceId=e.spaceId;const n=t.auth;return t.auth=function(e){const t=n.call(this,e);return["linkAndRetrieveDataWithTicket","signInAnonymously","signOut","getAccessToken","getLoginState","signInWithTicket","getUserInfo"].forEach((e=>{var n;t[e]=(n=t[e],function(e){e=e||{};const{success:t,fail:s,complete:r}=Z(e);if(!(t||s||r))return n.call(this,e);n.call(this,e).then((e=>{t&&t(e),r&&r(e)}),(e=>{s&&s(e),r&&r(e)}))}).bind(t)})),t},t.customAuth=t.auth,t};var St=vt;async function bt(e,t){const n=`http://${e}:${t}/system/ping`;try{const e=await(s={url:n,timeout:500},new Promise(((e,t)=>{te.request({...s,success(t){e(t)},fail(e){t(e)}})})));return!(!e.data||0!==e.data.code)}catch(e){return!1}var s}async function kt(e,t){let n;for(let s=0;s<e.length;s++){const r=e[s];if(await bt(r,t)){n=r;break}}return{address:n,port:t}}const At={"serverless.file.resource.generateProximalSign":"storage/generate-proximal-sign","serverless.file.resource.report":"storage/report","serverless.file.resource.delete":"storage/delete","serverless.file.resource.getTempFileURL":"storage/get-temp-file-url"};var Pt=class{constructor(e){if(["spaceId","clientSecret"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),!e.endpoint)throw new Error("集群空间未配置ApiEndpoint，配置后需要重新关联服务空间后生效");this.config=Object.assign({},e),this.config.provider="dcloud",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.adapter=te}async request(e,t=!0){const n=I&&t;return e=n?await this.setupLocalRequest(e):this.setupRequest(e),Promise.resolve().then((()=>n?this.requestLocal(e):he.wrappedRequest(e,this.adapter.request)))}requestLocal(e){return new Promise(((t,n)=>{this.adapter.request(Object.assign(e,{complete(e){if(e||(e={}),!e.statusCode||e.statusCode>=400){const t=e.data&&e.data.code||"SYS_ERR",s=e.data&&e.data.message||"request:fail";return n(new ee({code:t,message:s}))}t({success:!0,result:e.data})}}))}))}setupRequest(e){const t=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),n={"Content-Type":"application/json"};n["x-serverless-sign"]=he.sign(t,this.config.clientSecret);const s=le();n["x-client-info"]=encodeURIComponent(JSON.stringify(s));const{token:r}=se();return n["x-client-token"]=r,{url:this.config.requestUrl,method:"POST",data:t,dataType:"json",header:JSON.parse(JSON.stringify(n))}}async setupLocalRequest(e){const t=le(),{token:n}=se(),s=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now(),clientInfo:t,token:n}),{address:r,servePort:i}=this.__dev__&&this.__dev__.debugInfo||{},{address:o}=await kt(r,i);return{url:`http://${o}:${i}/${At[e.method]}`,method:"POST",data:s,dataType:"json",header:JSON.parse(JSON.stringify({"Content-Type":"application/json"}))}}callFunction(e){const t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request(t,!1)}getUploadFileOptions(e){const t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(t)}reportUploadFile(e){const t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(t)}uploadFile({filePath:e,cloudPath:t,fileType:n="image",onUploadProgress:s}){if(!t)throw new ee({code:"CLOUDPATH_REQUIRED",message:"cloudPath不可为空"});let r;return this.getUploadFileOptions({cloudPath:t}).then((t=>{const{url:i,formData:o,name:a}=t.result;return r=t.result.fileUrl,new Promise(((t,r)=>{const c=this.adapter.uploadFile({url:i,formData:o,name:a,filePath:e,fileType:n,success(e){e&&e.statusCode<400?t(e):r(new ee({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){r(new ee({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof s&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((e=>{s({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))})).then((()=>this.reportUploadFile({cloudPath:t}))).then((t=>new Promise(((n,s)=>{t.success?n({success:!0,filePath:e,fileID:r}):s(new ee({code:"UPLOAD_FAILED",message:"文件上传失败"}))}))))}deleteFile({fileList:e}){const t={method:"serverless.file.resource.delete",params:JSON.stringify({fileList:e})};return this.request(t).then((e=>{if(e.success)return e.result;throw new ee({code:"DELETE_FILE_FAILED",message:"删除文件失败"})}))}getTempFileURL({fileList:e,maxAge:t}={}){if(!Array.isArray(e)||0===e.length)throw new ee({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});const n={method:"serverless.file.resource.getTempFileURL",params:JSON.stringify({fileList:e,maxAge:t})};return this.request(n).then((e=>{if(e.success)return{fileList:e.result.fileList.map((e=>({fileID:e.fileID,tempFileURL:e.tempFileURL})))};throw new ee({code:"GET_TEMP_FILE_URL_FAILED",message:"获取临时文件链接失败"})}))}};var Ct={init(e){const t=new Pt(e),n={signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},Tt=t((function(e,t){e.exports=s.enc.Hex}));function xt(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}function Ot(e="",t={}){const{data:n,functionName:s,method:r,headers:i,signHeaderKeys:o=[],config:a}=t,c=Date.now(),u=xt(),l=Object.assign({},i,{"x-from-app-id":a.spaceAppId,"x-from-env-id":a.spaceId,"x-to-env-id":a.spaceId,"x-from-instance-id":c,"x-from-function-name":s,"x-client-timestamp":c,"x-alipay-source":"client","x-request-id":u,"x-alipay-callid":u,"x-trace-id":u}),h=["x-from-app-id","x-from-env-id","x-to-env-id","x-from-instance-id","x-from-function-name","x-client-timestamp"].concat(o),[d="",p=""]=e.split("?")||[],f=function(e){const t=e.signedHeaders.join(";"),n=e.signedHeaders.map((t=>`${t.toLowerCase()}:${e.headers[t]}\n`)).join(""),s=_e(e.body).toString(Tt),r=`${e.method.toUpperCase()}\n${e.path}\n${e.query}\n${n}\n${t}\n${s}\n`,i=_e(r).toString(Tt),o=`HMAC-SHA256\n${e.timestamp}\n${i}\n`,a=we(o,e.secretKey).toString(Tt);return`HMAC-SHA256 Credential=${e.secretId}, SignedHeaders=${t}, Signature=${a}`}({path:d,query:p,method:r,headers:l,timestamp:c,body:JSON.stringify(n),secretId:a.accessKey,secretKey:a.secretKey,signedHeaders:h.sort()});return{url:`${a.endpoint}${e}`,headers:Object.assign({},l,{Authorization:f})}}function Et({url:e,data:t,method:n="POST",headers:s={},timeout:r}){return new Promise(((i,o)=>{te.request({url:e,method:n,data:"object"==typeof t?JSON.stringify(t):t,header:s,dataType:"json",timeout:r,complete:(e={})=>{const t=s["x-trace-id"]||"";if(!e.statusCode||e.statusCode>=400){const{message:n,errMsg:s,trace_id:r}=e.data||{};return o(new ee({code:"SYS_ERR",message:n||s||"request:fail",requestId:r||t}))}i({status:e.statusCode,data:e.data,headers:e.header,requestId:t})}})}))}function Lt(e,t){const{path:n,data:s,method:r="GET"}=e,{url:i,headers:o}=Ot(n,{functionName:"",data:s,method:r,headers:{"x-alipay-cloud-mode":"oss","x-data-api-type":"oss","x-expire-timestamp":Date.now()+6e4},signHeaderKeys:["x-data-api-type","x-expire-timestamp"],config:t});return Et({url:i,data:s,method:r,headers:o}).then((e=>{const t=e.data||{};if(!t.success)throw new ee({code:e.errCode,message:e.errMsg,requestId:e.requestId});return t.data||{}})).catch((e=>{throw new ee({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}function Rt(e=""){const t=e.trim().replace(/^cloud:\/\//,""),n=t.indexOf("/");if(n<=0)throw new ee({code:"INVALID_PARAM",message:"fileID不合法"});const s=t.substring(0,n),r=t.substring(n+1);return s!==this.config.spaceId&&console.warn("file ".concat(e," does not belong to env ").concat(this.config.spaceId)),r}function Ut(e=""){return"cloud://".concat(this.config.spaceId,"/").concat(e.replace(/^\/+/,""))}class Nt{constructor(e){this.config=e}signedURL(e,t={}){const n=`/ws/function/${e}`,s=this.config.wsEndpoint.replace(/^ws(s)?:\/\//,""),r=Object.assign({},t,{accessKeyId:this.config.accessKey,signatureNonce:xt(),timestamp:""+Date.now()}),i=[n,["accessKeyId","authorization","signatureNonce","timestamp"].sort().map((function(e){return r[e]?"".concat(e,"=").concat(r[e]):null})).filter(Boolean).join("&"),`host:${s}`].join("\n"),o=["HMAC-SHA256",_e(i).toString(Tt)].join("\n"),a=we(o,this.config.secretKey).toString(Tt),c=Object.keys(r).map((e=>`${e}=${encodeURIComponent(r[e])}`)).join("&");return`${this.config.wsEndpoint}${n}?${c}&signature=${a}`}}var Dt=class{constructor(e){if(["spaceId","spaceAppId","accessKey","secretKey"].forEach((t=>{if(!Object.prototype.hasOwnProperty.call(e,t))throw new Error(`${t} required`)})),e.endpoint){if("string"!=typeof e.endpoint)throw new Error("endpoint must be string");if(!/^https:\/\//.test(e.endpoint))throw new Error("endpoint must start with https://");e.endpoint=e.endpoint.replace(/\/$/,"")}this.config=Object.assign({},e,{endpoint:e.endpoint||`https://${e.spaceId}.api-hz.cloudbasefunction.cn`,wsEndpoint:e.wsEndpoint||`wss://${e.spaceId}.api-hz.cloudbasefunction.cn`}),this._websocket=new Nt(this.config)}callFunction(e){return function(e,t){const{name:n,data:s,async:r=!1,timeout:i}=e,o="POST",a={"x-to-function-name":n};r&&(a["x-function-invoke-type"]="async");const{url:c,headers:u}=Ot("/functions/invokeFunction",{functionName:n,data:s,method:o,headers:a,signHeaderKeys:["x-to-function-name"],config:t});return Et({url:c,data:s,method:o,headers:u,timeout:i}).then((e=>{let t=0;if(r){const n=e.data||{};t="200"===n.errCode?0:n.errCode,e.data=n.data||{},e.errMsg=n.errMsg}if(0!==t)throw new ee({code:t,message:e.errMsg,requestId:e.requestId});return{errCode:t,success:0===t,requestId:e.requestId,result:e.data}})).catch((e=>{throw new ee({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}(e,this.config)}uploadFileToOSS({url:e,filePath:t,fileType:n,formData:s,onUploadProgress:r}){return new Promise(((i,o)=>{const a=te.uploadFile({url:e,filePath:t,fileType:n,formData:s,name:"file",success(e){e&&e.statusCode<400?i(e):o(new ee({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail(e){o(new ee({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof r&&a&&"function"==typeof a.onProgressUpdate&&a.onProgressUpdate((e=>{r({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}async uploadFile({filePath:e,cloudPath:t="",fileType:n="image",onUploadProgress:s}){if("string"!==p(t))throw new ee({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});if(!(t=t.trim()))throw new ee({code:"INVALID_PARAM",message:"cloudPath不可为空"});if(/:\/\//.test(t))throw new ee({code:"INVALID_PARAM",message:"cloudPath不合法"});const r=await Lt({path:"/".concat(t.replace(/^\//,""),"?post_url")},this.config),{file_id:i,upload_url:o,form_data:a}=r,c=a&&a.reduce(((e,t)=>(e[t.key]=t.value,e)),{});return this.uploadFileToOSS({url:o,filePath:e,fileType:n,formData:c,onUploadProgress:s}).then((()=>({fileID:i})))}async getTempFileURL({fileList:e}){return new Promise(((t,n)=>{(!e||e.length<0)&&n(new ee({errCode:"INVALID_PARAM",errMsg:"fileList不能为空数组"})),e.length>50&&n(new ee({errCode:"INVALID_PARAM",errMsg:"fileList数组长度不能超过50"}));const s=[];for(const t of e){"string"!==p(t)&&n(new ee({errCode:"INVALID_PARAM",errMsg:"fileList的元素必须是非空的字符串"}));const e=Rt.call(this,t);s.push({file_id:e,expire:600})}Lt({path:"/?download_url",data:{file_list:s},method:"POST"},this.config).then((e=>{const{file_list:n=[]}=e;t({fileList:n.map((e=>({fileID:Ut.call(this,e.file_id),tempFileURL:e.download_url})))})})).catch((e=>n(e)))}))}async connectWebSocket(e){const{name:t,query:n}=e;return te.connectSocket({url:this._websocket.signedURL(t,n),complete:()=>{}})}};var Mt={init:e=>{e.provider="alipay";const t=new Dt(e);return t.auth=function(){return{signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!0)}}},t}};function qt({data:e}){let t;t=le();const n=JSON.parse(JSON.stringify(e||{}));if(Object.assign(n,{clientInfo:t}),!n.uniIdToken){const{token:e}=se();e&&(n.uniIdToken=e)}return n}async function Ft(e={}){await this.__dev__.initLocalNetwork();const{localAddress:t,localPort:n}=this.__dev__,s={aliyun:"aliyun",tencent:"tcb",alipay:"alipay",dcloud:"dcloud"}[this.config.provider],r=this.config.spaceId,i=`http://${t}:${n}/system/check-function`,o=`http://${t}:${n}/cloudfunctions/${e.name}`;return new Promise(((t,n)=>{te.request({method:"POST",url:i,data:{name:e.name,platform:A,provider:s,spaceId:r},timeout:3e3,success(e){t(e)},fail(){t({data:{code:"NETWORK_ERROR",message:"连接本地调试服务失败，请检查客户端是否和主机在同一局域网下，自动切换为已部署的云函数。"}})}})})).then((({data:e}={})=>{const{code:t,message:n}=e||{};return{code:0===t?0:t||"SYS_ERR",message:n||"SYS_ERR"}})).then((({code:t,message:n})=>{if(0!==t){switch(t){case"MODULE_ENCRYPTED":console.error(`此云函数（${e.name}）依赖加密公共模块不可本地调试，自动切换为云端已部署的云函数`);break;case"FUNCTION_ENCRYPTED":console.error(`此云函数（${e.name}）已加密不可本地调试，自动切换为云端已部署的云函数`);break;case"ACTION_ENCRYPTED":console.error(n||"需要访问加密的uni-clientDB-action，自动切换为云端环境");break;case"NETWORK_ERROR":console.error(n||"连接本地调试服务失败，请检查客户端是否和主机在同一局域网下");break;case"SWITCH_TO_CLOUD":break;default:{const e=`检测本地调试服务出现错误：${n}，请检查网络环境或重启客户端再试`;throw console.error(e),new Error(e)}}return this._callCloudFunction(e)}return new Promise(((t,n)=>{const r=qt.call(this,{data:e.data});te.request({method:"POST",url:o,data:{provider:s,platform:A,param:r},timeout:e.timeout,success:({statusCode:e,data:s}={})=>!e||e>=400?n(new ee({code:s.code||"SYS_ERR",message:s.message||"request:fail"})):t({result:s}),fail(e){n(new ee({code:e.code||e.errCode||"SYS_ERR",message:e.message||e.errMsg||"request:fail"}))}})}))}))}const Kt=[{rule:/fc_function_not_found|FUNCTION_NOT_FOUND/,content:"，云函数[{functionName}]在云端不存在，请检查此云函数名称是否正确以及该云函数是否已上传到服务空间",mode:"append"}];var jt=/[\\^$.*+?()[\]{}|]/g,$t=RegExp(jt.source);function Bt(e,t,n){return e.replace(new RegExp((s=t)&&$t.test(s)?s.replace(jt,"\\$&"):s,"g"),n);var s}const Wt="none",Ht="request",Jt="response",zt="both";class Vt{constructor({secretType:e,uniCloudIns:t}={}){this.clientType="",this.secretType=e||Wt,this.uniCloudIns=t;const{provider:n,spaceId:s}=this.uniCloudIns.config;var r;this.provider=n,this.spaceId=s,this.scopedGlobalCache=(r=this.uniCloudIns,E("_globalUniCloudSecureNetworkCache__{spaceId}".replace("{spaceId}",r.config.spaceId)))}getSystemInfo(){return this._systemInfo||(this._systemInfo=ae()),this._systemInfo}get appId(){return this.getSystemInfo().appId}get deviceId(){return this.getSystemInfo().deviceId}async encryptData(e){return this.secretType===Wt?e:this.platformEncryptData(e)}async decryptResult(e){if(this.secretType===Wt)return e;const{errCode:t,content:n}=e||{};return t||!n?e:this.secretType===Ht?n:this.platformDecryptResult(e)}wrapVerifyClientCallFunction(e){const t=this;return async function({name:n,data:s={}}={}){await t.prepare(),(s=JSON.parse(JSON.stringify(s)))._uniCloudOptions=await t.platformGetSignOption();let r=await e({name:n,data:s});return t.isClientKeyNotFound(r)&&(await t.prepare({forceUpdate:!0}),s._uniCloudOptions=await t.platformGetSignOption(),r=await e({name:n,data:s})),r}}wrapEncryptDataCallFunction(e){const t=this;return async function({name:n,data:s={}}={}){await t.prepare();const r=await t.encryptData(s);let i=await e({name:n,data:r});if(t.isClientKeyNotFound(i)){await t.prepare({forceUpdate:!0});const r=await t.encryptData(s);s._uniCloudOptions=await t.platformGetSignOption(),i=await e({name:n,data:r})}return i.result=await t.decryptResult(i.result),i}}}
/*! MIT License. Copyright 2015-2018 Richard Moore <<EMAIL>>. See LICENSE.txt. */function Gt(e){return parseInt(e)===e}function Yt(e){if(!Gt(e.length))return!1;for(var t=0;t<e.length;t++)if(!Gt(e[t])||e[t]<0||e[t]>255)return!1;return!0}function Qt(e,t){if(e.buffer&&"Uint8Array"===e.name)return t&&(e=e.slice?e.slice():Array.prototype.slice.call(e)),e;if(Array.isArray(e)){if(!Yt(e))throw new Error("Array contains invalid value: "+e);return new Uint8Array(e)}if(Gt(e.length)&&Yt(e))return new Uint8Array(e);throw new Error("unsupported array-like object")}function Xt(e){return new Uint8Array(e)}function Zt(e,t,n,s,r){null==s&&null==r||(e=e.slice?e.slice(s,r):Array.prototype.slice.call(e,s,r)),t.set(e,n)}var en,tn={toBytes:function(e){var t=[],n=0;for(e=encodeURI(e);n<e.length;){var s=e.charCodeAt(n++);37===s?(t.push(parseInt(e.substr(n,2),16)),n+=2):t.push(s)}return Qt(t)},fromBytes:function(e){for(var t=[],n=0;n<e.length;){var s=e[n];s<128?(t.push(String.fromCharCode(s)),n++):s>191&&s<224?(t.push(String.fromCharCode((31&s)<<6|63&e[n+1])),n+=2):(t.push(String.fromCharCode((15&s)<<12|(63&e[n+1])<<6|63&e[n+2])),n+=3)}return t.join("")}},nn=(en="0123456789abcdef",{toBytes:function(e){for(var t=[],n=0;n<e.length;n+=2)t.push(parseInt(e.substr(n,2),16));return t},fromBytes:function(e){for(var t=[],n=0;n<e.length;n++){var s=e[n];t.push(en[(240&s)>>4]+en[15&s])}return t.join("")}}),sn={16:10,24:12,32:14},rn=[1,2,4,8,16,32,64,128,27,54,108,216,171,77,154,47,94,188,99,198,151,53,106,212,179,125,250,239,197,145],on=[99,124,119,123,242,107,111,197,48,1,103,43,254,215,171,118,202,130,201,125,250,89,71,240,173,212,162,175,156,164,114,192,183,253,147,38,54,63,247,204,52,165,229,241,113,216,49,21,4,199,35,195,24,150,5,154,7,18,128,226,235,39,178,117,9,131,44,26,27,110,90,160,82,59,214,179,41,227,47,132,83,209,0,237,32,252,177,91,106,203,190,57,74,76,88,207,208,239,170,251,67,77,51,133,69,249,2,127,80,60,159,168,81,163,64,143,146,157,56,245,188,182,218,33,16,255,243,210,205,12,19,236,95,151,68,23,196,167,126,61,100,93,25,115,96,129,79,220,34,42,144,136,70,238,184,20,222,94,11,219,224,50,58,10,73,6,36,92,194,211,172,98,145,149,228,121,231,200,55,109,141,213,78,169,108,86,244,234,101,122,174,8,186,120,37,46,28,166,180,198,232,221,116,31,75,189,139,138,112,62,181,102,72,3,246,14,97,53,87,185,134,193,29,158,225,248,152,17,105,217,142,148,155,30,135,233,206,85,40,223,140,161,137,13,191,230,66,104,65,153,45,15,176,84,187,22],an=[82,9,106,213,48,54,165,56,191,64,163,158,129,243,215,251,124,227,57,130,155,47,255,135,52,142,67,68,196,222,233,203,84,123,148,50,166,194,35,61,238,76,149,11,66,250,195,78,8,46,161,102,40,217,36,178,118,91,162,73,109,139,209,37,114,248,246,100,134,104,152,22,212,164,92,204,93,101,182,146,108,112,72,80,253,237,185,218,94,21,70,87,167,141,157,132,144,216,171,0,140,188,211,10,247,228,88,5,184,179,69,6,208,44,30,143,202,63,15,2,193,175,189,3,1,19,138,107,58,145,17,65,79,103,220,234,151,242,207,206,240,180,230,115,150,172,116,34,231,173,53,133,226,249,55,232,28,117,223,110,71,241,26,113,29,41,197,137,111,183,98,14,170,24,190,27,252,86,62,75,198,210,121,32,154,219,192,254,120,205,90,244,31,221,168,51,136,7,199,49,177,18,16,89,39,128,236,95,96,81,127,169,25,181,74,13,45,229,122,159,147,201,156,239,160,224,59,77,174,42,245,176,200,235,187,60,131,83,153,97,23,43,4,126,186,119,214,38,225,105,20,99,85,33,12,125],cn=[3328402341,4168907908,4000806809,4135287693,4294111757,3597364157,3731845041,2445657428,1613770832,33620227,3462883241,1445669757,3892248089,3050821474,1303096294,3967186586,2412431941,528646813,2311702848,4202528135,4026202645,2992200171,2387036105,4226871307,1101901292,3017069671,1604494077,1169141738,597466303,1403299063,3832705686,2613100635,1974974402,3791519004,1033081774,1277568618,1815492186,2118074177,4126668546,2211236943,1748251740,1369810420,3521504564,4193382664,3799085459,2883115123,1647391059,706024767,134480908,2512897874,1176707941,2646852446,806885416,932615841,168101135,798661301,235341577,605164086,461406363,3756188221,3454790438,1311188841,2142417613,3933566367,302582043,495158174,1479289972,874125870,907746093,3698224818,3025820398,1537253627,2756858614,1983593293,3084310113,2108928974,1378429307,3722699582,1580150641,327451799,2790478837,3117535592,0,3253595436,1075847264,3825007647,2041688520,3059440621,3563743934,2378943302,1740553945,1916352843,2487896798,2555137236,2958579944,2244988746,3151024235,3320835882,1336584933,3992714006,2252555205,2588757463,1714631509,293963156,2319795663,3925473552,67240454,4269768577,2689618160,2017213508,631218106,1269344483,2723238387,1571005438,2151694528,93294474,1066570413,563977660,1882732616,4059428100,1673313503,2008463041,2950355573,1109467491,537923632,3858759450,4260623118,3218264685,2177748300,403442708,638784309,3287084079,3193921505,899127202,2286175436,773265209,2479146071,1437050866,4236148354,2050833735,3362022572,3126681063,840505643,3866325909,3227541664,427917720,2655997905,2749160575,1143087718,1412049534,999329963,193497219,2353415882,3354324521,1807268051,672404540,2816401017,3160301282,369822493,2916866934,3688947771,1681011286,1949973070,336202270,2454276571,201721354,1210328172,3093060836,2680341085,3184776046,1135389935,3294782118,965841320,831886756,3554993207,4068047243,3588745010,2345191491,1849112409,3664604599,26054028,2983581028,2622377682,1235855840,3630984372,2891339514,4092916743,3488279077,3395642799,4101667470,1202630377,268961816,1874508501,4034427016,1243948399,1546530418,941366308,1470539505,1941222599,2546386513,3421038627,2715671932,3899946140,1042226977,2521517021,1639824860,227249030,260737669,3765465232,2084453954,1907733956,3429263018,2420656344,100860677,4160157185,470683154,3261161891,1781871967,2924959737,1773779408,394692241,2579611992,974986535,664706745,3655459128,3958962195,731420851,571543859,3530123707,2849626480,126783113,865375399,765172662,1008606754,361203602,3387549984,2278477385,2857719295,1344809080,2782912378,59542671,1503764984,160008576,437062935,1707065306,3622233649,2218934982,3496503480,2185314755,697932208,1512910199,504303377,2075177163,2824099068,1841019862,739644986],un=[2781242211,2230877308,2582542199,2381740923,234877682,3184946027,2984144751,1418839493,1348481072,50462977,2848876391,2102799147,434634494,1656084439,3863849899,2599188086,1167051466,2636087938,1082771913,2281340285,368048890,3954334041,3381544775,201060592,3963727277,1739838676,4250903202,3930435503,3206782108,4149453988,2531553906,1536934080,3262494647,484572669,2923271059,1783375398,1517041206,1098792767,49674231,1334037708,1550332980,4098991525,886171109,150598129,2481090929,1940642008,1398944049,1059722517,201851908,1385547719,1699095331,1587397571,674240536,2704774806,252314885,3039795866,151914247,908333586,2602270848,1038082786,651029483,1766729511,3447698098,2682942837,454166793,2652734339,1951935532,775166490,758520603,3000790638,4004797018,4217086112,4137964114,1299594043,1639438038,3464344499,2068982057,1054729187,1901997871,2534638724,4121318227,1757008337,0,750906861,1614815264,535035132,3363418545,3988151131,3201591914,1183697867,3647454910,1265776953,3734260298,3566750796,3903871064,1250283471,1807470800,717615087,3847203498,384695291,3313910595,3617213773,1432761139,2484176261,3481945413,283769337,100925954,2180939647,4037038160,1148730428,3123027871,3813386408,4087501137,4267549603,3229630528,2315620239,2906624658,3156319645,1215313976,82966005,3747855548,3245848246,1974459098,1665278241,807407632,451280895,251524083,1841287890,1283575245,337120268,891687699,801369324,3787349855,2721421207,3431482436,959321879,1469301956,4065699751,2197585534,1199193405,2898814052,3887750493,724703513,2514908019,2696962144,2551808385,3516813135,2141445340,1715741218,2119445034,2872807568,2198571144,3398190662,700968686,3547052216,1009259540,2041044702,3803995742,487983883,1991105499,1004265696,1449407026,1316239930,504629770,3683797321,168560134,1816667172,3837287516,1570751170,1857934291,4014189740,2797888098,2822345105,2754712981,936633572,2347923833,852879335,1133234376,1500395319,3084545389,2348912013,1689376213,3533459022,3762923945,3034082412,4205598294,133428468,634383082,2949277029,2398386810,3913789102,403703816,3580869306,2297460856,1867130149,1918643758,607656988,4049053350,3346248884,1368901318,600565992,2090982877,2632479860,557719327,3717614411,3697393085,2249034635,2232388234,2430627952,1115438654,3295786421,2865522278,3633334344,84280067,33027830,303828494,2747425121,1600795957,4188952407,3496589753,2434238086,1486471617,658119965,3106381470,953803233,334231800,3005978776,857870609,3151128937,1890179545,2298973838,2805175444,3056442267,574365214,2450884487,550103529,1233637070,4289353045,2018519080,2057691103,2399374476,4166623649,2148108681,387583245,3664101311,836232934,3330556482,3100665960,3280093505,2955516313,2002398509,287182607,3413881008,4238890068,3597515707,975967766],ln=[1671808611,2089089148,2006576759,2072901243,4061003762,1807603307,1873927791,3310653893,810573872,16974337,1739181671,729634347,4263110654,3613570519,2883997099,1989864566,3393556426,2191335298,3376449993,2106063485,4195741690,1508618841,1204391495,4027317232,2917941677,3563566036,2734514082,2951366063,2629772188,2767672228,1922491506,3227229120,3082974647,4246528509,2477669779,644500518,911895606,1061256767,4144166391,3427763148,878471220,2784252325,3845444069,4043897329,1905517169,3631459288,827548209,356461077,67897348,3344078279,593839651,3277757891,405286936,2527147926,84871685,2595565466,118033927,305538066,2157648768,3795705826,3945188843,661212711,2999812018,1973414517,152769033,2208177539,745822252,439235610,455947803,1857215598,1525593178,2700827552,1391895634,994932283,3596728278,3016654259,695947817,3812548067,795958831,2224493444,1408607827,3513301457,0,3979133421,543178784,4229948412,2982705585,1542305371,1790891114,3410398667,3201918910,961245753,1256100938,1289001036,1491644504,3477767631,3496721360,4012557807,2867154858,4212583931,1137018435,1305975373,861234739,2241073541,1171229253,4178635257,33948674,2139225727,1357946960,1011120188,2679776671,2833468328,1374921297,2751356323,1086357568,2408187279,2460827538,2646352285,944271416,4110742005,3168756668,3066132406,3665145818,560153121,271589392,4279952895,4077846003,3530407890,3444343245,202643468,322250259,3962553324,1608629855,2543990167,1154254916,389623319,3294073796,2817676711,2122513534,1028094525,1689045092,1575467613,422261273,1939203699,1621147744,2174228865,1339137615,3699352540,577127458,712922154,2427141008,2290289544,1187679302,3995715566,3100863416,339486740,3732514782,1591917662,186455563,3681988059,3762019296,844522546,978220090,169743370,1239126601,101321734,611076132,1558493276,3260915650,3547250131,2901361580,1655096418,2443721105,2510565781,3828863972,2039214713,3878868455,3359869896,928607799,1840765549,2374762893,3580146133,1322425422,2850048425,1823791212,1459268694,4094161908,3928346602,1706019429,2056189050,2934523822,135794696,3134549946,2022240376,628050469,779246638,472135708,2800834470,3032970164,3327236038,3894660072,3715932637,1956440180,522272287,1272813131,3185336765,2340818315,2323976074,1888542832,1044544574,3049550261,1722469478,1222152264,50660867,4127324150,236067854,1638122081,895445557,1475980887,3117443513,2257655686,3243809217,489110045,2662934430,3778599393,4162055160,2561878936,288563729,1773916777,3648039385,2391345038,2493985684,2612407707,505560094,2274497927,3911240169,3460925390,1442818645,678973480,3749357023,2358182796,2717407649,2306869641,219617805,3218761151,3862026214,1120306242,1756942440,1103331905,2578459033,762796589,252780047,2966125488,1425844308,3151392187,372911126],hn=[1667474886,2088535288,2004326894,2071694838,4075949567,1802223062,1869591006,3318043793,808472672,16843522,1734846926,724270422,4278065639,3621216949,2880169549,1987484396,3402253711,2189597983,3385409673,2105378810,4210693615,1499065266,1195886990,4042263547,2913856577,3570689971,2728590687,2947541573,2627518243,2762274643,1920112356,3233831835,3082273397,4261223649,2475929149,640051788,909531756,1061110142,4160160501,3435941763,875846760,2779116625,3857003729,4059105529,1903268834,3638064043,825316194,353713962,67374088,3351728789,589522246,3284360861,404236336,2526454071,84217610,2593830191,117901582,303183396,2155911963,3806477791,3958056653,656894286,2998062463,1970642922,151591698,2206440989,741110872,437923380,454765878,1852748508,1515908788,2694904667,1381168804,993742198,3604373943,3014905469,690584402,3823320797,791638366,2223281939,1398011302,3520161977,0,3991743681,538992704,4244381667,2981218425,1532751286,1785380564,3419096717,3200178535,960056178,1246420628,1280103576,1482221744,3486468741,3503319995,4025428677,2863326543,4227536621,1128514950,1296947098,859002214,2240123921,1162203018,4193849577,33687044,2139062782,1347481760,1010582648,2678045221,2829640523,1364325282,2745433693,1077985408,2408548869,2459086143,2644360225,943212656,4126475505,3166494563,3065430391,3671750063,555836226,269496352,4294908645,4092792573,3537006015,3452783745,202118168,320025894,3974901699,1600119230,2543297077,1145359496,387397934,3301201811,2812801621,2122220284,1027426170,1684319432,1566435258,421079858,1936954854,1616945344,2172753945,1330631070,3705438115,572679748,707427924,2425400123,2290647819,1179044492,4008585671,3099120491,336870440,3739122087,1583276732,185277718,3688593069,3772791771,842159716,976899700,168435220,1229577106,101059084,606366792,1549591736,3267517855,3553849021,2897014595,1650632388,2442242105,2509612081,3840161747,2038008818,3890688725,3368567691,926374254,1835907034,2374863873,3587531953,1313788572,2846482505,1819063512,1448540844,4109633523,3941213647,1701162954,2054852340,2930698567,134748176,3132806511,2021165296,623210314,774795868,471606328,2795958615,3031746419,3334885783,3907527627,3722280097,1953799400,522133822,1263263126,3183336545,2341176845,2324333839,1886425312,1044267644,3048588401,1718004428,1212733584,50529542,4143317495,235803164,1633788866,892690282,1465383342,3115962473,2256965911,3250673817,488449850,2661202215,3789633753,4177007595,2560144171,286339874,1768537042,3654906025,2391705863,2492770099,2610673197,505291324,2273808917,3924369609,3469625735,1431699370,673740880,3755965093,2358021891,2711746649,2307489801,218961690,3217021541,3873845719,1111672452,1751693520,1094828930,2576986153,757954394,252645662,2964376443,1414855848,3149649517,370555436],dn=[1374988112,2118214995,437757123,975658646,1001089995,530400753,2902087851,1273168787,540080725,2910219766,2295101073,4110568485,1340463100,3307916247,641025152,3043140495,3736164937,632953703,1172967064,1576976609,3274667266,2169303058,2370213795,1809054150,59727847,361929877,3211623147,2505202138,3569255213,1484005843,1239443753,2395588676,1975683434,4102977912,2572697195,666464733,3202437046,4035489047,3374361702,2110667444,1675577880,3843699074,2538681184,1649639237,2976151520,3144396420,4269907996,4178062228,1883793496,2403728665,2497604743,1383856311,2876494627,1917518562,3810496343,1716890410,3001755655,800440835,2261089178,3543599269,807962610,599762354,33778362,3977675356,2328828971,2809771154,4077384432,1315562145,1708848333,101039829,3509871135,3299278474,875451293,2733856160,92987698,2767645557,193195065,1080094634,1584504582,3178106961,1042385657,2531067453,3711829422,1306967366,2438237621,1908694277,67556463,1615861247,429456164,3602770327,2302690252,1742315127,2968011453,126454664,3877198648,2043211483,2709260871,2084704233,4169408201,0,159417987,841739592,504459436,1817866830,4245618683,260388950,1034867998,908933415,168810852,1750902305,2606453969,607530554,202008497,2472011535,3035535058,463180190,2160117071,1641816226,1517767529,470948374,3801332234,3231722213,1008918595,303765277,235474187,4069246893,766945465,337553864,1475418501,2943682380,4003061179,2743034109,4144047775,1551037884,1147550661,1543208500,2336434550,3408119516,3069049960,3102011747,3610369226,1113818384,328671808,2227573024,2236228733,3535486456,2935566865,3341394285,496906059,3702665459,226906860,2009195472,733156972,2842737049,294930682,1206477858,2835123396,2700099354,1451044056,573804783,2269728455,3644379585,2362090238,2564033334,2801107407,2776292904,3669462566,1068351396,742039012,1350078989,1784663195,1417561698,4136440770,2430122216,775550814,2193862645,2673705150,1775276924,1876241833,3475313331,3366754619,270040487,3902563182,3678124923,3441850377,1851332852,3969562369,2203032232,3868552805,2868897406,566021896,4011190502,3135740889,1248802510,3936291284,699432150,832877231,708780849,3332740144,899835584,1951317047,4236429990,3767586992,866637845,4043610186,1106041591,2144161806,395441711,1984812685,1139781709,3433712980,3835036895,2664543715,1282050075,3240894392,1181045119,2640243204,25965917,4203181171,4211818798,3009879386,2463879762,3910161971,1842759443,2597806476,933301370,1509430414,3943906441,3467192302,3076639029,3776767469,2051518780,2631065433,1441952575,404016761,1942435775,1408749034,1610459739,3745345300,2017778566,3400528769,3110650942,941896748,3265478751,371049330,3168937228,675039627,4279080257,967311729,135050206,3635733660,1683407248,2076935265,3576870512,1215061108,3501741890],pn=[1347548327,1400783205,3273267108,2520393566,3409685355,4045380933,2880240216,2471224067,1428173050,4138563181,2441661558,636813900,4233094615,3620022987,2149987652,2411029155,1239331162,1730525723,2554718734,3781033664,46346101,310463728,2743944855,3328955385,3875770207,2501218972,3955191162,3667219033,768917123,3545789473,692707433,1150208456,1786102409,2029293177,1805211710,3710368113,3065962831,401639597,1724457132,3028143674,409198410,2196052529,1620529459,1164071807,3769721975,2226875310,486441376,2499348523,1483753576,428819965,2274680428,3075636216,598438867,3799141122,1474502543,711349675,129166120,53458370,2592523643,2782082824,4063242375,2988687269,3120694122,1559041666,730517276,2460449204,4042459122,2706270690,3446004468,3573941694,533804130,2328143614,2637442643,2695033685,839224033,1973745387,957055980,2856345839,106852767,1371368976,4181598602,1033297158,2933734917,1179510461,3046200461,91341917,1862534868,4284502037,605657339,2547432937,3431546947,2003294622,3182487618,2282195339,954669403,3682191598,1201765386,3917234703,3388507166,0,2198438022,1211247597,2887651696,1315723890,4227665663,1443857720,507358933,657861945,1678381017,560487590,3516619604,975451694,2970356327,261314535,3535072918,2652609425,1333838021,2724322336,1767536459,370938394,182621114,3854606378,1128014560,487725847,185469197,2918353863,3106780840,3356761769,2237133081,1286567175,3152976349,4255350624,2683765030,3160175349,3309594171,878443390,1988838185,3704300486,1756818940,1673061617,3403100636,272786309,1075025698,545572369,2105887268,4174560061,296679730,1841768865,1260232239,4091327024,3960309330,3497509347,1814803222,2578018489,4195456072,575138148,3299409036,446754879,3629546796,4011996048,3347532110,3252238545,4270639778,915985419,3483825537,681933534,651868046,2755636671,3828103837,223377554,2607439820,1649704518,3270937875,3901806776,1580087799,4118987695,3198115200,2087309459,2842678573,3016697106,1003007129,2802849917,1860738147,2077965243,164439672,4100872472,32283319,2827177882,1709610350,2125135846,136428751,3874428392,3652904859,3460984630,3572145929,3593056380,2939266226,824852259,818324884,3224740454,930369212,2801566410,2967507152,355706840,1257309336,4148292826,243256656,790073846,2373340630,1296297904,1422699085,3756299780,3818836405,457992840,3099667487,2135319889,77422314,1560382517,1945798516,788204353,1521706781,1385356242,870912086,325965383,2358957921,2050466060,2388260884,2313884476,4006521127,901210569,3990953189,1014646705,1503449823,1062597235,2031621326,3212035895,3931371469,1533017514,350174575,2256028891,2177544179,1052338372,741876788,1606591296,1914052035,213705253,2334669897,1107234197,1899603969,3725069491,2631447780,2422494913,1635502980,1893020342,1950903388,1120974935],fn=[2807058932,1699970625,2764249623,1586903591,1808481195,1173430173,1487645946,59984867,4199882800,1844882806,1989249228,1277555970,3623636965,3419915562,1149249077,2744104290,1514790577,459744698,244860394,3235995134,1963115311,4027744588,2544078150,4190530515,1608975247,2627016082,2062270317,1507497298,2200818878,567498868,1764313568,3359936201,2305455554,2037970062,1047239e3,1910319033,1337376481,2904027272,2892417312,984907214,1243112415,830661914,861968209,2135253587,2011214180,2927934315,2686254721,731183368,1750626376,4246310725,1820824798,4172763771,3542330227,48394827,2404901663,2871682645,671593195,3254988725,2073724613,145085239,2280796200,2779915199,1790575107,2187128086,472615631,3029510009,4075877127,3802222185,4107101658,3201631749,1646252340,4270507174,1402811438,1436590835,3778151818,3950355702,3963161475,4020912224,2667994737,273792366,2331590177,104699613,95345982,3175501286,2377486676,1560637892,3564045318,369057872,4213447064,3919042237,1137477952,2658625497,1119727848,2340947849,1530455833,4007360968,172466556,266959938,516552836,0,2256734592,3980931627,1890328081,1917742170,4294704398,945164165,3575528878,958871085,3647212047,2787207260,1423022939,775562294,1739656202,3876557655,2530391278,2443058075,3310321856,547512796,1265195639,437656594,3121275539,719700128,3762502690,387781147,218828297,3350065803,2830708150,2848461854,428169201,122466165,3720081049,1627235199,648017665,4122762354,1002783846,2117360635,695634755,3336358691,4234721005,4049844452,3704280881,2232435299,574624663,287343814,612205898,1039717051,840019705,2708326185,793451934,821288114,1391201670,3822090177,376187827,3113855344,1224348052,1679968233,2361698556,1058709744,752375421,2431590963,1321699145,3519142200,2734591178,188127444,2177869557,3727205754,2384911031,3215212461,2648976442,2450346104,3432737375,1180849278,331544205,3102249176,4150144569,2952102595,2159976285,2474404304,766078933,313773861,2570832044,2108100632,1668212892,3145456443,2013908262,418672217,3070356634,2594734927,1852171925,3867060991,3473416636,3907448597,2614737639,919489135,164948639,2094410160,2997825956,590424639,2486224549,1723872674,3157750862,3399941250,3501252752,3625268135,2555048196,3673637356,1343127501,4130281361,3599595085,2957853679,1297403050,81781910,3051593425,2283490410,532201772,1367295589,3926170974,895287692,1953757831,1093597963,492483431,3528626907,1446242576,1192455638,1636604631,209336225,344873464,1015671571,669961897,3375740769,3857572124,2973530695,3747192018,1933530610,3464042516,935293895,3454686199,2858115069,1863638845,3683022916,4085369519,3292445032,875313188,1080017571,3279033885,621591778,1233856572,2504130317,24197544,3017672716,3835484340,3247465558,2220981195,3060847922,1551124588,1463996600],gn=[4104605777,1097159550,396673818,660510266,2875968315,2638606623,4200115116,3808662347,821712160,1986918061,3430322568,38544885,3856137295,718002117,893681702,1654886325,2975484382,3122358053,3926825029,4274053469,796197571,1290801793,1184342925,3556361835,2405426947,2459735317,1836772287,1381620373,3196267988,1948373848,3764988233,3385345166,3263785589,2390325492,1480485785,3111247143,3780097726,2293045232,548169417,3459953789,3746175075,439452389,1362321559,1400849762,1685577905,1806599355,2174754046,137073913,1214797936,1174215055,3731654548,2079897426,1943217067,1258480242,529487843,1437280870,3945269170,3049390895,3313212038,923313619,679998e3,3215307299,57326082,377642221,3474729866,2041877159,133361907,1776460110,3673476453,96392454,878845905,2801699524,777231668,4082475170,2330014213,4142626212,2213296395,1626319424,1906247262,1846563261,562755902,3708173718,1040559837,3871163981,1418573201,3294430577,114585348,1343618912,2566595609,3186202582,1078185097,3651041127,3896688048,2307622919,425408743,3371096953,2081048481,1108339068,2216610296,0,2156299017,736970802,292596766,1517440620,251657213,2235061775,2933202493,758720310,265905162,1554391400,1532285339,908999204,174567692,1474760595,4002861748,2610011675,3234156416,3693126241,2001430874,303699484,2478443234,2687165888,585122620,454499602,151849742,2345119218,3064510765,514443284,4044981591,1963412655,2581445614,2137062819,19308535,1928707164,1715193156,4219352155,1126790795,600235211,3992742070,3841024952,836553431,1669664834,2535604243,3323011204,1243905413,3141400786,4180808110,698445255,2653899549,2989552604,2253581325,3252932727,3004591147,1891211689,2487810577,3915653703,4237083816,4030667424,2100090966,865136418,1229899655,953270745,3399679628,3557504664,4118925222,2061379749,3079546586,2915017791,983426092,2022837584,1607244650,2118541908,2366882550,3635996816,972512814,3283088770,1568718495,3499326569,3576539503,621982671,2895723464,410887952,2623762152,1002142683,645401037,1494807662,2595684844,1335535747,2507040230,4293295786,3167684641,367585007,3885750714,1865862730,2668221674,2960971305,2763173681,1059270954,2777952454,2724642869,1320957812,2194319100,2429595872,2815956275,77089521,3973773121,3444575871,2448830231,1305906550,4021308739,2857194700,2516901860,3518358430,1787304780,740276417,1699839814,1592394909,2352307457,2272556026,188821243,1729977011,3687994002,274084841,3594982253,3613494426,2701949495,4162096729,322734571,2837966542,1640576439,484830689,1202797690,3537852828,4067639125,349075736,3342319475,4157467219,4255800159,1030690015,1155237496,2951971274,1757691577,607398968,2738905026,499347990,3794078908,1011452712,227885567,2818666809,213114376,3034881240,1455525988,3414450555,850817237,1817998408,3092726480],mn=[0,235474187,470948374,303765277,941896748,908933415,607530554,708780849,1883793496,2118214995,1817866830,1649639237,1215061108,1181045119,1417561698,1517767529,3767586992,4003061179,4236429990,4069246893,3635733660,3602770327,3299278474,3400528769,2430122216,2664543715,2362090238,2193862645,2835123396,2801107407,3035535058,3135740889,3678124923,3576870512,3341394285,3374361702,3810496343,3977675356,4279080257,4043610186,2876494627,2776292904,3076639029,3110650942,2472011535,2640243204,2403728665,2169303058,1001089995,899835584,666464733,699432150,59727847,226906860,530400753,294930682,1273168787,1172967064,1475418501,1509430414,1942435775,2110667444,1876241833,1641816226,2910219766,2743034109,2976151520,3211623147,2505202138,2606453969,2302690252,2269728455,3711829422,3543599269,3240894392,3475313331,3843699074,3943906441,4178062228,4144047775,1306967366,1139781709,1374988112,1610459739,1975683434,2076935265,1775276924,1742315127,1034867998,866637845,566021896,800440835,92987698,193195065,429456164,395441711,1984812685,2017778566,1784663195,1683407248,1315562145,1080094634,1383856311,1551037884,101039829,135050206,437757123,337553864,1042385657,807962610,573804783,742039012,2531067453,2564033334,2328828971,2227573024,2935566865,2700099354,3001755655,3168937228,3868552805,3902563182,4203181171,4102977912,3736164937,3501741890,3265478751,3433712980,1106041591,1340463100,1576976609,1408749034,2043211483,2009195472,1708848333,1809054150,832877231,1068351396,766945465,599762354,159417987,126454664,361929877,463180190,2709260871,2943682380,3178106961,3009879386,2572697195,2538681184,2236228733,2336434550,3509871135,3745345300,3441850377,3274667266,3910161971,3877198648,4110568485,4211818798,2597806476,2497604743,2261089178,2295101073,2733856160,2902087851,3202437046,2968011453,3936291284,3835036895,4136440770,4169408201,3535486456,3702665459,3467192302,3231722213,2051518780,1951317047,1716890410,1750902305,1113818384,1282050075,1584504582,1350078989,168810852,67556463,371049330,404016761,841739592,1008918595,775550814,540080725,3969562369,3801332234,4035489047,4269907996,3569255213,3669462566,3366754619,3332740144,2631065433,2463879762,2160117071,2395588676,2767645557,2868897406,3102011747,3069049960,202008497,33778362,270040487,504459436,875451293,975658646,675039627,641025152,2084704233,1917518562,1615861247,1851332852,1147550661,1248802510,1484005843,1451044056,933301370,967311729,733156972,632953703,260388950,25965917,328671808,496906059,1206477858,1239443753,1543208500,1441952575,2144161806,1908694277,1675577880,1842759443,3610369226,3644379585,3408119516,3307916247,4011190502,3776767469,4077384432,4245618683,2809771154,2842737049,3144396420,3043140495,2673705150,2438237621,2203032232,2370213795],yn=[0,185469197,370938394,487725847,741876788,657861945,975451694,824852259,1483753576,1400783205,1315723890,1164071807,1950903388,2135319889,1649704518,1767536459,2967507152,3152976349,2801566410,2918353863,2631447780,2547432937,2328143614,2177544179,3901806776,3818836405,4270639778,4118987695,3299409036,3483825537,3535072918,3652904859,2077965243,1893020342,1841768865,1724457132,1474502543,1559041666,1107234197,1257309336,598438867,681933534,901210569,1052338372,261314535,77422314,428819965,310463728,3409685355,3224740454,3710368113,3593056380,3875770207,3960309330,4045380933,4195456072,2471224067,2554718734,2237133081,2388260884,3212035895,3028143674,2842678573,2724322336,4138563181,4255350624,3769721975,3955191162,3667219033,3516619604,3431546947,3347532110,2933734917,2782082824,3099667487,3016697106,2196052529,2313884476,2499348523,2683765030,1179510461,1296297904,1347548327,1533017514,1786102409,1635502980,2087309459,2003294622,507358933,355706840,136428751,53458370,839224033,957055980,605657339,790073846,2373340630,2256028891,2607439820,2422494913,2706270690,2856345839,3075636216,3160175349,3573941694,3725069491,3273267108,3356761769,4181598602,4063242375,4011996048,3828103837,1033297158,915985419,730517276,545572369,296679730,446754879,129166120,213705253,1709610350,1860738147,1945798516,2029293177,1239331162,1120974935,1606591296,1422699085,4148292826,4233094615,3781033664,3931371469,3682191598,3497509347,3446004468,3328955385,2939266226,2755636671,3106780840,2988687269,2198438022,2282195339,2501218972,2652609425,1201765386,1286567175,1371368976,1521706781,1805211710,1620529459,2105887268,1988838185,533804130,350174575,164439672,46346101,870912086,954669403,636813900,788204353,2358957921,2274680428,2592523643,2441661558,2695033685,2880240216,3065962831,3182487618,3572145929,3756299780,3270937875,3388507166,4174560061,4091327024,4006521127,3854606378,1014646705,930369212,711349675,560487590,272786309,457992840,106852767,223377554,1678381017,1862534868,1914052035,2031621326,1211247597,1128014560,1580087799,1428173050,32283319,182621114,401639597,486441376,768917123,651868046,1003007129,818324884,1503449823,1385356242,1333838021,1150208456,1973745387,2125135846,1673061617,1756818940,2970356327,3120694122,2802849917,2887651696,2637442643,2520393566,2334669897,2149987652,3917234703,3799141122,4284502037,4100872472,3309594171,3460984630,3545789473,3629546796,2050466060,1899603969,1814803222,1730525723,1443857720,1560382517,1075025698,1260232239,575138148,692707433,878443390,1062597235,243256656,91341917,409198410,325965383,3403100636,3252238545,3704300486,3620022987,3874428392,3990953189,4042459122,4227665663,2460449204,2578018489,2226875310,2411029155,3198115200,3046200461,2827177882,2743944855],_n=[0,218828297,437656594,387781147,875313188,958871085,775562294,590424639,1750626376,1699970625,1917742170,2135253587,1551124588,1367295589,1180849278,1265195639,3501252752,3720081049,3399941250,3350065803,3835484340,3919042237,4270507174,4085369519,3102249176,3051593425,2734591178,2952102595,2361698556,2177869557,2530391278,2614737639,3145456443,3060847922,2708326185,2892417312,2404901663,2187128086,2504130317,2555048196,3542330227,3727205754,3375740769,3292445032,3876557655,3926170974,4246310725,4027744588,1808481195,1723872674,1910319033,2094410160,1608975247,1391201670,1173430173,1224348052,59984867,244860394,428169201,344873464,935293895,984907214,766078933,547512796,1844882806,1627235199,2011214180,2062270317,1507497298,1423022939,1137477952,1321699145,95345982,145085239,532201772,313773861,830661914,1015671571,731183368,648017665,3175501286,2957853679,2807058932,2858115069,2305455554,2220981195,2474404304,2658625497,3575528878,3625268135,3473416636,3254988725,3778151818,3963161475,4213447064,4130281361,3599595085,3683022916,3432737375,3247465558,3802222185,4020912224,4172763771,4122762354,3201631749,3017672716,2764249623,2848461854,2331590177,2280796200,2431590963,2648976442,104699613,188127444,472615631,287343814,840019705,1058709744,671593195,621591778,1852171925,1668212892,1953757831,2037970062,1514790577,1463996600,1080017571,1297403050,3673637356,3623636965,3235995134,3454686199,4007360968,3822090177,4107101658,4190530515,2997825956,3215212461,2830708150,2779915199,2256734592,2340947849,2627016082,2443058075,172466556,122466165,273792366,492483431,1047239e3,861968209,612205898,695634755,1646252340,1863638845,2013908262,1963115311,1446242576,1530455833,1277555970,1093597963,1636604631,1820824798,2073724613,1989249228,1436590835,1487645946,1337376481,1119727848,164948639,81781910,331544205,516552836,1039717051,821288114,669961897,719700128,2973530695,3157750862,2871682645,2787207260,2232435299,2283490410,2667994737,2450346104,3647212047,3564045318,3279033885,3464042516,3980931627,3762502690,4150144569,4199882800,3070356634,3121275539,2904027272,2686254721,2200818878,2384911031,2570832044,2486224549,3747192018,3528626907,3310321856,3359936201,3950355702,3867060991,4049844452,4234721005,1739656202,1790575107,2108100632,1890328081,1402811438,1586903591,1233856572,1149249077,266959938,48394827,369057872,418672217,1002783846,919489135,567498868,752375421,209336225,24197544,376187827,459744698,945164165,895287692,574624663,793451934,1679968233,1764313568,2117360635,1933530610,1343127501,1560637892,1243112415,1192455638,3704280881,3519142200,3336358691,3419915562,3907448597,3857572124,4075877127,4294704398,3029510009,3113855344,2927934315,2744104290,2159976285,2377486676,2594734927,2544078150],wn=[0,151849742,303699484,454499602,607398968,758720310,908999204,1059270954,1214797936,1097159550,1517440620,1400849762,1817998408,1699839814,2118541908,2001430874,2429595872,2581445614,2194319100,2345119218,3034881240,3186202582,2801699524,2951971274,3635996816,3518358430,3399679628,3283088770,4237083816,4118925222,4002861748,3885750714,1002142683,850817237,698445255,548169417,529487843,377642221,227885567,77089521,1943217067,2061379749,1640576439,1757691577,1474760595,1592394909,1174215055,1290801793,2875968315,2724642869,3111247143,2960971305,2405426947,2253581325,2638606623,2487810577,3808662347,3926825029,4044981591,4162096729,3342319475,3459953789,3576539503,3693126241,1986918061,2137062819,1685577905,1836772287,1381620373,1532285339,1078185097,1229899655,1040559837,923313619,740276417,621982671,439452389,322734571,137073913,19308535,3871163981,4021308739,4104605777,4255800159,3263785589,3414450555,3499326569,3651041127,2933202493,2815956275,3167684641,3049390895,2330014213,2213296395,2566595609,2448830231,1305906550,1155237496,1607244650,1455525988,1776460110,1626319424,2079897426,1928707164,96392454,213114376,396673818,514443284,562755902,679998e3,865136418,983426092,3708173718,3557504664,3474729866,3323011204,4180808110,4030667424,3945269170,3794078908,2507040230,2623762152,2272556026,2390325492,2975484382,3092726480,2738905026,2857194700,3973773121,3856137295,4274053469,4157467219,3371096953,3252932727,3673476453,3556361835,2763173681,2915017791,3064510765,3215307299,2156299017,2307622919,2459735317,2610011675,2081048481,1963412655,1846563261,1729977011,1480485785,1362321559,1243905413,1126790795,878845905,1030690015,645401037,796197571,274084841,425408743,38544885,188821243,3613494426,3731654548,3313212038,3430322568,4082475170,4200115116,3780097726,3896688048,2668221674,2516901860,2366882550,2216610296,3141400786,2989552604,2837966542,2687165888,1202797690,1320957812,1437280870,1554391400,1669664834,1787304780,1906247262,2022837584,265905162,114585348,499347990,349075736,736970802,585122620,972512814,821712160,2595684844,2478443234,2293045232,2174754046,3196267988,3079546586,2895723464,2777952454,3537852828,3687994002,3234156416,3385345166,4142626212,4293295786,3841024952,3992742070,174567692,57326082,410887952,292596766,777231668,660510266,1011452712,893681702,1108339068,1258480242,1343618912,1494807662,1715193156,1865862730,1948373848,2100090966,2701949495,2818666809,3004591147,3122358053,2235061775,2352307457,2535604243,2653899549,3915653703,3764988233,4219352155,4067639125,3444575871,3294430577,3746175075,3594982253,836553431,953270745,600235211,718002117,367585007,484830689,133361907,251657213,2041877159,1891211689,1806599355,1654886325,1568718495,1418573201,1335535747,1184342925];function vn(e){for(var t=[],n=0;n<e.length;n+=4)t.push(e[n]<<24|e[n+1]<<16|e[n+2]<<8|e[n+3]);return t}class In{constructor(e){if(!(this instanceof In))throw Error("AES must be instanitated with `new`");Object.defineProperty(this,"key",{value:Qt(e,!0)}),this._prepare()}_prepare(){var e=sn[this.key.length];if(null==e)throw new Error("invalid key size (must be 16, 24 or 32 bytes)");this._Ke=[],this._Kd=[];for(var t=0;t<=e;t++)this._Ke.push([0,0,0,0]),this._Kd.push([0,0,0,0]);var n,s=4*(e+1),r=this.key.length/4,i=vn(this.key);for(t=0;t<r;t++)n=t>>2,this._Ke[n][t%4]=i[t],this._Kd[e-n][t%4]=i[t];for(var o,a=0,c=r;c<s;){if(o=i[r-1],i[0]^=on[o>>16&255]<<24^on[o>>8&255]<<16^on[255&o]<<8^on[o>>24&255]^rn[a]<<24,a+=1,8!=r)for(t=1;t<r;t++)i[t]^=i[t-1];else{for(t=1;t<r/2;t++)i[t]^=i[t-1];o=i[r/2-1],i[r/2]^=on[255&o]^on[o>>8&255]<<8^on[o>>16&255]<<16^on[o>>24&255]<<24;for(t=r/2+1;t<r;t++)i[t]^=i[t-1]}for(t=0;t<r&&c<s;)u=c>>2,l=c%4,this._Ke[u][l]=i[t],this._Kd[e-u][l]=i[t++],c++}for(var u=1;u<e;u++)for(var l=0;l<4;l++)o=this._Kd[u][l],this._Kd[u][l]=mn[o>>24&255]^yn[o>>16&255]^_n[o>>8&255]^wn[255&o]}encrypt(e){if(16!=e.length)throw new Error("invalid plaintext size (must be 16 bytes)");for(var t=this._Ke.length-1,n=[0,0,0,0],s=vn(e),r=0;r<4;r++)s[r]^=this._Ke[0][r];for(var i=1;i<t;i++){for(r=0;r<4;r++)n[r]=cn[s[r]>>24&255]^un[s[(r+1)%4]>>16&255]^ln[s[(r+2)%4]>>8&255]^hn[255&s[(r+3)%4]]^this._Ke[i][r];s=n.slice()}var o,a=Xt(16);for(r=0;r<4;r++)o=this._Ke[t][r],a[4*r]=255&(on[s[r]>>24&255]^o>>24),a[4*r+1]=255&(on[s[(r+1)%4]>>16&255]^o>>16),a[4*r+2]=255&(on[s[(r+2)%4]>>8&255]^o>>8),a[4*r+3]=255&(on[255&s[(r+3)%4]]^o);return a}decrypt(e){if(16!=e.length)throw new Error("invalid ciphertext size (must be 16 bytes)");for(var t=this._Kd.length-1,n=[0,0,0,0],s=vn(e),r=0;r<4;r++)s[r]^=this._Kd[0][r];for(var i=1;i<t;i++){for(r=0;r<4;r++)n[r]=dn[s[r]>>24&255]^pn[s[(r+3)%4]>>16&255]^fn[s[(r+2)%4]>>8&255]^gn[255&s[(r+1)%4]]^this._Kd[i][r];s=n.slice()}var o,a=Xt(16);for(r=0;r<4;r++)o=this._Kd[t][r],a[4*r]=255&(an[s[r]>>24&255]^o>>24),a[4*r+1]=255&(an[s[(r+3)%4]>>16&255]^o>>16),a[4*r+2]=255&(an[s[(r+2)%4]>>8&255]^o>>8),a[4*r+3]=255&(an[255&s[(r+1)%4]]^o);return a}}class Sn{constructor(e){if(!(this instanceof Sn))throw Error("AES must be instanitated with `new`");this.description="Electronic Code Block",this.name="ecb",this._aes=new In(e)}encrypt(e){if((e=Qt(e)).length%16!=0)throw new Error("invalid plaintext size (must be multiple of 16 bytes)");for(var t=Xt(e.length),n=Xt(16),s=0;s<e.length;s+=16)Zt(e,n,0,s,s+16),Zt(n=this._aes.encrypt(n),t,s);return t}decrypt(e){if((e=Qt(e)).length%16!=0)throw new Error("invalid ciphertext size (must be multiple of 16 bytes)");for(var t=Xt(e.length),n=Xt(16),s=0;s<e.length;s+=16)Zt(e,n,0,s,s+16),Zt(n=this._aes.decrypt(n),t,s);return t}}class bn{constructor(e,t){if(!(this instanceof bn))throw Error("AES must be instanitated with `new`");if(this.description="Cipher Block Chaining",this.name="cbc",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 bytes)")}else t=Xt(16);this._lastCipherblock=Qt(t,!0),this._aes=new In(e)}encrypt(e){if((e=Qt(e)).length%16!=0)throw new Error("invalid plaintext size (must be multiple of 16 bytes)");for(var t=Xt(e.length),n=Xt(16),s=0;s<e.length;s+=16){Zt(e,n,0,s,s+16);for(var r=0;r<16;r++)n[r]^=this._lastCipherblock[r];this._lastCipherblock=this._aes.encrypt(n),Zt(this._lastCipherblock,t,s)}return t}decrypt(e){if((e=Qt(e)).length%16!=0)throw new Error("invalid ciphertext size (must be multiple of 16 bytes)");for(var t=Xt(e.length),n=Xt(16),s=0;s<e.length;s+=16){Zt(e,n,0,s,s+16),n=this._aes.decrypt(n);for(var r=0;r<16;r++)t[s+r]=n[r]^this._lastCipherblock[r];Zt(e,this._lastCipherblock,0,s,s+16)}return t}}class kn{constructor(e,t,n){if(!(this instanceof kn))throw Error("AES must be instanitated with `new`");if(this.description="Cipher Feedback",this.name="cfb",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 size)")}else t=Xt(16);n||(n=1),this.segmentSize=n,this._shiftRegister=Qt(t,!0),this._aes=new In(e)}encrypt(e){if(e.length%this.segmentSize!=0)throw new Error("invalid plaintext size (must be segmentSize bytes)");for(var t,n=Qt(e,!0),s=0;s<n.length;s+=this.segmentSize){t=this._aes.encrypt(this._shiftRegister);for(var r=0;r<this.segmentSize;r++)n[s+r]^=t[r];Zt(this._shiftRegister,this._shiftRegister,0,this.segmentSize),Zt(n,this._shiftRegister,16-this.segmentSize,s,s+this.segmentSize)}return n}decrypt(e){if(e.length%this.segmentSize!=0)throw new Error("invalid ciphertext size (must be segmentSize bytes)");for(var t,n=Qt(e,!0),s=0;s<n.length;s+=this.segmentSize){t=this._aes.encrypt(this._shiftRegister);for(var r=0;r<this.segmentSize;r++)n[s+r]^=t[r];Zt(this._shiftRegister,this._shiftRegister,0,this.segmentSize),Zt(e,this._shiftRegister,16-this.segmentSize,s,s+this.segmentSize)}return n}}class An{constructor(e,t){if(!(this instanceof An))throw Error("AES must be instanitated with `new`");if(this.description="Output Feedback",this.name="ofb",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 bytes)")}else t=Xt(16);this._lastPrecipher=Qt(t,!0),this._lastPrecipherIndex=16,this._aes=new In(e)}encrypt(e){for(var t=Qt(e,!0),n=0;n<t.length;n++)16===this._lastPrecipherIndex&&(this._lastPrecipher=this._aes.encrypt(this._lastPrecipher),this._lastPrecipherIndex=0),t[n]^=this._lastPrecipher[this._lastPrecipherIndex++];return t}decrypt(e){return this.encrypt(e)}}class Pn{constructor(e){if(!(this instanceof Pn))throw Error("Counter must be instanitated with `new`");0===e||e||(e=1),"number"==typeof e?(this._counter=Xt(16),this.setValue(e)):this.setBytes(e)}setValue(e){if("number"!=typeof e||parseInt(e)!=e)throw new Error("invalid counter value (must be an integer)");if(e>Number.MAX_SAFE_INTEGER)throw new Error("integer value out of safe range");for(var t=15;t>=0;--t)this._counter[t]=e%256,e=parseInt(e/256)}setBytes(e){if(16!=(e=Qt(e,!0)).length)throw new Error("invalid counter bytes size (must be 16 bytes)");this._counter=e}increment(){for(var e=15;e>=0;e--){if(255!==this._counter[e]){this._counter[e]++;break}this._counter[e]=0}}}class Cn{constructor(e,t){if(!(this instanceof Cn))throw Error("AES must be instanitated with `new`");this.description="Counter",this.name="ctr",t instanceof Pn||(t=new Pn(t)),this._counter=t,this._remainingCounter=null,this._remainingCounterIndex=16,this._aes=new In(e)}encrypt(e){for(var t=Qt(e,!0),n=0;n<t.length;n++)16===this._remainingCounterIndex&&(this._remainingCounter=this._aes.encrypt(this._counter._counter),this._remainingCounterIndex=0,this._counter.increment()),t[n]^=this._remainingCounter[this._remainingCounterIndex++];return t}decrypt(e){return this.encrypt(e)}}var Tn={AES:In,Counter:Pn,ModeOfOperation:{ecb:Sn,cbc:bn,cfb:kn,ofb:An,ctr:Cn},utils:{hex:nn,utf8:tn},padding:{pkcs7:{pad:function(e){var t=16-(e=Qt(e,!0)).length%16,n=Xt(e.length+t);Zt(e,n);for(var s=e.length;s<n.length;s++)n[s]=t;return n},strip:function(e){if((e=Qt(e,!0)).length<16)throw new Error("PKCS#7 invalid length");var t=e[e.length-1];if(t>16)throw new Error("PKCS#7 padding byte out of range");for(var n=e.length-t,s=0;s<t;s++)if(e[n+s]!==t)throw new Error("PKCS#7 invalid padding byte");var r=Xt(n);return Zt(e,r,0,0,n),r}}},_arrayTest:{coerceArray:Qt,createArray:Xt,copyArray:Zt}};function xn(e,t,n){const s=new Uint8Array(uni.base64ToArrayBuffer(t)),r=Tn.utils.utf8.toBytes(n),i=Tn.utils.utf8.toBytes(e),o=new Tn.ModeOfOperation.cbc(s,r),a=Tn.padding.pkcs7.pad(i),c=o.encrypt(a);return uni.arrayBufferToBase64(c)}const On={code:2e4,message:"System error"},En={code:20101,message:"Invalid client"},Ln={code:20102,message:"Get encrypt key failed"},Rn={10001:"Secure network is not supported on current playground or unimpsdk",10003:"Config missing in current app. If the problem pesist, please contact DCloud.",10009:"Encrypt payload failed",10010:"Decrypt response failed"};function Un(e){const{errSubject:t,subject:n,errCode:s,errMsg:r,code:i,message:o,cause:a}=e||{};return new ee({subject:t||n||"uni-secure-network",code:s||i||On.code,message:r||o,cause:a})}let Nn,Dn,Mn=null;class qn extends Vt{constructor(e){super(e),this.clientType="mp-weixin",this.userEncryptKey=null}isLogin(){return!!this.scopedGlobalCache.mpWeixinCode||!!this.scopedGlobalCache.mpWeixinOpenid}async prepare(){if(!this.isLogin()){if(!this.scopedGlobalCache.initPromise)throw new Error("`uniCloud.initSecureNetworkByWeixin` has not yet been called");if(await this.scopedGlobalCache.initPromise,!this.isLogin())throw new Error("uniCloud.initSecureNetworkByWeixin` has not yet been called or successfully excuted")}}async getUserEncryptKey(){if(this.userEncryptKey)return this.userEncryptKey;if(Mn&&Mn.expireTime){const e=Date.now();if(Mn.expireTime-e>0)return this.userEncryptKey=Mn,this.userEncryptKey}return new Promise(((e,t)=>{uni.getUserCryptoManager().getLatestUserKey({success:t=>{Mn=t,this.userEncryptKey=t,e(this.userEncryptKey)},fail:e=>{t(Un({...Ln,cause:e}))}})}))}getWxAppId(){return wx.getAccountInfoSync().miniProgram.appId}async platformGetSignOption(){const{encryptKey:e,iv:t,version:n}=await this.getUserEncryptKey();return{verifyClientSign:xn(JSON.stringify({data:JSON.stringify({}),appId:this.appId,deviceId:this.deviceId,wxAppId:this.getWxAppId(),simulator:"devtools"===ae().platform,timestamp:Date.now()}),e,t),encryptKeyId:n,mpWeixinCode:this.scopedGlobalCache.mpWeixinCode,mpWeixinOpenid:this.scopedGlobalCache.mpWeixinOpenid}}async platformEncryptData(e){const{encryptKey:t,iv:n,version:s}=await this.getUserEncryptKey(),r={secretType:this.secretType,encryptKeyId:s,mpWeixinCode:this.scopedGlobalCache.mpWeixinCode,mpWeixinOpenid:this.scopedGlobalCache.mpWeixinOpenid};return this.secretType===Jt?{content:e,_uniCloudOptions:r}:{content:xn(JSON.stringify({data:JSON.stringify(e),appId:this.appId,deviceId:this.deviceId,wxAppId:this.getWxAppId(),simulator:"devtools"===ae().platform,timestamp:Date.now()}),t,n),_uniCloudOptions:r}}async platformDecryptResult(e){const{content:t}=e,{encryptKey:n,iv:s}=await this.getUserEncryptKey();return JSON.parse(function(e,t,n){const s=new Uint8Array(uni.base64ToArrayBuffer(e)),r=new Uint8Array(uni.base64ToArrayBuffer(t)),i=Tn.utils.utf8.toBytes(n),o=new Tn.ModeOfOperation.cbc(r,i),a=Tn.padding.pkcs7.strip(o.decrypt(s));return Tn.utils.utf8.fromBytes(a)}(t,n,s))}isClientKeyNotFound(){return!1}}function Fn(e){const t=["hasClientKey","encryptGetClientKeyPayload","setClientKey","encrypt","decrypt"],n={};for(let s=0;s<t.length;s++){const r=t[s];n[r]=function(...t){return new Promise(((n,s)=>{"function"==typeof e[r]?e[r](...t,(function({type:e,data:t,errCode:r,errMsg:i,errSubject:o,message:a}={}){"success"===e?n(t):s(Un({errCode:r,errMsg:Rn[r]||i||a,errSubject:o}))})):s(Un({message:"请检查manifest.json内是否开启安全网络模块，另外注意标准基座不支持安全网络模块"}))}))}}return n}class Kn extends Vt{constructor(e){super(e),this.clientType="app",this.appUtils={...Fn(uni.requireNativePlugin("plus"))},this.systemInfo=Nn||(Nn=ae())}async hasClientKey(){return this._hasClientKey=await this.appUtils.hasClientKey({provider:this.provider,spaceId:this.spaceId}),this._hasClientKey}async getAppClientKey(){const{data:e,key:t}=await this.appUtils.encryptGetClientKeyPayload({data:JSON.stringify({})}),n=(await this.uniCloudIns.callFunction({name:"DCloud-clientDB",data:{redirectTo:"encryption",action:"getAppClientKey",data:e,key:t}})).result||{};if(0!==n.errCode)throw function(e){return new ee({subject:e.errSubject||"uni-secure-network",code:e.errCode||e.code||On.code,message:e.errMsg||e.message})}(n);const{clientKey:s,key:r}=n;await this.appUtils.setClientKey({provider:this.provider,spaceId:this.spaceId,clientKey:s,key:r})}async ensureClientKey({forceUpdate:e=!1}={}){if(!0!==await this.hasClientKey()||e)return e&&this.scopedGlobalCache.initPromise&&this.scopedGlobalCache.initStatus===l||!e&&this.scopedGlobalCache.initPromise&&this.scopedGlobalCache.initStatus!==d||(this.scopedGlobalCache.initPromise=this.getAppClientKey(),this.scopedGlobalCache.initPromise.then((e=>{this.scopedGlobalCache.initStatus=h})).catch((e=>{throw this.scopedGlobalCache.initStatus=d,e})),this.scopedGlobalCache.initStatus=l),this.scopedGlobalCache.initPromise}async prepare({forceUpdate:e=!1}={}){await this.ensureClientKey({forceUpdate:e})}async platformGetSignOption(){const{data:e,key:t}=await this.appUtils.encrypt({provider:this.provider,spaceId:this.spaceId,data:JSON.stringify({})});return{verifyClientSign:e,encryptKeyId:t}}async platformEncryptData(e){const{data:t,key:n}=await this.appUtils.encrypt({provider:this.provider,spaceId:this.spaceId,data:JSON.stringify(e)}),s={secretType:this.secretType,encryptKeyId:n};return this.secretType===Jt?{content:e,_uniCloudOptions:s}:{content:t,_uniCloudOptions:s}}async platformDecryptResult(e){const{content:t,_uniCloudOptions:n={}}=e,s=n.encryptKeyId,r=await this.appUtils.decrypt({provider:this.provider,spaceId:this.spaceId,data:t,key:s});return JSON.parse(r.data)}isClientKeyNotFound(e={}){const t=e.result||{};return 70009===t.errCode&&"uni-secure-network"===t.errSubject}}function jn({secretType:e}={}){return e===Ht||e===Jt||e===zt}function $n({name:e,data:t={}}={}){return"app"===A&&"DCloud-clientDB"===e&&"encryption"===t.redirectTo&&"getAppClientKey"===t.action}function Bn({provider:e,spaceId:t,functionName:n}={}){const{appId:s,uniPlatform:r,osName:i}=ae();let o=r;"app"===r&&(o=i);const a=function({provider:e,spaceId:t}={}){const n=k;if(!n)return{};e=function(e){return"tencent"===e?"tcb":e}(e);const s=n.find((n=>n.provider===e&&n.spaceId===t));return s&&s.config}({provider:e,spaceId:t});if(!a||!a.accessControl||!a.accessControl.enable)return!1;const c=a.accessControl.function||{},u=Object.keys(c);if(0===u.length)return!0;const l=function(e,t){let n,s,r;for(let i=0;i<e.length;i++){const o=e[i];o!==t?"*"!==o?o.split(",").map((e=>e.trim())).indexOf(t)>-1&&(s=o):r=o:n=o}return n||s||r}(u,n);if(!l)return!1;if((c[l]||[]).find(((e={})=>e.appId===s&&(e.platform||"").toLowerCase()===o.toLowerCase())))return!0;throw console.error(`此应用[appId: ${s}, platform: ${o}]不在云端配置的允许访问的应用列表内，参考：https://uniapp.dcloud.net.cn/uniCloud/secure-network.html#verify-client`),Un(En)}function Wn({functionName:e,result:t,logPvd:n}){if(I&&this.__dev__.debugLog&&t&&t.requestId){const s=JSON.stringify({spaceId:this.config.spaceId,functionName:e,requestId:t.requestId});console.log(`[${n}-request]${s}[/${n}-request]`)}}function Hn(e){const t=e.callFunction,n=function(n){const s=n.name;n.data=qt.call(e,{data:n.data});const r={aliyun:"aliyun",tencent:"tcb",tcb:"tcb",alipay:"alipay",dcloud:"dcloud"}[this.config.provider],i=jn(n),o=$n(n),a=i||o;return t.call(this,n).then((e=>(e.errCode=0,!a&&Wn.call(this,{functionName:s,result:e,logPvd:r}),Promise.resolve(e))),(e=>(!a&&Wn.call(this,{functionName:s,result:e,logPvd:r}),e&&e.message&&(e.message=function({message:e="",extraInfo:t={},formatter:n=[]}={}){for(let s=0;s<n.length;s++){const{rule:r,content:i,mode:o}=n[s],a=e.match(r);if(!a)continue;let c=i;for(let e=1;e<a.length;e++)c=Bt(c,`{$${e}}`,a[e]);for(const e in t)c=Bt(c,`{${e}}`,t[e]);return"replace"===o?c:e+c}return e}({message:`[${n.name}]: ${e.message}`,formatter:Kt,extraInfo:{functionName:s}})),Promise.reject(e))))};e.callFunction=function(t){const{provider:s,spaceId:r}=e.config,i=t.name;let o,a;if(t.data=t.data||{},I&&e.__dev__.debugInfo&&!e.__dev__.debugInfo.forceRemote&&C?(e._callCloudFunction||(e._callCloudFunction=n,e._callLocalFunction=Ft),o=Ft):o=n,o=o.bind(e),$n(t))a=n.call(e,t);else if(function({name:e,data:t={}}){return"mp-weixin"===A&&"uni-id-co"===e&&"secureNetworkHandshakeByWeixin"===t.method}(t))a=o.call(e,t);else if(jn(t)){a=new Dn({secretType:t.secretType,uniCloudIns:e}).wrapEncryptDataCallFunction(n.bind(e))(t)}else if(Bn({provider:s,spaceId:r,functionName:i})){a=new Dn({secretType:t.secretType,uniCloudIns:e}).wrapVerifyClientCallFunction(n.bind(e))(t)}else a=o(t);return Object.defineProperty(a,"result",{get:()=>(console.warn("当前返回结果为Promise类型，不可直接访问其result属性，详情请参考：https://uniapp.dcloud.net.cn/uniCloud/faq?id=promise"),{})}),a.then((e=>("undefined"!=typeof UTSJSONObject&&(e.result=new UTSJSONObject(e.result)),e)))}}Dn="mp-weixin"!==A&&"app"!==A?class{constructor(){throw Un({message:`Platform ${A} is not supported by secure network`})}}:b?"mp-weixin"===A?qn:Kn:class{constructor(){throw Un({message:`Platform ${A} is not enabled, please check whether secure network module is enabled in your manifest.json`})}};const Jn=Symbol("CLIENT_DB_INTERNAL");function zn(e,t){return e.then="DoNotReturnProxyWithAFunctionNamedThen",e._internalType=Jn,e.inspect=null,e.__v_raw=void 0,new Proxy(e,{get(e,n,s){if("_uniClient"===n)return null;if("symbol"==typeof n)return e[n];if(n in e||"string"!=typeof n){const t=e[n];return"function"==typeof t?t.bind(e):t}return t.get(e,n,s)}})}function Vn(e){return{on:(t,n)=>{e[t]=e[t]||[],e[t].indexOf(n)>-1||e[t].push(n)},off:(t,n)=>{e[t]=e[t]||[];const s=e[t].indexOf(n);-1!==s&&e[t].splice(s,1)}}}const Gn=["db.Geo","db.command","command.aggregate"];function Yn(e,t){return Gn.indexOf(`${e}.${t}`)>-1}function Qn(e){switch(p(e=ne(e))){case"array":return e.map((e=>Qn(e)));case"object":return e._internalType===Jn||Object.keys(e).forEach((t=>{e[t]=Qn(e[t])})),e;case"regexp":return{$regexp:{source:e.source,flags:e.flags}};case"date":return{$date:e.toISOString()};default:return e}}function Xn(e){return e&&e.content&&e.content.$method}class Zn{constructor(e,t,n){this.content=e,this.prevStage=t||null,this.udb=null,this._database=n}toJSON(){let e=this;const t=[e.content];for(;e.prevStage;)e=e.prevStage,t.push(e.content);return{$db:t.reverse().map((e=>({$method:e.$method,$param:Qn(e.$param)})))}}toString(){return JSON.stringify(this.toJSON())}getAction(){const e=this.toJSON().$db.find((e=>"action"===e.$method));return e&&e.$param&&e.$param[0]}getCommand(){return{$db:this.toJSON().$db.filter((e=>"action"!==e.$method))}}get isAggregate(){let e=this;for(;e;){const t=Xn(e),n=Xn(e.prevStage);if("aggregate"===t&&"collection"===n||"pipeline"===t)return!0;e=e.prevStage}return!1}get isCommand(){let e=this;for(;e;){if("command"===Xn(e))return!0;e=e.prevStage}return!1}get isAggregateCommand(){let e=this;for(;e;){const t=Xn(e),n=Xn(e.prevStage);if("aggregate"===t&&"command"===n)return!0;e=e.prevStage}return!1}getNextStageFn(e){const t=this;return function(){return es({$method:e,$param:Qn(Array.from(arguments))},t,t._database)}}get count(){return this.isAggregate?this.getNextStageFn("count"):function(){return this._send("count",Array.from(arguments))}}get remove(){return this.isCommand?this.getNextStageFn("remove"):function(){return this._send("remove",Array.from(arguments))}}get(){return this._send("get",Array.from(arguments))}get add(){return this.isCommand?this.getNextStageFn("add"):function(){return this._send("add",Array.from(arguments))}}update(){return this._send("update",Array.from(arguments))}end(){return this._send("end",Array.from(arguments))}get set(){return this.isCommand?this.getNextStageFn("set"):function(){throw new Error("JQL禁止使用set方法")}}_send(e,t){const n=this.getAction(),s=this.getCommand();if(s.$db.push({$method:e,$param:Qn(t)}),I){const e=s.$db.find((e=>"collection"===e.$method)),t=e&&e.$param;t&&1===t.length&&"string"==typeof e.$param[0]&&e.$param[0].indexOf(",")>-1&&console.warn("检测到使用JQL语法联表查询时，未使用getTemp先过滤主表数据，在主表数据量大的情况下可能会查询缓慢。\n- 如何优化请参考此文档：https://uniapp.dcloud.net.cn/uniCloud/jql?id=lookup-with-temp \n- 如果主表数据量很小请忽略此信息，项目发行时不会出现此提示。")}return this._database._callCloudFunction({action:n,command:s})}}function es(e,t,n){return zn(new Zn(e,t,n),{get(e,t){let s="db";return e&&e.content&&(s=e.content.$method),Yn(s,t)?es({$method:t},e,n):function(){return es({$method:t,$param:Qn(Array.from(arguments))},e,n)}}})}function ts({path:e,method:t}){return class{constructor(){this.param=Array.from(arguments)}toJSON(){return{$newDb:[...e.map((e=>({$method:e}))),{$method:t,$param:this.param}]}}toString(){return JSON.stringify(this.toJSON())}}}function ns(e,t={}){return zn(new e(t),{get:(e,t)=>Yn("db",t)?es({$method:t},null,e):function(){return es({$method:t,$param:Qn(Array.from(arguments))},null,e)}})}class ss extends class{constructor({uniClient:e={},isJQL:t=!1}={}){this._uniClient=e,this._authCallBacks={},this._dbCallBacks={},e._isDefault&&(this._dbCallBacks=E("_globalUniCloudDatabaseCallback")),t||(this.auth=Vn(this._authCallBacks)),this._isJQL=t,Object.assign(this,Vn(this._dbCallBacks)),this.env=zn({},{get:(e,t)=>({$env:t})}),this.Geo=zn({},{get:(e,t)=>ts({path:["Geo"],method:t})}),this.serverDate=ts({path:[],method:"serverDate"}),this.RegExp=ts({path:[],method:"RegExp"})}getCloudEnv(e){if("string"!=typeof e||!e.trim())throw new Error("getCloudEnv参数错误");return{$env:e.replace("$cloudEnv_","")}}_callback(e,t){const n=this._dbCallBacks;n[e]&&n[e].forEach((e=>{e(...t)}))}_callbackAuth(e,t){const n=this._authCallBacks;n[e]&&n[e].forEach((e=>{e(...t)}))}multiSend(){const e=Array.from(arguments),t=e.map((e=>{const t=e.getAction(),n=e.getCommand();if("getTemp"!==n.$db[n.$db.length-1].$method)throw new Error("multiSend只支持子命令内使用getTemp");return{action:t,command:n}}));return this._callCloudFunction({multiCommand:t,queryList:e})}}{_parseResult(e){return this._isJQL?e.result:e}_callCloudFunction({action:e,command:t,multiCommand:n,queryList:s}){function r(e,t){if(n&&s)for(let n=0;n<s.length;n++){const r=s[n];r.udb&&"function"==typeof r.udb.setResult&&(t?r.udb.setResult(t):r.udb.setResult(e.result.dataList[n]))}}const i=this,o=this._isJQL?"databaseForJQL":"database";function a(e){return i._callback("error",[e]),D(M(o,"fail"),e).then((()=>D(M(o,"complete"),e))).then((()=>(r(null,e),G(K,{type:B,content:e}),Promise.reject(e))))}const c=D(M(o,"invoke")),l=this._uniClient;return c.then((()=>l.callFunction({name:"DCloud-clientDB",type:u,data:{action:e,command:t,multiCommand:n}}))).then((e=>{const{code:t,message:n,token:s,tokenExpired:c,systemInfo:u=[]}=e.result;if(u)for(let e=0;e<u.length;e++){const{level:t,message:n,detail:s}=u[e],r=console["app"===A&&"warn"===t?"error":t]||console.log;let i="[System Info]"+n;s&&(i=`${i}\n详细信息：${s}`),r(i)}if(t){return a(new ee({code:t,message:n,requestId:e.requestId}))}e.result.errCode=e.result.errCode||e.result.code,e.result.errMsg=e.result.errMsg||e.result.message,s&&c&&(re({token:s,tokenExpired:c}),this._callbackAuth("refreshToken",[{token:s,tokenExpired:c}]),this._callback("refreshToken",[{token:s,tokenExpired:c}]),G($,{token:s,tokenExpired:c}));const l=[{prop:"affectedDocs",tips:"affectedDocs不再推荐使用，请使用inserted/deleted/updated/data.length替代"},{prop:"code",tips:"code不再推荐使用，请使用errCode替代"},{prop:"message",tips:"message不再推荐使用，请使用errMsg替代"}];for(let t=0;t<l.length;t++){const{prop:n,tips:s}=l[t];if(n in e.result){const t=e.result[n];Object.defineProperty(e.result,n,{get:()=>(console.warn(s),t)})}}return function(e){return D(M(o,"success"),e).then((()=>D(M(o,"complete"),e))).then((()=>{r(e,null);const t=i._parseResult(e);return G(K,{type:B,content:t}),Promise.resolve(t)}))}(e)}),(e=>{/fc_function_not_found|FUNCTION_NOT_FOUND/g.test(e.message)&&console.warn("clientDB未初始化，请在web控制台保存一次schema以开启clientDB");return a(new ee({code:e.code||"SYSTEM_ERROR",message:e.message,requestId:e.requestId}))}))}}var rs={};const is="token无效，跳转登录页面",os="token过期，跳转登录页面",as={TOKEN_INVALID_TOKEN_EXPIRED:os,TOKEN_INVALID_INVALID_CLIENTID:is,TOKEN_INVALID:is,TOKEN_INVALID_WRONG_TOKEN:is,TOKEN_INVALID_ANONYMOUS_USER:is},cs={"uni-id-token-expired":os,"uni-id-check-token-failed":is,"uni-id-token-not-exist":is,"uni-id-check-device-feature-failed":is};function us(e,t){let n="";return n=e?`${e}/${t}`:t,n.replace(/^\//,"")}function ls(e=[],t=""){const n=[],s=[];return e.forEach((e=>{!0===e.needLogin?n.push(us(t,e.path)):!1===e.needLogin&&s.push(us(t,e.path))})),{needLoginPage:n,notNeedLoginPage:s}}function hs(e){return e.split("?")[0].replace(/^\//,"")}function ds(){return function(e){let t=e&&e.$page&&e.$page.fullPath||"";return t?("/"!==t.charAt(0)&&(t="/"+t),t):t}(function(){const e=getCurrentPages();return e[e.length-1]}())}function ps(){return hs(ds())}function fs(e="",t={}){if(!e)return!1;if(!(t&&t.list&&t.list.length))return!1;const n=t.list,s=hs(e);return n.some((e=>e.pagePath===s))}const gs=!!rs.uniIdRouter;const{loginPage:ms,routerNeedLogin:ys,resToLogin:_s,needLoginPage:ws,notNeedLoginPage:vs,loginPageInTabBar:Is}=function({pages:e=[],subPackages:t=[],uniIdRouter:n={},tabBar:s={}}=rs){const{loginPage:r,needLogin:i=[],resToLogin:o=!0}=n,{needLoginPage:a,notNeedLoginPage:c}=ls(e),{needLoginPage:u,notNeedLoginPage:l}=function(e=[]){const t=[],n=[];return e.forEach((e=>{const{root:s,pages:r=[]}=e,{needLoginPage:i,notNeedLoginPage:o}=ls(r,s);t.push(...i),n.push(...o)})),{needLoginPage:t,notNeedLoginPage:n}}(t);return{loginPage:r,routerNeedLogin:i,resToLogin:o,needLoginPage:[...a,...u],notNeedLoginPage:[...c,...l],loginPageInTabBar:fs(r,s)}}();if(ws.indexOf(ms)>-1)throw new Error(`Login page [${ms}] should not be "needLogin", please check your pages.json`);function Ss(e){const t=ps();if("/"===e.charAt(0))return e;const[n,s]=e.split("?"),r=n.replace(/^\//,"").split("/"),i=t.split("/");i.pop();for(let e=0;e<r.length;e++){const t=r[e];".."===t?i.pop():"."!==t&&i.push(t)}return""===i[0]&&i.shift(),"/"+i.join("/")+(s?"?"+s:"")}function bs(e){const t=hs(Ss(e));return!(vs.indexOf(t)>-1)&&(ws.indexOf(t)>-1||ys.some((t=>function(e,t){return new RegExp(t).test(e)}(e,t))))}function ks({redirect:e}){const t=hs(e),n=hs(ms);return ps()!==n&&t!==n}function As({api:e,redirect:t}={}){if(!t||!ks({redirect:t}))return;const n=function(e,t){return"/"!==e.charAt(0)&&(e="/"+e),t?e.indexOf("?")>-1?e+`&uniIdRedirectUrl=${encodeURIComponent(t)}`:e+`?uniIdRedirectUrl=${encodeURIComponent(t)}`:e}(ms,t);Is?"navigateTo"!==e&&"redirectTo"!==e||(e="switchTab"):"switchTab"===e&&(e="navigateTo");const s={navigateTo:uni.navigateTo,redirectTo:uni.redirectTo,switchTab:uni.switchTab,reLaunch:uni.reLaunch};setTimeout((()=>{s[e]({url:n})}),0)}function Ps({url:e}={}){const t={abortLoginPageJump:!1,autoToLoginPage:!1},n=function(){const{token:e,tokenExpired:t}=se();let n;if(e){if(t<Date.now()){const e="uni-id-token-expired";n={errCode:e,errMsg:cs[e]}}}else{const e="uni-id-check-token-failed";n={errCode:e,errMsg:cs[e]}}return n}();if(bs(e)&&n){n.uniIdRedirectUrl=e;if(J(j).length>0)return setTimeout((()=>{G(j,n)}),0),t.abortLoginPageJump=!0,t;t.autoToLoginPage=!0}return t}function Cs(){!function(){const e=ds(),{abortLoginPageJump:t,autoToLoginPage:n}=Ps({url:e});t||n&&As({api:"redirectTo",redirect:e})}();const e=["navigateTo","redirectTo","reLaunch","switchTab"];for(let t=0;t<e.length;t++){const n=e[t];uni.addInterceptor(n,{invoke(e){const{abortLoginPageJump:t,autoToLoginPage:s}=Ps({url:e.url});return t?e:s?(As({api:n,redirect:Ss(e.url)}),!1):e}})}}function Ts(){this.onResponse((e=>{const{type:t,content:n}=e;let s=!1;switch(t){case"cloudobject":s=function(e){if("object"!=typeof e)return!1;const{errCode:t}=e||{};return t in cs}(n);break;case"clientdb":s=function(e){if("object"!=typeof e)return!1;const{errCode:t}=e||{};return t in as}(n)}s&&function(e={}){const t=J(j);X().then((()=>{const n=ds();if(n&&ks({redirect:n}))return t.length>0?G(j,Object.assign({uniIdRedirectUrl:n},e)):void(ms&&As({api:"navigateTo",redirect:n}))}))}(n)}))}function xs(e){!function(e){e.onResponse=function(e){z(K,e)},e.offResponse=function(e){V(K,e)}}(e),function(e){e.onNeedLogin=function(e){z(j,e)},e.offNeedLogin=function(e){V(j,e)},gs&&(E("_globalUniCloudStatus").needLoginInit||(E("_globalUniCloudStatus").needLoginInit=!0,X().then((()=>{Cs.call(e)})),_s&&Ts.call(e)))}(e),function(e){e.onRefreshToken=function(e){z($,e)},e.offRefreshToken=function(e){V($,e)}}(e)}let Os;const Es="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",Ls=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function Rs(){const e=se().token||"",t=e.split(".");if(!e||3!==t.length)return{uid:null,role:[],permission:[],tokenExpired:0};let n;try{n=JSON.parse((s=t[1],decodeURIComponent(Os(s).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(e){throw new Error("获取当前用户信息出错，详细错误信息为："+e.message)}var s;return n.tokenExpired=1e3*n.exp,delete n.exp,delete n.iat,n}Os="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!Ls.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,s,r="",i=0;i<e.length;)t=Es.indexOf(e.charAt(i++))<<18|Es.indexOf(e.charAt(i++))<<12|(n=Es.indexOf(e.charAt(i++)))<<6|(s=Es.indexOf(e.charAt(i++))),r+=64===n?String.fromCharCode(t>>16&255):64===s?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return r}:atob;var Us=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});const n="chooseAndUploadFile:ok",s="chooseAndUploadFile:fail";function r(e,t){return e.tempFiles.forEach(((e,n)=>{e.name||(e.name=e.path.substring(e.path.lastIndexOf("/")+1)),t&&(e.fileType=t),e.cloudPath=Date.now()+"_"+n+e.name.substring(e.name.lastIndexOf("."))})),e.tempFilePaths||(e.tempFilePaths=e.tempFiles.map((e=>e.path))),e}function i(e,t,{onChooseFile:s,onUploadProgress:r}){return t.then((e=>{if(s){const t=s(e);if(void 0!==t)return Promise.resolve(t).then((t=>void 0===t?e:t))}return e})).then((t=>!1===t?{errMsg:n,tempFilePaths:[],tempFiles:[]}:function(e,t,s=5,r){(t=Object.assign({},t)).errMsg=n;const i=t.tempFiles,o=i.length;let a=0;return new Promise((n=>{for(;a<s;)c();function c(){const s=a++;if(s>=o)return void(!i.find((e=>!e.url&&!e.errMsg))&&n(t));const u=i[s];e.uploadFile({provider:u.provider,filePath:u.path,cloudPath:u.cloudPath,fileType:u.fileType,cloudPathAsRealPath:u.cloudPathAsRealPath,onUploadProgress(e){e.index=s,e.tempFile=u,e.tempFilePath=u.path,r&&r(e)}}).then((e=>{u.url=e.fileID,s<o&&c()})).catch((e=>{u.errMsg=e.errMsg||e.message,s<o&&c()}))}}))}(e,t,5,r)))}t.initChooseAndUploadFile=function(e){return function(t={type:"all"}){return"image"===t.type?i(e,function(e){const{count:t,sizeType:n,sourceType:i=["album","camera"],extension:o}=e;return new Promise(((e,a)=>{uni.chooseImage({count:t,sizeType:n,sourceType:i,extension:o,success(t){e(r(t,"image"))},fail(e){a({errMsg:e.errMsg.replace("chooseImage:fail",s)})}})}))}(t),t):"video"===t.type?i(e,function(e){const{camera:t,compressed:n,maxDuration:i,sourceType:o=["album","camera"],extension:a}=e;return new Promise(((e,c)=>{uni.chooseVideo({camera:t,compressed:n,maxDuration:i,sourceType:o,extension:a,success(t){const{tempFilePath:n,duration:s,size:i,height:o,width:a}=t;e(r({errMsg:"chooseVideo:ok",tempFilePaths:[n],tempFiles:[{name:t.tempFile&&t.tempFile.name||"",path:n,size:i,type:t.tempFile&&t.tempFile.type||"",width:a,height:o,duration:s,fileType:"video",cloudPath:""}]},"video"))},fail(e){c({errMsg:e.errMsg.replace("chooseVideo:fail",s)})}})}))}(t),t):i(e,function(e){const{count:t,extension:n}=e;return new Promise(((e,i)=>{let o=uni.chooseFile;if("undefined"!=typeof wx&&"function"==typeof wx.chooseMessageFile&&(o=wx.chooseMessageFile),"function"!=typeof o)return i({errMsg:s+" 请指定 type 类型，该平台仅支持选择 image 或 video。"});o({type:"all",count:t,extension:n,success(t){e(r(t))},fail(e){i({errMsg:e.errMsg.replace("chooseFile:fail",s)})}})}))}(t),t)}}})),Ns=e(Us);const Ds="manual";function Ms(e){return{props:{localdata:{type:Array,default:()=>[]},options:{type:[Object,Array],default:()=>({})},spaceInfo:{type:Object,default:()=>({})},collection:{type:[String,Array],default:""},action:{type:String,default:""},field:{type:String,default:""},orderby:{type:String,default:""},where:{type:[String,Object],default:""},pageData:{type:String,default:"add"},pageCurrent:{type:Number,default:1},pageSize:{type:Number,default:20},getcount:{type:[Boolean,String],default:!1},gettree:{type:[Boolean,String],default:!1},gettreepath:{type:[Boolean,String],default:!1},startwith:{type:String,default:""},limitlevel:{type:Number,default:10},groupby:{type:String,default:""},groupField:{type:String,default:""},distinct:{type:[Boolean,String],default:!1},foreignKey:{type:String,default:""},loadtime:{type:String,default:"auto"},manual:{type:Boolean,default:!1}},data:()=>({mixinDatacomLoading:!1,mixinDatacomHasMore:!1,mixinDatacomResData:[],mixinDatacomErrorMessage:"",mixinDatacomPage:{},mixinDatacomError:null}),created(){this.mixinDatacomPage={current:this.pageCurrent,size:this.pageSize,count:0},this.$watch((()=>{var e=[];return["pageCurrent","pageSize","localdata","collection","action","field","orderby","where","getont","getcount","gettree","groupby","groupField","distinct"].forEach((t=>{e.push(this[t])})),e}),((e,t)=>{if(this.loadtime===Ds)return;let n=!1;const s=[];for(let r=2;r<e.length;r++)e[r]!==t[r]&&(s.push(e[r]),n=!0);e[0]!==t[0]&&(this.mixinDatacomPage.current=this.pageCurrent),this.mixinDatacomPage.size=this.pageSize,this.onMixinDatacomPropsChange(n,s)}))},methods:{onMixinDatacomPropsChange(e,t){},mixinDatacomEasyGet({getone:e=!1,success:t,fail:n}={}){this.mixinDatacomLoading||(this.mixinDatacomLoading=!0,this.mixinDatacomErrorMessage="",this.mixinDatacomError=null,this.mixinDatacomGet().then((n=>{this.mixinDatacomLoading=!1;const{data:s,count:r}=n.result;this.getcount&&(this.mixinDatacomPage.count=r),this.mixinDatacomHasMore=s.length<this.pageSize;const i=e?s.length?s[0]:void 0:s;this.mixinDatacomResData=i,t&&t(i)})).catch((e=>{this.mixinDatacomLoading=!1,this.mixinDatacomErrorMessage=e,this.mixinDatacomError=e,n&&n(e)})))},mixinDatacomGet(t={}){let n;t=t||{},n="undefined"!=typeof __uniX&&__uniX?e.databaseForJQL(this.spaceInfo):e.database(this.spaceInfo);const s=t.action||this.action;s&&(n=n.action(s));const r=t.collection||this.collection;n=Array.isArray(r)?n.collection(...r):n.collection(r);const i=t.where||this.where;i&&Object.keys(i).length&&(n=n.where(i));const o=t.field||this.field;o&&(n=n.field(o));const a=t.foreignKey||this.foreignKey;a&&(n=n.foreignKey(a));const c=t.groupby||this.groupby;c&&(n=n.groupBy(c));const u=t.groupField||this.groupField;u&&(n=n.groupField(u));!0===(void 0!==t.distinct?t.distinct:this.distinct)&&(n=n.distinct());const l=t.orderby||this.orderby;l&&(n=n.orderBy(l));const h=void 0!==t.pageCurrent?t.pageCurrent:this.mixinDatacomPage.current,d=void 0!==t.pageSize?t.pageSize:this.mixinDatacomPage.size,p=void 0!==t.getcount?t.getcount:this.getcount,f=void 0!==t.gettree?t.gettree:this.gettree,g=void 0!==t.gettreepath?t.gettreepath:this.gettreepath,m={getCount:p},y={limitLevel:void 0!==t.limitlevel?t.limitlevel:this.limitlevel,startWith:void 0!==t.startwith?t.startwith:this.startwith};return f&&(m.getTree=y),g&&(m.getTreePath=y),n=n.skip(d*(h-1)).limit(d).get(m),n}}}}function qs(e){return function(t,n={}){n=function(e,t={}){return e.customUI=t.customUI||e.customUI,e.parseSystemError=t.parseSystemError||e.parseSystemError,Object.assign(e.loadingOptions,t.loadingOptions),Object.assign(e.errorOptions,t.errorOptions),"object"==typeof t.secretMethods&&(e.secretMethods=t.secretMethods),e}({customUI:!1,loadingOptions:{title:"加载中...",mask:!0},errorOptions:{type:"modal",retry:!1}},n);const{customUI:s,loadingOptions:r,errorOptions:i,parseSystemError:o}=n,a=!s;return new Proxy({},{get(s,u){switch(u){case"toString":return"[object UniCloudObject]";case"toJSON":return{}}return function({fn:e,interceptorName:t,getCallbackArgs:n}={}){return async function(...s){const r=n?n({params:s}):{};let i,o;try{return await D(M(t,"invoke"),{...r}),i=await e(...s),await D(M(t,"success"),{...r,result:i}),i}catch(e){throw o=e,await D(M(t,"fail"),{...r,error:o}),o}finally{await D(M(t,"complete"),o?{...r,error:o}:{...r,result:i})}}}({fn:async function s(...l){let h;a&&uni.showLoading({title:r.title,mask:r.mask});const d={name:t,type:c,data:{method:u,params:l}};"object"==typeof n.secretMethods&&function(e,t){const n=t.data.method,s=e.secretMethods||{},r=s[n]||s["*"];r&&(t.secretType=r)}(n,d);let p=!1;try{h=await e.callFunction(d)}catch(e){p=!0,h={result:new ee(e)}}const{errSubject:f,errCode:g,errMsg:m,newToken:y}=h.result||{};if(a&&uni.hideLoading(),y&&y.token&&y.tokenExpired&&(re(y),G($,{...y})),g){let e=m;if(p&&o){e=(await o({objectName:t,methodName:u,params:l,errSubject:f,errCode:g,errMsg:m})).errMsg||m}if(a)if("toast"===i.type)uni.showToast({title:e,icon:"none"});else{if("modal"!==i.type)throw new Error(`Invalid errorOptions.type: ${i.type}`);{const{confirm:t}=await async function({title:e,content:t,showCancel:n,cancelText:s,confirmText:r}={}){return new Promise(((i,o)=>{uni.showModal({title:e,content:t,showCancel:n,cancelText:s,confirmText:r,success(e){i(e)},fail(){i({confirm:!1,cancel:!0})}})}))}({title:"提示",content:e,showCancel:i.retry,cancelText:"取消",confirmText:i.retry?"重试":"确定"});if(i.retry&&t)return s(...l)}}const n=new ee({subject:f,code:g,message:m,requestId:h.requestId});throw n.detail=h.result,G(K,{type:H,content:n}),n}return G(K,{type:H,content:h.result}),h.result},interceptorName:"callObject",getCallbackArgs:function({params:e}={}){return{objectName:t,methodName:u,params:e}}})}})}}function Fs(e){return E("_globalUniCloudSecureNetworkCache__{spaceId}".replace("{spaceId}",e.config.spaceId))}async function Ks({openid:e,callLoginByWeixin:t=!1}={}){const n=Fs(this);if("mp-weixin"!==A)throw new Error(`[SecureNetwork] API \`initSecureNetworkByWeixin\` is not supported on platform \`${A}\``);if(e&&t)throw new Error("[SecureNetwork] openid and callLoginByWeixin cannot be passed at the same time");if(e)return n.mpWeixinOpenid=e,{};const s=await new Promise(((e,t)=>{uni.login({success(t){e(t.code)},fail(e){t(new Error(e.errMsg))}})})),r=this.importObject("uni-id-co",{customUI:!0});return await r.secureNetworkHandshakeByWeixin({code:s,callLoginByWeixin:t}),n.mpWeixinCode=s,{code:s}}async function js(e){const t=Fs(this);return t.initPromise||(t.initPromise=Ks.call(this,e).then((e=>e)).catch((e=>{throw delete t.initPromise,e}))),t.initPromise}function $s(e){return function({openid:t,callLoginByWeixin:n=!1}={}){return js.call(e,{openid:t,callLoginByWeixin:n})}}function Bs(e){!function(e){ue=e}(e)}function Ws(e){const t={getSystemInfo:uni.getSystemInfo,getPushClientId:uni.getPushClientId};return function(n){return new Promise(((s,r)=>{t[e]({...n,success(e){s(e)},fail(e){r(e)}})}))}}class Hs extends class{constructor(){this._callback={}}addListener(e,t){this._callback[e]||(this._callback[e]=[]),this._callback[e].push(t)}on(e,t){return this.addListener(e,t)}removeListener(e,t){if(!t)throw new Error('The "listener" argument must be of type function. Received undefined');const n=this._callback[e];if(!n)return;const s=function(e,t){for(let n=e.length-1;n>=0;n--)if(e[n]===t)return n;return-1}(n,t);n.splice(s,1)}off(e,t){return this.removeListener(e,t)}removeAllListener(e){delete this._callback[e]}emit(e,...t){const n=this._callback[e];if(n)for(let e=0;e<n.length;e++)n[e](...t)}}{constructor(){super(),this._uniPushMessageCallback=this._receivePushMessage.bind(this),this._currentMessageId=-1,this._payloadQueue=[]}init(){return Promise.all([Ws("getSystemInfo")(),Ws("getPushClientId")()]).then((([{appId:e}={},{cid:t}={}]=[])=>{if(!e)throw new Error("Invalid appId, please check the manifest.json file");if(!t)throw new Error("Invalid push client id");this._appId=e,this._pushClientId=t,this._seqId=Date.now()+"-"+Math.floor(9e5*Math.random()+1e5),this.emit("open"),this._initMessageListener()}),(e=>{throw this.emit("error",e),this.close(),e}))}async open(){return this.init()}_isUniCloudSSE(e){if("receive"!==e.type)return!1;const t=e&&e.data&&e.data.payload;return!(!t||"UNI_CLOUD_SSE"!==t.channel||t.seqId!==this._seqId)}_receivePushMessage(e){if(!this._isUniCloudSSE(e))return;const t=e&&e.data&&e.data.payload,{action:n,messageId:s,message:r}=t;this._payloadQueue.push({action:n,messageId:s,message:r}),this._consumMessage()}_consumMessage(){for(;;){const e=this._payloadQueue.find((e=>e.messageId===this._currentMessageId+1));if(!e)break;this._currentMessageId++,this._parseMessagePayload(e)}}_parseMessagePayload(e){const{action:t,messageId:n,message:s}=e;"end"===t?this._end({messageId:n,message:s}):"message"===t&&this._appendMessage({messageId:n,message:s})}_appendMessage({messageId:e,message:t}={}){this.emit("message",t)}_end({messageId:e,message:t}={}){this.emit("end",t),this.close()}_initMessageListener(){uni.onPushMessage(this._uniPushMessageCallback)}_destroy(){uni.offPushMessage(this._uniPushMessageCallback)}toJSON(){return{appId:this._appId,pushClientId:this._pushClientId,seqId:this._seqId}}close(){this._destroy(),this.emit("close")}}async function Js(e){if(!I)return Promise.resolve();if("app"===A){const{osName:e,osVersion:t}=ae();"ios"===e&&function(e){if(!e||"string"!=typeof e)return 0;const t=e.match(/^(\d+)./);return t&&t[1]?parseInt(t[1]):0}(t)>=14&&console.warn("iOS 14及以上版本连接uniCloud本地调试服务需要允许客户端查找并连接到本地网络上的设备（仅开发期间需要，发行后不需要）")}const t=e.__dev__;if(!t.debugInfo)return;const{address:n,servePort:s}=t.debugInfo,{address:r}=await kt(n,s);if(r)return t.localAddress=r,void(t.localPort=s);const i=console["app"===A?"error":"warn"];let o="";if("remote"===t.debugInfo.initialLaunchType?(t.debugInfo.forceRemote=!0,o="当前客户端和HBuilderX不在同一局域网下（或其他网络原因无法连接HBuilderX），uniCloud本地调试服务不对当前客户端生效。\n- 如果不使用uniCloud本地调试服务，请直接忽略此信息。\n- 如需使用uniCloud本地调试服务，请将客户端与主机连接到同一局域网下并重新运行到客户端。"):o="无法连接uniCloud本地调试服务，请检查当前客户端是否与主机在同一局域网下。\n- 如需使用uniCloud本地调试服务，请将客户端与主机连接到同一局域网下并重新运行到客户端。",o+="\n- 如果在HBuilderX开启的状态下切换过网络环境，请重启HBuilderX后再试\n- 检查系统防火墙是否拦截了HBuilderX自带的nodejs\n- 检查是否错误的使用拦截器修改uni.request方法的参数","web"===A&&(o+="\n- 部分浏览器开启节流模式之后访问本地地址受限，请检查是否启用了节流模式"),0===A.indexOf("mp-")&&(o+="\n- 小程序中如何使用uniCloud，请参考：https://uniapp.dcloud.net.cn/uniCloud/publish.html#useinmp"),!t.debugInfo.forceRemote)throw new Error(o);i(o)}const zs={tcb:St,tencent:St,aliyun:pe,private:Ct,dcloud:Ct,alipay:Mt};let Vs=new class{init(e){let t={};const n=zs[e.provider];if(!n)throw new Error("未提供正确的provider参数");t=n.init(e),I&&function(e){if(!I)return;const t={};e.__dev__=t,t.debugLog=I&&!1;const n=P;n&&!n.code&&(t.debugInfo=n);const s=new w({createPromise:function(){return Js(e)}});t.initLocalNetwork=function(){return s.exec()}}(t),function(e){e._initPromiseHub||(e._initPromiseHub=new w({createPromise:function(){let t=Promise.resolve();const n=e.auth();return t.then((()=>n.getLoginState())).then((e=>e?Promise.resolve():n.signInAnonymously()))}}))}(t),Hn(t),function(e){const t=e.uploadFile;e.uploadFile=function(e){return t.call(this,e)}}(t),function(e){e.database=function(t){if(t&&Object.keys(t).length>0)return e.init(t).database();if(this._database)return this._database;const n=ns(ss,{uniClient:e});return this._database=n,n},e.databaseForJQL=function(t){if(t&&Object.keys(t).length>0)return e.init(t).databaseForJQL();if(this._databaseForJQL)return this._databaseForJQL;const n=ns(ss,{uniClient:e,isJQL:!0});return this._databaseForJQL=n,n}}(t),function(e){e.getCurrentUserInfo=Rs,e.chooseAndUploadFile=Ns.initChooseAndUploadFile(e),Object.assign(e,{get mixinDatacom(){return Ms(e)}}),e.SSEChannel=Hs,e.initSecureNetworkByWeixin=$s(e),e.setCustomClientInfo=Bs,e.importObject=qs(e)}(t);return["callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","chooseAndUploadFile"].forEach((e=>{if(!t[e])return;const n=t[e];t[e]=function(){return n.apply(t,Array.from(arguments))},t[e]=function(e,t){return function(n){let s=!1;if("callFunction"===t){const e=n&&n.type||a;s=e!==a}const r="callFunction"===t&&!s,i=this._initPromiseHub.exec();n=n||{};const{success:o,fail:c,complete:u}=Z(n),l=i.then((()=>s?Promise.resolve():D(M(t,"invoke"),n))).then((()=>e.call(this,n))).then((e=>s?Promise.resolve(e):D(M(t,"success"),e).then((()=>D(M(t,"complete"),e))).then((()=>(r&&G(K,{type:W,content:e}),Promise.resolve(e))))),(e=>s?Promise.reject(e):D(M(t,"fail"),e).then((()=>D(M(t,"complete"),e))).then((()=>(G(K,{type:W,content:e}),Promise.reject(e))))));if(!(o||c||u))return l;l.then((e=>{o&&o(e),u&&u(e),r&&G(K,{type:W,content:e})}),(e=>{c&&c(e),u&&u(e),r&&G(K,{type:W,content:e})}))}}(t[e],e).bind(t)})),t.init=this.init,t}};(()=>{const e=C;let t={};if(e&&1===e.length)t=e[0],Vs=Vs.init(t),Vs._isDefault=!0;else{const t=["auth","callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","database","getCurrentUSerInfo","importObject"];let n;n=e&&e.length>0?"应用有多个服务空间，请通过uniCloud.init方法指定要使用的服务空间":T?"应用未关联服务空间，请在uniCloud目录右键关联服务空间":"uni-app cli项目内使用uniCloud需要使用HBuilderX的运行菜单运行项目，且需要在uniCloud目录关联服务空间",t.forEach((e=>{Vs[e]=function(){return console.error(n),Promise.reject(new ee({code:"SYS_ERR",message:n}))}}))}Object.assign(Vs,{get mixinDatacom(){return Ms(Vs)}}),xs(Vs),Vs.addInterceptor=U,Vs.removeInterceptor=N,Vs.interceptObject=q})();var Gs=Vs;exports.UniCloudError=ee,exports.default=Gs;
