uni-slider {
  display: block;
  margin-top: 1px;
  margin-bottom: 1px;
}

uni-slider[hidden] {
  display: none;
}

uni-slider .uni-slider-browser-input-range {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
}

uni-slider .uni-slider-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  min-height: 8px;
  margin-top: 4px;
  margin-bottom: 4px;
}

uni-slider .uni-slider-input {
  position: relative;
  flex: 1;
}

uni-slider .uni-slider-track {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin-top: auto;
  margin-bottom: auto;
  height: 2px;
  border-radius: 5px;
  background-color: #e9e9e9;
  cursor: pointer;
  transition: background-color 0.3s ease;
  -webkit-tap-highlight-color: transparent;
}

uni-slider .uni-slider-track-value {
  height: 100%;
  border-radius: 6px;
  background-color: #007aff;
  transition: background-color 0.3s ease;
}

uni-slider .uni-slider-thumb-value {
  position: relative;
  cursor: pointer;
  border-radius: 50%;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
  transition: border-color 0.3s ease;
}

uni-slider .uni-slider-value {
  width: 4ch;
  color: #888;
  font-size: 14px;
  margin-left: 3px;
}

uni-slider .uni-slider-disabled .uni-slider-track {
  background-color: #ccc;
}

uni-slider .uni-slider-disabled .uni-slider-thumb-value {
  background-color: #fff;
  border-color: #ccc;
}
