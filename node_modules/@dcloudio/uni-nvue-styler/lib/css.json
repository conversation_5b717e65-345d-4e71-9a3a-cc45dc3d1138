{"version": 1.0, "properties": [{"name": "width", "restrictions": ["length", "percentage"], "syntax": "auto | <length> | <percentage>", "values": [{"name": "auto", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "fit-content", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "max-content", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "min-content", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "auto"}, {"name": "min-width", "restrictions": ["length"], "syntax": "<length>", "values": [{"name": "fit-content", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "max-content", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "min-content", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "auto", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "none", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": {"uvue": "0px", "w3c": "auto"}}, {"name": "max-width", "restrictions": ["length"], "syntax": "<length>", "values": [{"name": "fit-content", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "max-content", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "min-content", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "auto", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "none", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "none"}, {"name": "height", "restrictions": ["length", "percentage"], "syntax": "auto | <length> | <percentage>", "values": [{"name": "auto", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "fit-content", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "max-content", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "min-content", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "auto"}, {"name": "min-height", "restrictions": ["length"], "syntax": "<length>", "values": [{"name": "fit-content", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "max-content", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "min-content", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "auto", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "none", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": {"uvue": "0px", "w3c": "auto"}}, {"name": "max-height", "restrictions": ["length"], "syntax": "<length>", "values": [{"name": "fit-content", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "max-content", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "min-content", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "auto", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "none", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "none"}, {"name": "position", "restrictions": ["enum"], "syntax": " relative | absolute | fixed", "values": [{"name": "relative", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "absolute", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "fixed", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "static", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "sticky", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": {"uvue": "relative", "w3c": "static"}}, {"name": "z-index", "description": "z-index 属性设定了一个定位元素及其后代元素或 flex 项目的 z-order。当元素之间重叠的时候，z-index 较大的元素会覆盖较小的元素在上层进行显示。", "restrictions": ["integer"], "syntax": "auto | <integer>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": {"uvue-app": "0", "uvue-web": "auto", "w3c": "auto"}}, {"name": "top", "restrictions": ["length", "percentage"], "syntax": "<length> | <percentage> | auto", "values": [{"name": "auto", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "auto"}, {"name": "bottom", "restrictions": ["length", "percentage"], "syntax": "<length> | <percentage> | auto", "values": [{"name": "auto", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "auto"}, {"name": "left", "restrictions": ["length", "percentage"], "syntax": "<length> | <percentage> | auto", "values": [{"name": "auto", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "auto"}, {"name": "right", "restrictions": ["length", "percentage"], "syntax": "<length> | <percentage> | auto", "values": [{"name": "auto", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "auto"}, {"name": "flex-direction", "restrictions": ["enum"], "syntax": "row | row-reverse | column | column-reverse", "values": [{"name": "row", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "row-reverse", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "column", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "column-reverse", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "unixTags": ["view", "scroll-view", "list-item", "swiper-item", "navigator"], "defaultValue": {"uvue": "column", "w3c": "row"}}, {"name": "justify-content", "restrictions": ["enum"], "syntax": "center | flex-start | flex-end | space-between | space-around", "values": [{"name": "center", "description": "元素紧密地排列在主轴方向居中对齐", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "flex-start", "description": "元素紧密地排列在容器主轴起始侧", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "flex-end", "description": "元素紧密地排列在容器主轴结束侧", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "space-between", "description": "在主轴上均匀分配元素。相邻元素间距离相同。第一个元素与主轴首对齐，最后一个元素与主轴尾对齐", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "space-around", "description": "元素沿着主轴均匀分布在容器中。相邻项之间的间距，主轴起始位置到第一个元素的间距，主轴结束位置到最后一个元素的间距，都完全一样", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "unixTags": ["view", "scroll-view", "list-view", "list-item", "swiper-item", "navigator"], "defaultValue": {"uvue": "flex-start", "w3c": "normal"}}, {"name": "flex-wrap", "restrictions": ["enum"], "syntax": "nowrap | wrap | wrap-reverse", "values": [{"name": "nowrap", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "wrap", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "wrap-reverse", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "unixTags": ["view", "scroll-view", "list-item", "swiper-item", "navigator"], "defaultValue": "nowrap"}, {"name": "align-items", "restrictions": ["enum"], "syntax": "center | flex-start | flex-end | stretch", "values": [{"name": "center", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "flex-start", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "flex-end", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "stretch", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "baseline", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "unixTags": ["view", "scroll-view", "list-view", "list-item", "swiper-item", "navigator"], "defaultValue": {"uvue": "stretch", "w3c": "normal"}}, {"name": "align-self", "restrictions": ["enum"], "syntax": "auto | center | flex-start | flex-end | stretch", "values": [{"name": "auto", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "center", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "flex-start", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "flex-end", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "stretch", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "baseline", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "auto"}, {"name": "align-content", "restrictions": ["enum"], "description": "CSS 的 align-content 属性用于设置交叉轴方向多个行或列的分布方式", "syntax": "center | flex-start | flex-end | space-between | space-around | stretch", "values": [{"name": "center", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "flex-start", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "flex-end", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "space-between", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "space-around", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "stretch", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "unixTags": ["view", "scroll-view", "list-view", "list-item", "swiper-item", "navigator"], "defaultValue": {"uvue": "stretch", "w3c": "normal"}}, {"name": "flex", "shorthand": true, "restrictions": ["number", "length", "enum"], "syntax": "none | [ <'flex-grow'> <'flex-shrink'>? || <'flex-basis'> ]", "values": [{"name": "initial", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "auto", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "none", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": {"uvue": "none", "w3c": "initial"}}, {"name": "flex-grow", "restrictions": ["number"], "syntax": "<number>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "0"}, {"name": "flex-shrink", "restrictions": ["number"], "syntax": "<number>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": {"uvue": "0", "w3c": "1"}}, {"name": "flex-basis", "restrictions": ["number", "length", "percentage"], "syntax": "auto | <length> | <percentage>", "values": [{"name": "auto", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "content", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "auto"}, {"name": "flex-flow", "shorthand": true, "restrictions": ["enum"], "syntax": "<'flex-direction'> || <'flex-wrap'>", "values": [{"name": "column", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "column-reverse", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "row", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "row-reverse", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "nowrap", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "wrap", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "wrap-reverse", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "unixTags": ["view", "scroll-view", "list-view", "list-item", "swiper-item", "navigator"]}, {"name": "display", "description": "CSS display 属性设置元素的布局方式，默认值为flex（弹性布局）。", "restrictions": ["enum"], "syntax": "flex | none", "values": [{"name": "flex", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "none", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": {"uvue": "flex", "w3c": "inline"}}, {"name": "overflow", "restrictions": ["enum"], "syntax": " visible | hidden ", "values": [{"name": "visible", "description": "暂时仅view组件支持visible，其他组件支持不支持", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "hidden", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "scroll", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "auto", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "clip", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": {"uvue": "hidden", "w3c": "visible"}}, {"name": "visibility", "restrictions": ["enum"], "syntax": "visible | hidden", "values": [{"name": "visible", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "hidden", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "visible"}, {"name": "opacity", "restrictions": ["number(0-1)"], "syntax": "<alpha-value>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "1"}, {"name": "box-sizing", "restrictions": ["enum"], "syntax": "content-box | border-box", "values": [{"name": "content-box", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "border-box", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": {"uvue": "border-box", "w3c": "content-box"}}, {"name": "background", "restrictions": ["color", "gradient"], "syntax": "[ <bg-layer> , ]* <final-bg-layer>", "values": [{"name": "fixed", "uniPlatform": {"app": {"android": {"osVer": "4.4", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "local", "uniPlatform": {"app": {"android": {"osVer": "4.4", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "none", "uniPlatform": {"app": {"android": {"osVer": "4.4", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "scroll", "uniPlatform": {"app": {"android": {"osVer": "4.4", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "background-color", "restrictions": ["color"], "syntax": "<color>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "transparent"}, {"name": "background-image", "restrictions": ["gradient"], "syntax": "none | <bg-image>#", "values": [{"name": "linear-gradient", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "none", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "4.27"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.27"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "background-clip", "restrictions": ["enum"], "syntax": "<box>#", "values": [{"name": "border-box", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "padding-box", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "content-box", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "border-box"}, {"name": "padding", "shorthand": true, "restrictions": ["length", "percentage"], "syntax": "[ <length> | <percentage> ]{1,4}", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "padding-left", "restrictions": ["length", "percentage"], "syntax": "<length> | <percentage>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "0"}, {"name": "padding-top", "restrictions": ["length", "percentage"], "syntax": "<length> | <percentage>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "0"}, {"name": "padding-right", "restrictions": ["length", "percentage"], "syntax": "<length> | <percentage>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "0"}, {"name": "padding-bottom", "restrictions": ["length", "percentage"], "syntax": "<length> | <percentage>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "0"}, {"name": "margin", "shorthand": true, "restrictions": ["length", "percentage"], "syntax": "[ <length> | <percentage> | auto ]{1,4}", "values": [{"name": "auto", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "0"}, {"name": "margin-left", "restrictions": ["length", "percentage"], "syntax": "<length> | <percentage> | auto", "values": [{"name": "auto", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "0"}, {"name": "margin-top", "restrictions": ["length", "percentage"], "syntax": "<length> | <percentage> | auto", "values": [{"name": "auto", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "0"}, {"name": "margin-right", "restrictions": ["length", "percentage"], "syntax": "<length> | <percentage> | auto", "values": [{"name": "auto", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "0"}, {"name": "margin-bottom", "restrictions": ["length", "percentage"], "description": "margin-bottom 属性设置与元素相关联的盒子模型的下外边距。可以为负值", "syntax": "<length> | <percentage> | auto", "values": [{"name": "auto", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "0"}, {"name": "border", "restrictions": ["length", "line-width", "line-style", "color"], "description": "CSS 的 border 是设置元素边框属性的简写形式，用于设置一个或多个以下属性的值：border-width、border-style、border-color。", "syntax": "<line-width> || <line-style> || <color>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": {"uvue": "0"}}, {"name": "border-style", "restrictions": ["enum"], "syntax": "<line-style>{1,4}", "values": [{"name": "none", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "solid", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "dashed", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "dotted", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "border-left-style", "restrictions": ["enum"], "syntax": "<line-style>", "values": [{"name": "none", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "solid", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "dashed", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "dotted", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "border-top-style", "restrictions": ["enum"], "syntax": "<line-style>", "values": [{"name": "none", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "solid", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "dashed", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "dotted", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "border-right-style", "restrictions": ["enum"], "syntax": "<line-style>", "values": [{"name": "none", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "solid", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "dashed", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "dotted", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "border-bottom-style", "restrictions": ["enum"], "syntax": "none | solid | dashed | dotted", "values": [{"name": "none", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "solid", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "dashed", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "dotted", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "border-width", "restrictions": ["length", "enum"], "syntax": "<line-width>{1,4}", "values": [{"name": "thin", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.93"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "medium", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.93"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "thick", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.93"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "medium"}, {"name": "border-left-width", "restrictions": ["length", "enum"], "syntax": "<length> | thin | medium | thick", "values": [{"name": "thin", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.93"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "medium", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.93"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "thick", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.93"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "medium"}, {"name": "border-top-width", "restrictions": ["length", "enum"], "syntax": "<length> | thin | medium | thick", "values": [{"name": "thin", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.93"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "medium", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.93"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "thick", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.93"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "medium"}, {"name": "border-right-width", "restrictions": ["length", "enum"], "syntax": "<length> | thin | medium | thick", "values": [{"name": "thin", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.93"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "medium", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.93"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "thick", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.93"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "medium"}, {"name": "border-bottom-width", "restrictions": ["length", "enum"], "syntax": "<length> | thin | medium | thick", "values": [{"name": "thin", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.93"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "medium", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.93"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "thick", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.93"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "medium"}, {"name": "border-color", "restrictions": ["color"], "syntax": "<color>{1,4}", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "border-left-color", "restrictions": ["color"], "syntax": "<color>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "border-top-color", "restrictions": ["color"], "syntax": "<color>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "border-right-color", "restrictions": ["color"], "syntax": "<color>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "border-bottom-color", "restrictions": ["color"], "syntax": "<color>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "border-radius", "restrictions": ["length"], "syntax": "<length-percentage>{1,4} [ / <length-percentage>{1,4} ]?", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "border-top-left-radius", "restrictions": ["length"], "syntax": "<length-percentage>{1,2}", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "border-top-right-radius", "restrictions": ["length"], "syntax": "<length-percentage>{1,2}", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "border-bottom-left-radius", "restrictions": ["length"], "syntax": "<length-percentage>{1,2}", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "border-bottom-right-radius", "restrictions": ["length"], "syntax": "<length-percentage>{1,2}", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "border-top", "restrictions": ["length", "line-width", "line-style", "color"], "syntax": "<line-width> || <line-style> || <color>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "0"}, {"name": "border-bottom", "restrictions": ["length", "line-width", "line-style", "color"], "syntax": "<line-width> || <line-style> || <color>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "0"}, {"name": "border-left", "restrictions": ["length", "line-width", "line-style", "color"], "syntax": "<line-width> || <line-style> || <color>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "0"}, {"name": "border-right", "restrictions": ["length", "line-width", "line-style", "color"], "syntax": "<line-width> || <line-style> || <color>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "0"}, {"name": "color", "description": "color CSS 属性设置文本及文本装饰（text-decoration）的前景色颜色值。", "restrictions": ["color"], "syntax": "<color>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "unixTags": ["text", "button", "input", "textarea"], "defaultValue": {"uvue": "#000000", "w3c": "canvastext"}}, {"name": "font-size", "description": "font-size CSS 属性设置字体大小。更改字体大小还会更新字体大小相关的 `<length>` 单位，例如 line-height 属性的 em 单位值。", "restrictions": ["length"], "syntax": "<absolute-size> | <relative-size> | <length-percentage>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "unixTags": ["text", "button", "input", "textarea"], "defaultValue": {"uvue": "16px", "w3c": "medium"}}, {"name": "font-style", "restrictions": ["enum"], "syntax": "normal | italic | oblique <angle>{0,2}", "values": [{"name": "italic", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "normal", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "unixTags": ["text", "button"], "defaultValue": "normal"}, {"name": "font-weight", "restrictions": ["enum"], "syntax": "<font-weight-absolute>{1,2}", "values": [{"name": "normal", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "bold", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "400", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "700", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "unixTags": ["text", "button", "input", "textarea"], "defaultValue": "normal"}, {"name": "text-decoration", "restrictions": ["enum", "color", "length"], "syntax": "<'text-decoration-line'> || <'text-decoration-style'> || <'text-decoration-color'> || <'text-decoration-thickness'>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "unixTags": ["text", "button"]}, {"name": "text-decoration-line", "restrictions": ["enum"], "syntax": "none | [ underline || overline || line-through || blink ] | spelling-error | grammar-error", "values": [{"name": "underline", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "line-through", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "overline", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "unixTags": ["text", "button"], "defaultValue": "none"}, {"name": "text-decoration-color", "restrictions": ["color"], "syntax": "<color>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "unixTags": ["text", "button"]}, {"name": "text-decoration-style", "restrictions": ["enum"], "syntax": "solid | double | dotted | dashed | wavy", "values": [{"name": "solid", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "dashed", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "dotted", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "wavy", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "unixTags": ["text", "button"]}, {"name": "text-decoration-thickness", "restrictions": ["length"], "syntax": "auto | from-font | <length> | <percentage> ", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "unixTags": ["text", "button"]}, {"name": "text-align", "restrictions": ["enum"], "syntax": "start | end | left | right | center | justify | match-parent", "values": [{"name": "left", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "center", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "right", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "unixTags": ["text", "button", "input", "textarea"], "defaultValue": {"uvue-app": "left", "uvue-web": "left", "w3c": "start"}}, {"name": "font-family", "restrictions": ["font"], "syntax": "<family-name>", "values": [{"name": "cursive", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "fantasy", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "monospace", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "sans-serif", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "serif", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "unixTags": ["text", "button", "input", "textarea"]}, {"name": "text-overflow", "restrictions": ["enum"], "syntax": "[ clip | ellipsis | <string> ]{1,2}", "values": [{"name": "clip", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "ellipsis", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "unixTags": ["text", "button"], "defaultValue": "clip"}, {"name": "line-height", "restrictions": ["length", "number"], "syntax": "normal | <number> | <length> | <percentage>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "unixTags": ["text", "button", "textarea"], "defaultValue": {"uvue": "1.2", "w3c": "normal"}}, {"name": "lines", "description": "text 组件专有样式，设置文本组件最大行数", "restrictions": ["integer"], "syntax": "<integer>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "x", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "x", "unixVer": "4.11"}}, "web": {"uniVer": "x", "unixVer": "x"}}, "unixTags": ["text", "button", "input", "textarea"], "defaultValue": "-1"}, {"name": "letter-spacing", "restrictions": ["length"], "syntax": "normal | <length>", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "unixTags": ["text", "button"], "defaultValue": {"uvue": "0", "w3c": "normal"}}, {"name": "white-space", "restrictions": ["enum"], "syntax": "normal | pre | nowrap | pre-wrap | pre-line | break-spaces | [ <'white-space-collapse'> || <'text-wrap'> || <'white-space-trim'> ]", "values": [{"name": "normal", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "4.0"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "nowrap", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "4.0"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "4.0"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "unixTags": ["text", "button"], "defaultValue": "normal"}, {"name": "box-shadow", "restrictions": ["enum", "length", "color"], "syntax": "none | <shadow>#", "values": [{"name": "inset", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "none", "uniPlatform": {"app": {"android": {"osVer": "x", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "x", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "transition", "restrictions": ["time", "enum"], "syntax": "<single-transition>#", "values": [{"name": "all", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "4.13"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.13"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "none", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "4.13"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.13"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "transition-property", "restrictions": ["enum"], "syntax": " all | none | <single-transition-property>#", "values": [{"name": "all", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "4.13"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.13"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "none", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "4.13"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.13"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "width", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "height", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "margin", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "margin-top", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "margin-bottom", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "margin-left", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "margin-right", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "left", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "right", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "top", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "bottom", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "padding", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "padding-left", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "padding-right", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "padding-top", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "padding-bottom", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "opacity", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "background-color", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "border-color", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "border-top-color", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "border-bottom-color", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "border-left-color", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "border-right-color", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "transform", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": {"uvue-app": "all", "uvue-web": "all", "w3c": "all"}}, {"name": "transition-duration", "restrictions": ["time"], "syntax": "<time>#", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "0s"}, {"name": "transition-timing-function", "restrictions": ["enum"], "syntax": "<easing-function>#", "values": [{"name": "ease", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "ease-in", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "ease-out", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "ease-in-out", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "linear", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "cubic-bezier", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "ease"}, {"name": "transition-delay", "restrictions": ["time"], "syntax": "<time>#", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "0s"}, {"name": "transform", "restrictions": ["enum"], "syntax": "none | <transform-list>", "values": [{"name": "rotate(<angle>)", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "rotateX(<angle>)", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "rotateY(<angle>)", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "rotateZ(<angle>)", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "scale(<number> <number>)", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "scaleX(<number>)", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "scaleY(<number>)", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "translate(<length/percentage> <length/percentage>)", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "translateX(<length/percentage>)", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "translateY(<length/percentage>)", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "transform-origin", "shorthand": true, "restrictions": ["length", "percentage", "enum"], "syntax": "[ <length-percentage> | left | center | right | top | bottom ] | [ [ <length-percentage> | left | center | right ] && [ <length-percentage> | top | center | bottom ] ] <length>?", "values": [{"name": "top", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "left", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "right", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "bottom", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "center", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": {"uvue-app": "50% 50%", "uvue-web": "50% 50% 0", "w3c": "50% 50% 0"}}, {"name": "pointer-events", "restrictions": ["enum"], "syntax": "auto | none", "values": [{"name": "auto", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "none", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}], "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "x", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "x", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}, "defaultValue": "auto"}], "atDirectives": [{"name": "@import", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "@font-face", "uniPlatform": {"app": {"android": {"osVer": "5.0", "uniVer": "√", "unixVer": "3.9+"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "4.11"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "@charset", "uniPlatform": {"app": {"android": {"osVer": "x", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "@color-profile", "description": "@color-profile CSS at-rule 定义并命名一个颜色配置文件，稍后可以在 color() 函数中使用该配置文件来指定颜色。", "uniPlatform": {"app": {"android": {"osVer": "x", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "@container", "description": "@container CSS at-rule 是将样式应用于包含上下文的条件组规则。 样式声明按条件筛选，如果条件为 true，则应用于容器。 当容器更改大小时，将评估条件。你还可以给这个收纳盒贴上一个名字，这样它就会专门整理那些名字相同的容器。当它整理容器的时候，会仔细检查每个容器的所有细节，确保所有条件都符合才会应用样式。", "uniPlatform": {"app": {"android": {"osVer": "x", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "@counter-style", "uniPlatform": {"app": {"android": {"osVer": "x", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "@document", "description": "@document CSS at 规则根据文档的 URL 限制其中包含的样式规则。它主要用于用户定义的样式表，但也可用于作者定义的样式表。", "uniPlatform": {"app": {"android": {"osVer": "x", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "@font-feature-values", "uniPlatform": {"app": {"android": {"osVer": "x", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "@font-palette-values", "description": "@font-palette-values CSS at-rule 允许您自定义字体制作者创建的字体调色板的默认值。", "uniPlatform": {"app": {"android": {"osVer": "x", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "@keyframes", "uniPlatform": {"app": {"android": {"osVer": "x", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "@layer", "uniPlatform": {"app": {"android": {"osVer": "x", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "@media", "uniPlatform": {"app": {"android": {"osVer": "x", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "@namespace", "uniPlatform": {"app": {"android": {"osVer": "x", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "@page", "uniPlatform": {"app": {"android": {"osVer": "x", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "@property", "uniPlatform": {"app": {"android": {"osVer": "x", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}, {"name": "@supports", "uniPlatform": {"app": {"android": {"osVer": "x", "uniVer": "√", "unixVer": "x"}, "ios": {"osVer": "12.0", "uniVer": "√", "unixVer": "x"}}, "web": {"uniVer": "√", "unixVer": "4.0"}}}]}