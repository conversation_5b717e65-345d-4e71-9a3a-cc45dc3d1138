import type { InlineConfig, Logger } from 'vite';
import type { CliOptions } from '.';
export declare const PLATFORMS: string[];
export type PLATFORM = 'app' | 'mp-alipay' | 'mp-baidu' | 'mp-kuaishou' | 'mp-lark' | 'mp-qq' | 'mp-toutiao' | 'mp-weixin' | 'quickapp-webview' | 'quickapp-webview-huawei' | 'quickapp-webview-union';
export declare function addConfigFile(inlineConfig: InlineConfig): InlineConfig;
export declare function initEnv(type: 'unknown' | 'dev' | 'build', options: CliOptions): void;
export declare function cleanOptions(options: CliOptions): {
    '--'?: string[] | undefined;
    c?: string | boolean | undefined;
    config?: string | undefined;
    platform?: string | undefined;
    p?: string | undefined;
    ssr?: boolean | undefined;
    base?: string | undefined;
    debug?: string | boolean | undefined;
    d?: string | boolean | undefined;
    filter?: string | undefined;
    f?: string | undefined;
    logLevel?: import("vite").LogLevel | undefined;
    l?: import("vite").LogLevel | undefined;
    m?: string | undefined;
    mode?: string | undefined;
    clearScreen?: boolean | undefined;
    autoHost?: string | undefined;
    autoPort?: number | undefined;
    devtools?: boolean | undefined;
    devtoolsHost?: string | undefined;
    devtoolsPort?: number | undefined;
    subpackage?: string | undefined;
    plugin?: boolean | undefined;
};
export declare function printStartupDuration(logger: Logger, whitespace?: boolean): void;
export declare function showRunPrompt(platform: PLATFORM): void;
