"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.runBuild = exports.runDev = void 0;
const fs_1 = __importDefault(require("fs"));
const vite_1 = __importDefault(require("unplugin-auto-import/vite"));
const plugin_vue_jsx_1 = __importDefault(require("@vitejs/plugin-vue-jsx"));
const plugin_legacy_1 = __importDefault(require("@vitejs/plugin-legacy"));
const uni_cli_shared_1 = require("@dcloudio/uni-cli-shared");
const config_1 = require("./config");
const configResolved_1 = require("./configResolved");
const copy_1 = require("./plugins/copy");
const move_1 = require("./plugins/move");
const utils_1 = require("./utils");
const vue_1 = require("./vue");
const utils_2 = require("./cli/utils");
const plugins_1 = require("./uvue/plugins");
// const debugUni = debug('uni:plugin')
(0, uni_cli_shared_1.initModuleAlias)();
var action_1 = require("./cli/action");
Object.defineProperty(exports, "runDev", { enumerable: true, get: function () { return action_1.runDev; } });
Object.defineProperty(exports, "runBuild", { enumerable: true, get: function () { return action_1.runBuild; } });
let isFirst = true;
function uniPlugin(rawOptions = {}) {
    // 重写readFileSync，拦截.scss 等文件读取，实现条件编译，
    (0, uni_cli_shared_1.rewriteScssReadFileSync)();
    if ((0, uni_cli_shared_1.isInHBuilderX)()) {
        (0, uni_cli_shared_1.rewriteExistsSyncHasRootFile)();
    }
    // 三方插件（如vitest）可能提供了自己的入口命令，需要补充 env 初始化逻辑
    (0, utils_2.initEnv)('unknown', { platform: process.env.UNI_PLATFORM || 'h5' });
    const options = {
        ...rawOptions,
        base: '/',
        assetsDir: 'assets',
        inputDir: '',
        outputDir: '',
        command: 'serve',
        platform: 'h5',
    };
    options.platform = process.env.UNI_PLATFORM || 'h5';
    options.inputDir = process.env.UNI_INPUT_DIR;
    const plugins = process.env.UNI_APP_X === 'true' &&
        process.env.UNI_UTS_PLATFORM === 'app-android'
        ? createUVueAndroidPlugins(options)
        : createPlugins(options);
    // x 提供 auto import（非android、android自行处理）
    if (process.env.UNI_APP_X === 'true' &&
        process.env.UNI_UTS_PLATFORM !== 'app-android') {
        plugins.unshift((0, vite_1.default)((0, uni_cli_shared_1.initAutoImportOptions)(process.env.UNI_UTS_PLATFORM, options.autoImportOptions || {})));
    }
    plugins.unshift((0, uni_cli_shared_1.uniJsonPlugin)());
    return plugins;
}
exports.default = uniPlugin;
function createPlugins(options) {
    const plugins = [];
    // uni x 需要插入到指定位置，此插件执行太早，又会引发 vue 文件的不支持，该插件是解析ast的，所以必须是合法的js或ts代码
    if (process.env.UNI_APP_X === 'true' &&
        // iOS 暂不使用该机制
        process.env.UNI_UTS_PLATFORM !== 'app-ios') {
        plugins.push((0, uni_cli_shared_1.uniUTSExtApiReplace)());
    }
    else {
        const injects = (0, uni_cli_shared_1.parseUniExtApisOnce)(true, process.env.UNI_UTS_PLATFORM, process.env.UNI_UTS_TARGET_LANGUAGE);
        if (Object.keys(injects).length) {
            plugins.push((0, uni_cli_shared_1.uniViteInjectPlugin)('uni:ext-api-inject', injects));
        }
    }
    // 仅限 h5
    if (options.viteLegacyOptions && options.platform === 'h5') {
        plugins.push(...(0, plugin_legacy_1.default)((0, vue_1.initPluginViteLegacyOptions)(options)));
    }
    const uniPlugins = (0, utils_1.initExtraPlugins)(process.env.UNI_CLI_CONTEXT || process.cwd(), process.env.UNI_PLATFORM || 'h5', options);
    // debugUni(uniPlugins)
    const uniPluginOptions = (0, utils_1.initPluginUniOptions)(uniPlugins);
    options.copyOptions = uniPluginOptions.copyOptions;
    if (options.vueJsxOptions) {
        plugins.push((0, plugin_vue_jsx_1.default)((0, vue_1.initPluginVueJsxOptions)(options, uniPluginOptions.compilerOptions, uniPluginOptions.jsxOptions)));
    }
    plugins.push({
        name: 'uni',
        config: (0, config_1.createConfig)(options, uniPlugins),
        configResolved: (0, configResolved_1.createConfigResolved)(options),
    });
    plugins.push(...uniPlugins);
    // plugins.push(...initFixedEsbuildInitTSConfck(process.env.UNI_INPUT_DIR))
    // 执行 build 命令时，vite 强制了 NODE_ENV
    // https://github.com/vitejs/vite/blob/main/packages/vite/src/node/build.ts#L405
    // const config = await resolveConfig(inlineConfig, 'build', 'production')
    // 在 @vitejs/plugin-vue 之前校正回来
    if (process.env.UNI_NODE_ENV &&
        process.env.UNI_NODE_ENV !== process.env.NODE_ENV) {
        process.env.NODE_ENV = process.env.UNI_NODE_ENV;
    }
    plugins.unshift((0, vue_1.createPluginVueInstance)((0, vue_1.initPluginVueOptions)(options, uniPlugins, uniPluginOptions)));
    let addCopyPlugin = false;
    if (options.platform !== 'app') {
        addCopyPlugin = true;
    }
    else {
        // 仅在 vue 或 纯原生 App.vue 编译时做 copy
        if (process.env.UNI_COMPILER !== 'nvue' ||
            (process.env.UNI_RENDERER === 'native' &&
                process.env.UNI_RENDERER_NATIVE === 'appService')) {
            addCopyPlugin = true;
        }
        else if (process.env.UNI_APP_X === 'true') {
            // app-ios
            addCopyPlugin = true;
        }
    }
    if (process.env.UNI_COMPILE_TARGET === 'uni_modules') {
        addCopyPlugin = false;
    }
    if (addCopyPlugin) {
        plugins.push((0, copy_1.uniCopyPlugin)({
            outputDir: process.env.UNI_OUTPUT_DIR,
            copyOptions: options.copyOptions,
        }));
    }
    if (process.env.SOURCEMAP === 'true') {
        // 清空之前的 sourcemap 目录
        const sourceMapPath = (0, uni_cli_shared_1.resolveSourceMapPath)();
        if (isFirst) {
            // 避免重复清空
            isFirst = false;
            if (fs_1.default.existsSync(sourceMapPath)) {
                (0, uni_cli_shared_1.emptyDir)(sourceMapPath);
            }
            plugins.push((0, move_1.uniMovePlugin)({
                apply: 'build',
                enforce: 'post',
                cwd: process.env.UNI_OUTPUT_DIR,
                pattern: '**/*.js.map',
                dest: sourceMapPath,
            }));
        }
    }
    (0, utils_1.rewriteCompilerSfcParse)();
    return plugins;
}
function createUVueAndroidPlugins(options) {
    const plugins = [];
    options.uvue = true;
    const uniPlugins = (0, utils_1.initExtraPlugins)(process.env.UNI_CLI_CONTEXT || process.cwd(), process.env.UNI_PLATFORM || 'h5', options);
    // debugUni(uniPlugins)
    const uniPluginOptions = (0, utils_1.initPluginUniOptions)(uniPlugins);
    options.copyOptions = uniPluginOptions.copyOptions;
    plugins.push((0, plugins_1.uniUVuePlugin)(options));
    plugins.push(...uniPlugins);
    // plugins.push(...initFixedEsbuildInitTSConfck(process.env.UNI_INPUT_DIR))
    // 执行 build 命令时，vite 强制了 NODE_ENV
    // https://github.com/vitejs/vite/blob/main/packages/vite/src/node/build.ts#L405
    // const config = await resolveConfig(inlineConfig, 'build', 'production')
    // 在 @vitejs/plugin-vue 之前校正回来
    if (process.env.UNI_NODE_ENV &&
        process.env.UNI_NODE_ENV !== process.env.NODE_ENV) {
        process.env.NODE_ENV = process.env.UNI_NODE_ENV;
    }
    if (process.env.UNI_COMPILE_TARGET !== 'uni_modules') {
        plugins.push((0, copy_1.uniCopyPlugin)({
            outputDir: process.env.UNI_OUTPUT_DIR,
            copyOptions: options.copyOptions,
        }));
    }
    return plugins;
}
