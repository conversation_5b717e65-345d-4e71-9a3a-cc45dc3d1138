{"version": 3, "sources": ["../src/index.js"], "names": ["pluginCrop", "event", "x", "y", "w", "h", "cb", "throwError", "call", "Math", "round", "bitmap", "width", "start", "end", "data", "slice", "<PERSON><PERSON><PERSON>", "allocUnsafe", "offset", "scanQuiet", "idx", "readUInt32BE", "writeUInt32BE", "height", "autocrop", "minPixelsPerSide", "leaveBorder", "tolerance", "cropOnlyFrames", "cropSymmetric", "args", "a", "len", "length", "config", "colorTarget", "getPixelColor", "rgba1", "constructor", "intToRGBA", "northPixelsToCrop", "eastPixelsToCrop", "southPixelsToCrop", "westPixelsToCrop", "north", "colorXY", "rgba2", "colorDiff", "east", "south", "west", "doCrop", "horizontal", "min", "vertical", "widthOfRemainingPixels", "heightOfRemainingPixels", "crop"], "mappings": ";;;;;;;;;;;AAEA;;AAFA;AAIe,SAASA,UAAT,CAAoBC,KAApB,EAA2B;AACxC;;;;;;;;;AASAA,EAAAA,KAAK,CAAC,MAAD,EAAS,UAASC,CAAT,EAAYC,CAAZ,EAAeC,CAAf,EAAkBC,CAAlB,EAAqBC,EAArB,EAAyB;AACrC,QAAI,OAAOJ,CAAP,KAAa,QAAb,IAAyB,OAAOC,CAAP,KAAa,QAA1C,EACE,OAAOI,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,yBAAtB,EAAiDF,EAAjD,CAAP;AACF,QAAI,OAAOF,CAAP,KAAa,QAAb,IAAyB,OAAOC,CAAP,KAAa,QAA1C,EACE,OAAOE,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,yBAAtB,EAAiDF,EAAjD,CAAP,CAJmC,CAMrC;;AACAJ,IAAAA,CAAC,GAAGO,IAAI,CAACC,KAAL,CAAWR,CAAX,CAAJ;AACAC,IAAAA,CAAC,GAAGM,IAAI,CAACC,KAAL,CAAWP,CAAX,CAAJ;AACAC,IAAAA,CAAC,GAAGK,IAAI,CAACC,KAAL,CAAWN,CAAX,CAAJ;AACAC,IAAAA,CAAC,GAAGI,IAAI,CAACC,KAAL,CAAWL,CAAX,CAAJ;;AAEA,QAAIH,CAAC,KAAK,CAAN,IAAWE,CAAC,KAAK,KAAKO,MAAL,CAAYC,KAAjC,EAAwC;AACtC;AACA,UAAMC,KAAK,GAAIT,CAAC,GAAGD,CAAJ,GAAQD,CAAT,IAAe,CAA7B;AACA,UAAMY,GAAG,GAAID,KAAK,GAAGR,CAAC,GAAGD,CAAb,IAAmB,CAA/B;AAEA,WAAKO,MAAL,CAAYI,IAAZ,GAAmB,KAAKJ,MAAL,CAAYI,IAAZ,CAAiBC,KAAjB,CAAuBH,KAAvB,EAA8BC,GAA9B,CAAnB;AACD,KAND,MAMO;AACL,UAAMH,MAAM,GAAGM,MAAM,CAACC,WAAP,CAAmBd,CAAC,GAAGC,CAAJ,GAAQ,CAA3B,CAAf;AACA,UAAIc,MAAM,GAAG,CAAb;AAEA,WAAKC,SAAL,CAAelB,CAAf,EAAkBC,CAAlB,EAAqBC,CAArB,EAAwBC,CAAxB,EAA2B,UAASH,CAAT,EAAYC,CAAZ,EAAekB,GAAf,EAAoB;AAC7C,YAAMN,IAAI,GAAG,KAAKJ,MAAL,CAAYI,IAAZ,CAAiBO,YAAjB,CAA8BD,GAA9B,EAAmC,IAAnC,CAAb;AACAV,QAAAA,MAAM,CAACY,aAAP,CAAqBR,IAArB,EAA2BI,MAA3B,EAAmC,IAAnC;AACAA,QAAAA,MAAM,IAAI,CAAV;AACD,OAJD;AAMA,WAAKR,MAAL,CAAYI,IAAZ,GAAmBJ,MAAnB;AACD;;AAED,SAAKA,MAAL,CAAYC,KAAZ,GAAoBR,CAApB;AACA,SAAKO,MAAL,CAAYa,MAAZ,GAAqBnB,CAArB;;AAEA,QAAI,0BAAcC,EAAd,CAAJ,EAAuB;AACrBA,MAAAA,EAAE,CAACE,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,WAAO,IAAP;AACD,GAvCI,CAAL;AAyCA,SAAO;AACL,aAAO;AACL;;;;;;;AAOAiB,MAAAA,QARK,sBAQa;AAChB,YAAMrB,CAAC,GAAG,KAAKO,MAAL,CAAYC,KAAtB;AACA,YAAMP,CAAC,GAAG,KAAKM,MAAL,CAAYa,MAAtB;AACA,YAAME,gBAAgB,GAAG,CAAzB,CAHgB,CAGY;;AAE5B,YAAIpB,EAAJ,CALgB,CAKR;;AACR,YAAIqB,WAAW,GAAG,CAAlB,CANgB,CAMK;;AACrB,YAAIC,SAAS,GAAG,MAAhB,CAPgB,CAOQ;;AACxB,YAAIC,cAAc,GAAG,IAArB,CARgB,CAQW;AAC3B;;AACA,YAAIC,aAAa,GAAG,KAApB,CAVgB,CAUW;AAC3B;AAEA;;AAbgB,0CAANC,IAAM;AAANA,UAAAA,IAAM;AAAA;;AAchB,aAAK,IAAIC,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGF,IAAI,CAACG,MAA3B,EAAmCF,CAAC,GAAGC,GAAvC,EAA4CD,CAAC,EAA7C,EAAiD;AAC/C,cAAI,OAAOD,IAAI,CAACC,CAAD,CAAX,KAAmB,QAAvB,EAAiC;AAC/B;AACAJ,YAAAA,SAAS,GAAGG,IAAI,CAACC,CAAD,CAAhB;AACD;;AAED,cAAI,OAAOD,IAAI,CAACC,CAAD,CAAX,KAAmB,SAAvB,EAAkC;AAChC;AACAH,YAAAA,cAAc,GAAGE,IAAI,CAACC,CAAD,CAArB;AACD;;AAED,cAAI,OAAOD,IAAI,CAACC,CAAD,CAAX,KAAmB,UAAvB,EAAmC;AACjC;AACA1B,YAAAA,EAAE,GAAGyB,IAAI,CAACC,CAAD,CAAT;AACD;;AAED,cAAI,yBAAOD,IAAI,CAACC,CAAD,CAAX,MAAmB,QAAvB,EAAiC;AAC/B;AACA,gBAAMG,MAAM,GAAGJ,IAAI,CAACC,CAAD,CAAnB;;AAEA,gBAAI,OAAOG,MAAM,CAACP,SAAd,KAA4B,WAAhC,EAA6C;AACxCA,cAAAA,SADwC,GAC1BO,MAD0B,CACxCP,SADwC;AAE5C;;AAED,gBAAI,OAAOO,MAAM,CAACN,cAAd,KAAiC,WAArC,EAAkD;AAC7CA,cAAAA,cAD6C,GAC1BM,MAD0B,CAC7CN,cAD6C;AAEjD;;AAED,gBAAI,OAAOM,MAAM,CAACL,aAAd,KAAgC,WAApC,EAAiD;AAC5CA,cAAAA,aAD4C,GAC1BK,MAD0B,CAC5CL,aAD4C;AAEhD;;AAED,gBAAI,OAAOK,MAAM,CAACR,WAAd,KAA8B,WAAlC,EAA+C;AAC1CA,cAAAA,WAD0C,GAC1BQ,MAD0B,CAC1CR,WAD0C;AAE9C;AACF;AACF;AAED;;;;;;AAOA;;;AACA,YAAIS,WAAW,GAAG,KAAKC,aAAL,CAAmB,CAAnB,EAAsB,CAAtB,CAAlB,CA5DgB,CA4D4B;;AAC5C,YAAMC,KAAK,GAAG,KAAKC,WAAL,CAAiBC,SAAjB,CAA2BJ,WAA3B,CAAd,CA7DgB,CA+DhB;;AACA,YAAIK,iBAAiB,GAAG,CAAxB;AACA,YAAIC,gBAAgB,GAAG,CAAvB;AACA,YAAIC,iBAAiB,GAAG,CAAxB;AACA,YAAIC,gBAAgB,GAAG,CAAvB,CAnEgB,CAqEhB;;AACAR,QAAAA,WAAW,GAAG,KAAKC,aAAL,CAAmB,CAAnB,EAAsB,CAAtB,CAAd;;AACAQ,QAAAA,KAAK,EAAE,KAAK,IAAI1C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGE,CAAC,GAAGqB,gBAAxB,EAA0CvB,CAAC,EAA3C,EAA+C;AACpD,eAAK,IAAID,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGE,CAApB,EAAuBF,CAAC,EAAxB,EAA4B;AAC1B,gBAAM4C,OAAO,GAAG,KAAKT,aAAL,CAAmBnC,CAAnB,EAAsBC,CAAtB,CAAhB;AACA,gBAAM4C,KAAK,GAAG,KAAKR,WAAL,CAAiBC,SAAjB,CAA2BM,OAA3B,CAAd;;AAEA,gBAAI,KAAKP,WAAL,CAAiBS,SAAjB,CAA2BV,KAA3B,EAAkCS,KAAlC,IAA2CnB,SAA/C,EAA0D;AACxD;AACA,oBAAMiB,KAAN;AACD;AACF,WATmD,CAWpD;;;AACAJ,UAAAA,iBAAiB;AAClB,SApFe,CAsFhB;;;AACAL,QAAAA,WAAW,GAAG,KAAKC,aAAL,CAAmBjC,CAAnB,EAAsB,CAAtB,CAAd;;AACA6C,QAAAA,IAAI,EAAE,KAAK,IAAI/C,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAGE,CAAC,GAAGsB,gBAAxB,EAA0CxB,EAAC,EAA3C,EAA+C;AACnD,eAAK,IAAIC,EAAC,GAAG,IAAIsC,iBAAjB,EAAoCtC,EAAC,GAAGE,CAAxC,EAA2CF,EAAC,EAA5C,EAAgD;AAC9C,gBAAM2C,QAAO,GAAG,KAAKT,aAAL,CAAmBnC,EAAnB,EAAsBC,EAAtB,CAAhB;;AACA,gBAAM4C,KAAK,GAAG,KAAKR,WAAL,CAAiBC,SAAjB,CAA2BM,QAA3B,CAAd;;AAEA,gBAAI,KAAKP,WAAL,CAAiBS,SAAjB,CAA2BV,KAA3B,EAAkCS,KAAlC,IAA2CnB,SAA/C,EAA0D;AACxD;AACA,oBAAMqB,IAAN;AACD;AACF,WATkD,CAWnD;;;AACAP,UAAAA,gBAAgB;AACjB,SArGe,CAuGhB;;;AACAN,QAAAA,WAAW,GAAG,KAAKC,aAAL,CAAmB,CAAnB,EAAsBhC,CAAtB,CAAd;;AACA6C,QAAAA,KAAK,EAAE,KACL,IAAI/C,GAAC,GAAGE,CAAC,GAAG,CADP,EAELF,GAAC,IAAIsC,iBAAiB,GAAGf,gBAFpB,EAGLvB,GAAC,EAHI,EAIL;AACA,eAAK,IAAID,GAAC,GAAGE,CAAC,GAAGsC,gBAAJ,GAAuB,CAApC,EAAuCxC,GAAC,IAAI,CAA5C,EAA+CA,GAAC,EAAhD,EAAoD;AAClD,gBAAM4C,SAAO,GAAG,KAAKT,aAAL,CAAmBnC,GAAnB,EAAsBC,GAAtB,CAAhB;;AACA,gBAAM4C,MAAK,GAAG,KAAKR,WAAL,CAAiBC,SAAjB,CAA2BM,SAA3B,CAAd;;AAEA,gBAAI,KAAKP,WAAL,CAAiBS,SAAjB,CAA2BV,KAA3B,EAAkCS,MAAlC,IAA2CnB,SAA/C,EAA0D;AACxD;AACA,oBAAMsB,KAAN;AACD;AACF,WATD,CAWA;;;AACAP,UAAAA,iBAAiB;AAClB,SA1He,CA4HhB;;;AACAP,QAAAA,WAAW,GAAG,KAAKC,aAAL,CAAmBjC,CAAnB,EAAsBC,CAAtB,CAAd;;AACA8C,QAAAA,IAAI,EAAE,KACJ,IAAIjD,GAAC,GAAGE,CAAC,GAAG,CADR,EAEJF,GAAC,IAAI,IAAIwC,gBAAJ,GAAuBhB,gBAFxB,EAGJxB,GAAC,EAHG,EAIJ;AACA,eAAK,IAAIC,GAAC,GAAGE,CAAC,GAAG,CAAjB,EAAoBF,GAAC,IAAI,IAAIsC,iBAA7B,EAAgDtC,GAAC,EAAjD,EAAqD;AACnD,gBAAM2C,SAAO,GAAG,KAAKT,aAAL,CAAmBnC,GAAnB,EAAsBC,GAAtB,CAAhB;;AACA,gBAAM4C,MAAK,GAAG,KAAKR,WAAL,CAAiBC,SAAjB,CAA2BM,SAA3B,CAAd;;AAEA,gBAAI,KAAKP,WAAL,CAAiBS,SAAjB,CAA2BV,KAA3B,EAAkCS,MAAlC,IAA2CnB,SAA/C,EAA0D;AACxD;AACA,oBAAMuB,IAAN;AACD;AACF,WATD,CAWA;;;AACAP,UAAAA,gBAAgB;AACjB,SA/Ie,CAiJhB;;;AACA,YAAIQ,MAAM,GAAG,KAAb,CAlJgB,CAoJhB;;AACAR,QAAAA,gBAAgB,IAAIjB,WAApB;AACAe,QAAAA,gBAAgB,IAAIf,WAApB;AACAc,QAAAA,iBAAiB,IAAId,WAArB;AACAgB,QAAAA,iBAAiB,IAAIhB,WAArB;;AAEA,YAAIG,aAAJ,EAAmB;AACjB,cAAMuB,UAAU,GAAG5C,IAAI,CAAC6C,GAAL,CAASZ,gBAAT,EAA2BE,gBAA3B,CAAnB;AACA,cAAMW,QAAQ,GAAG9C,IAAI,CAAC6C,GAAL,CAASb,iBAAT,EAA4BE,iBAA5B,CAAjB;AACAC,UAAAA,gBAAgB,GAAGS,UAAnB;AACAX,UAAAA,gBAAgB,GAAGW,UAAnB;AACAZ,UAAAA,iBAAiB,GAAGc,QAApB;AACAZ,UAAAA,iBAAiB,GAAGY,QAApB;AACD,SAjKe,CAmKhB;;;AACAX,QAAAA,gBAAgB,GAAGA,gBAAgB,IAAI,CAApB,GAAwBA,gBAAxB,GAA2C,CAA9D;AACAF,QAAAA,gBAAgB,GAAGA,gBAAgB,IAAI,CAApB,GAAwBA,gBAAxB,GAA2C,CAA9D;AACAD,QAAAA,iBAAiB,GAAGA,iBAAiB,IAAI,CAArB,GAAyBA,iBAAzB,GAA6C,CAAjE;AACAE,QAAAA,iBAAiB,GAAGA,iBAAiB,IAAI,CAArB,GAAyBA,iBAAzB,GAA6C,CAAjE,CAvKgB,CAyKhB;;AACA,YAAMa,sBAAsB,GAC1BpD,CAAC,IAAIwC,gBAAgB,GAAGF,gBAAvB,CADH;AAEA,YAAMe,uBAAuB,GAC3BpD,CAAC,IAAIsC,iBAAiB,GAAGF,iBAAxB,CADH;;AAGA,YAAIZ,cAAJ,EAAoB;AAClB;AACAuB,UAAAA,MAAM,GACJV,gBAAgB,KAAK,CAArB,IACAD,iBAAiB,KAAK,CADtB,IAEAG,gBAAgB,KAAK,CAFrB,IAGAD,iBAAiB,KAAK,CAJxB;AAKD,SAPD,MAOO;AACL;AACAS,UAAAA,MAAM,GACJV,gBAAgB,KAAK,CAArB,IACAD,iBAAiB,KAAK,CADtB,IAEAG,gBAAgB,KAAK,CAFrB,IAGAD,iBAAiB,KAAK,CAJxB;AAKD;;AAED,YAAIS,MAAJ,EAAY;AACV;AACA,eAAKM,IAAL,CACEhB,gBADF,EAEED,iBAFF,EAGEe,sBAHF,EAIEC,uBAJF;AAMD;;AAED,YAAI,0BAAcnD,EAAd,CAAJ,EAAuB;AACrBA,UAAAA,EAAE,CAACE,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,eAAO,IAAP;AACD;AAtNI;AADF,GAAP;AA0ND", "sourcesContent": ["/* eslint-disable no-labels */\n\nimport { throwError, isNodePattern } from '@jimp/utils';\n\nexport default function pluginCrop(event) {\n  /**\n   * Crops the image at a given point to a give size\n   * @param {number} x the x coordinate to crop form\n   * @param {number} y the y coordinate to crop form\n   * @param w the width of the crop region\n   * @param h the height of the crop region\n   * @param {function(<PERSON><PERSON><PERSON>, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp} this for chaining of methods\n   */\n  event('crop', function(x, y, w, h, cb) {\n    if (typeof x !== 'number' || typeof y !== 'number')\n      return throwError.call(this, 'x and y must be numbers', cb);\n    if (typeof w !== 'number' || typeof h !== 'number')\n      return throwError.call(this, 'w and h must be numbers', cb);\n\n    // round input\n    x = Math.round(x);\n    y = Math.round(y);\n    w = Math.round(w);\n    h = Math.round(h);\n\n    if (x === 0 && w === this.bitmap.width) {\n      // shortcut\n      const start = (w * y + x) << 2;\n      const end = (start + h * w) << 2;\n\n      this.bitmap.data = this.bitmap.data.slice(start, end);\n    } else {\n      const bitmap = Buffer.allocUnsafe(w * h * 4);\n      let offset = 0;\n\n      this.scanQuiet(x, y, w, h, function(x, y, idx) {\n        const data = this.bitmap.data.readUInt32BE(idx, true);\n        bitmap.writeUInt32BE(data, offset, true);\n        offset += 4;\n      });\n\n      this.bitmap.data = bitmap;\n    }\n\n    this.bitmap.width = w;\n    this.bitmap.height = h;\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  });\n\n  return {\n    class: {\n      /**\n       * Autocrop same color borders from this image\n       * @param {number} tolerance (optional): a percent value of tolerance for pixels color difference (default: 0.0002%)\n       * @param {boolean} cropOnlyFrames (optional): flag to crop only real frames: all 4 sides of the image must have some border (default: true)\n       * @param {function(Error, Jimp)} cb (optional): a callback for when complete (default: no callback)\n       * @returns {Jimp} this for chaining of methods\n       */\n      autocrop(...args) {\n        const w = this.bitmap.width;\n        const h = this.bitmap.height;\n        const minPixelsPerSide = 1; // to avoid cropping completely the image, resulting in an invalid 0 sized image\n\n        let cb; // callback\n        let leaveBorder = 0; // Amount of pixels in border to leave\n        let tolerance = 0.0002; // percent of color difference tolerance (default value)\n        let cropOnlyFrames = true; // flag to force cropping only if the image has a real \"frame\"\n        // i.e. all 4 sides have some border (default value)\n        let cropSymmetric = false; // flag to force cropping top be symmetric.\n        // i.e. north and south / east and west are cropped by the same value\n\n        // parse arguments\n        for (let a = 0, len = args.length; a < len; a++) {\n          if (typeof args[a] === 'number') {\n            // tolerance value passed\n            tolerance = args[a];\n          }\n\n          if (typeof args[a] === 'boolean') {\n            // cropOnlyFrames value passed\n            cropOnlyFrames = args[a];\n          }\n\n          if (typeof args[a] === 'function') {\n            // callback value passed\n            cb = args[a];\n          }\n\n          if (typeof args[a] === 'object') {\n            // config object passed\n            const config = args[a];\n\n            if (typeof config.tolerance !== 'undefined') {\n              ({ tolerance } = config);\n            }\n\n            if (typeof config.cropOnlyFrames !== 'undefined') {\n              ({ cropOnlyFrames } = config);\n            }\n\n            if (typeof config.cropSymmetric !== 'undefined') {\n              ({ cropSymmetric } = config);\n            }\n\n            if (typeof config.leaveBorder !== 'undefined') {\n              ({ leaveBorder } = config);\n            }\n          }\n        }\n\n        /**\n         * All borders must be of the same color as the top left pixel, to be cropped.\n         * It should be possible to crop borders each with a different color,\n         * but since there are many ways for corners to intersect, it would\n         * introduce unnecessary complexity to the algorithm.\n         */\n\n        // scan each side for same color borders\n        let colorTarget = this.getPixelColor(0, 0); // top left pixel color is the target color\n        const rgba1 = this.constructor.intToRGBA(colorTarget);\n\n        // for north and east sides\n        let northPixelsToCrop = 0;\n        let eastPixelsToCrop = 0;\n        let southPixelsToCrop = 0;\n        let westPixelsToCrop = 0;\n\n        // north side (scan rows from north to south)\n        colorTarget = this.getPixelColor(0, 0);\n        north: for (let y = 0; y < h - minPixelsPerSide; y++) {\n          for (let x = 0; x < w; x++) {\n            const colorXY = this.getPixelColor(x, y);\n            const rgba2 = this.constructor.intToRGBA(colorXY);\n\n            if (this.constructor.colorDiff(rgba1, rgba2) > tolerance) {\n              // this pixel is too distant from the first one: abort this side scan\n              break north;\n            }\n          }\n\n          // this row contains all pixels with the same color: increment this side pixels to crop\n          northPixelsToCrop++;\n        }\n\n        // east side (scan columns from east to west)\n        colorTarget = this.getPixelColor(w, 0);\n        east: for (let x = 0; x < w - minPixelsPerSide; x++) {\n          for (let y = 0 + northPixelsToCrop; y < h; y++) {\n            const colorXY = this.getPixelColor(x, y);\n            const rgba2 = this.constructor.intToRGBA(colorXY);\n\n            if (this.constructor.colorDiff(rgba1, rgba2) > tolerance) {\n              // this pixel is too distant from the first one: abort this side scan\n              break east;\n            }\n          }\n\n          // this column contains all pixels with the same color: increment this side pixels to crop\n          eastPixelsToCrop++;\n        }\n\n        // south side (scan rows from south to north)\n        colorTarget = this.getPixelColor(0, h);\n        south: for (\n          let y = h - 1;\n          y >= northPixelsToCrop + minPixelsPerSide;\n          y--\n        ) {\n          for (let x = w - eastPixelsToCrop - 1; x >= 0; x--) {\n            const colorXY = this.getPixelColor(x, y);\n            const rgba2 = this.constructor.intToRGBA(colorXY);\n\n            if (this.constructor.colorDiff(rgba1, rgba2) > tolerance) {\n              // this pixel is too distant from the first one: abort this side scan\n              break south;\n            }\n          }\n\n          // this row contains all pixels with the same color: increment this side pixels to crop\n          southPixelsToCrop++;\n        }\n\n        // west side (scan columns from west to east)\n        colorTarget = this.getPixelColor(w, h);\n        west: for (\n          let x = w - 1;\n          x >= 0 + eastPixelsToCrop + minPixelsPerSide;\n          x--\n        ) {\n          for (let y = h - 1; y >= 0 + northPixelsToCrop; y--) {\n            const colorXY = this.getPixelColor(x, y);\n            const rgba2 = this.constructor.intToRGBA(colorXY);\n\n            if (this.constructor.colorDiff(rgba1, rgba2) > tolerance) {\n              // this pixel is too distant from the first one: abort this side scan\n              break west;\n            }\n          }\n\n          // this column contains all pixels with the same color: increment this side pixels to crop\n          westPixelsToCrop++;\n        }\n\n        // decide if a crop is needed\n        let doCrop = false;\n\n        // apply leaveBorder\n        westPixelsToCrop -= leaveBorder;\n        eastPixelsToCrop -= leaveBorder;\n        northPixelsToCrop -= leaveBorder;\n        southPixelsToCrop -= leaveBorder;\n\n        if (cropSymmetric) {\n          const horizontal = Math.min(eastPixelsToCrop, westPixelsToCrop);\n          const vertical = Math.min(northPixelsToCrop, southPixelsToCrop);\n          westPixelsToCrop = horizontal;\n          eastPixelsToCrop = horizontal;\n          northPixelsToCrop = vertical;\n          southPixelsToCrop = vertical;\n        }\n\n        // make sure that crops are >= 0\n        westPixelsToCrop = westPixelsToCrop >= 0 ? westPixelsToCrop : 0;\n        eastPixelsToCrop = eastPixelsToCrop >= 0 ? eastPixelsToCrop : 0;\n        northPixelsToCrop = northPixelsToCrop >= 0 ? northPixelsToCrop : 0;\n        southPixelsToCrop = southPixelsToCrop >= 0 ? southPixelsToCrop : 0;\n\n        // safety checks\n        const widthOfRemainingPixels =\n          w - (westPixelsToCrop + eastPixelsToCrop);\n        const heightOfRemainingPixels =\n          h - (southPixelsToCrop + northPixelsToCrop);\n\n        if (cropOnlyFrames) {\n          // crop image if all sides should be cropped\n          doCrop =\n            eastPixelsToCrop !== 0 &&\n            northPixelsToCrop !== 0 &&\n            westPixelsToCrop !== 0 &&\n            southPixelsToCrop !== 0;\n        } else {\n          // crop image if at least one side should be cropped\n          doCrop =\n            eastPixelsToCrop !== 0 ||\n            northPixelsToCrop !== 0 ||\n            westPixelsToCrop !== 0 ||\n            southPixelsToCrop !== 0;\n        }\n\n        if (doCrop) {\n          // do the real crop\n          this.crop(\n            eastPixelsToCrop,\n            northPixelsToCrop,\n            widthOfRemainingPixels,\n            heightOfRemainingPixels\n          );\n        }\n\n        if (isNodePattern(cb)) {\n          cb.call(this, null, this);\n        }\n\n        return this;\n      }\n    }\n  };\n}\n"], "file": "index.js"}