{"version": 3, "sources": ["../src/index.js"], "names": ["invert", "cb", "scanQuiet", "bitmap", "width", "height", "x", "y", "idx", "data", "call"], "mappings": ";;;;;;;AAAA;;AAEA;;;;;eAKe;AAAA,SAAO;AACpBA,IAAAA,MADoB,kBACbC,EADa,EACT;AACT,WAAKC,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,KAAKC,MAAL,CAAYC,KAAjC,EAAwC,KAAKD,MAAL,CAAYE,MAApD,EAA4D,UAC1DC,CAD0D,EAE1DC,CAF0D,EAG1DC,GAH0D,EAI1D;AACA,aAAKL,MAAL,CAAYM,IAAZ,CAAiBD,GAAjB,IAAwB,MAAM,KAAKL,MAAL,CAAYM,IAAZ,CAAiBD,GAAjB,CAA9B;AACA,aAAKL,MAAL,CAAYM,IAAZ,CAAiBD,GAAG,GAAG,CAAvB,IAA4B,MAAM,KAAKL,MAAL,CAAYM,IAAZ,CAAiBD,GAAG,GAAG,CAAvB,CAAlC;AACA,aAAKL,MAAL,CAAYM,IAAZ,CAAiBD,GAAG,GAAG,CAAvB,IAA4B,MAAM,KAAKL,MAAL,CAAYM,IAAZ,CAAiBD,GAAG,GAAG,CAAvB,CAAlC;AACD,OARD;;AAUA,UAAI,0BAAcP,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACS,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD;AAjBmB,GAAP;AAAA,C", "sourcesContent": ["import { isNodePattern } from '@jimp/utils';\n\n/**\n * Inverts the image\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {<PERSON><PERSON>} this for chaining of methods\n */\nexport default () => ({\n  invert(cb) {\n    this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function(\n      x,\n      y,\n      idx\n    ) {\n      this.bitmap.data[idx] = 255 - this.bitmap.data[idx];\n      this.bitmap.data[idx + 1] = 255 - this.bitmap.data[idx + 1];\n      this.bitmap.data[idx + 2] = 255 - this.bitmap.data[idx + 2];\n    });\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  }\n});\n"], "file": "index.js"}