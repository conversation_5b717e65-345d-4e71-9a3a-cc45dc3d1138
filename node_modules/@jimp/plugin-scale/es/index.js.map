{"version": 3, "sources": ["../src/index.js"], "names": ["scale", "f", "mode", "cb", "throwError", "call", "w", "bitmap", "width", "h", "height", "resize", "scaleToFit"], "mappings": ";;;;;;;AAAA;;eAEe;AAAA,SAAO;AACpB;;;;;;;AAOAA,IAAAA,KARoB,iBAQdC,CARc,EAQXC,IARW,EAQLC,EARK,EAQD;AACjB,UAAI,OAAOF,CAAP,KAAa,QAAjB,EAA2B;AACzB,eAAOG,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,oBAAtB,EAA4CF,EAA5C,CAAP;AACD;;AAED,UAAIF,CAAC,GAAG,CAAR,EAAW;AACT,eAAOG,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,6BAAtB,EAAqDF,EAArD,CAAP;AACD;;AAED,UAAI,OAAOD,IAAP,KAAgB,UAAhB,IAA8B,OAAOC,EAAP,KAAc,WAAhD,EAA6D;AAC3DA,QAAAA,EAAE,GAAGD,IAAL;AACAA,QAAAA,IAAI,GAAG,IAAP;AACD;;AAED,UAAMI,CAAC,GAAG,KAAKC,MAAL,CAAYC,KAAZ,GAAoBP,CAA9B;AACA,UAAMQ,CAAC,GAAG,KAAKF,MAAL,CAAYG,MAAZ,GAAqBT,CAA/B;AACA,WAAKU,MAAL,CAAYL,CAAZ,EAAeG,CAAf,EAAkBP,IAAlB;;AAEA,UAAI,0BAAcC,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACE,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD,KA/BmB;;AAiCpB;;;;;;;;AAQAO,IAAAA,UAzCoB,sBAyCTN,CAzCS,EAyCNG,CAzCM,EAyCHP,IAzCG,EAyCGC,EAzCH,EAyCO;AACzB,UAAI,OAAOG,CAAP,KAAa,QAAb,IAAyB,OAAOG,CAAP,KAAa,QAA1C,EAAoD;AAClD,eAAOL,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,yBAAtB,EAAiDF,EAAjD,CAAP;AACD;;AAED,UAAI,OAAOD,IAAP,KAAgB,UAAhB,IAA8B,OAAOC,EAAP,KAAc,WAAhD,EAA6D;AAC3DA,QAAAA,EAAE,GAAGD,IAAL;AACAA,QAAAA,IAAI,GAAG,IAAP;AACD;;AAED,UAAMD,CAAC,GACLK,CAAC,GAAGG,CAAJ,GAAQ,KAAKF,MAAL,CAAYC,KAAZ,GAAoB,KAAKD,MAAL,CAAYG,MAAxC,GACID,CAAC,GAAG,KAAKF,MAAL,CAAYG,MADpB,GAEIJ,CAAC,GAAG,KAAKC,MAAL,CAAYC,KAHtB;AAIA,WAAKR,KAAL,CAAWC,CAAX,EAAcC,IAAd;;AAEA,UAAI,0BAAcC,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACE,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD;AA9DmB,GAAP;AAAA,C", "sourcesContent": ["import { isNodePattern, throwError } from '@jimp/utils';\n\nexport default () => ({\n  /**\n   * Uniformly scales the image by a factor.\n   * @param {number} f the factor to scale the image by\n   * @param {string} mode (optional) a scaling method (e.g. Jimp.RESIZE_BEZIER)\n   * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n   * @returns {Jimp} this for chaining of methods\n   */\n  scale(f, mode, cb) {\n    if (typeof f !== 'number') {\n      return throwError.call(this, 'f must be a number', cb);\n    }\n\n    if (f < 0) {\n      return throwError.call(this, 'f must be a positive number', cb);\n    }\n\n    if (typeof mode === 'function' && typeof cb === 'undefined') {\n      cb = mode;\n      mode = null;\n    }\n\n    const w = this.bitmap.width * f;\n    const h = this.bitmap.height * f;\n    this.resize(w, h, mode);\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Scale the image to the largest size that fits inside the rectangle that has the given width and height.\n   * @param {number} w the width to resize the image to\n   * @param {number} h the height to resize the image to\n   * @param {string} mode (optional) a scaling method (e.g. Jimp.RESIZE_BEZIER)\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp} this for chaining of methods\n   */\n  scaleToFit(w, h, mode, cb) {\n    if (typeof w !== 'number' || typeof h !== 'number') {\n      return throwError.call(this, 'w and h must be numbers', cb);\n    }\n\n    if (typeof mode === 'function' && typeof cb === 'undefined') {\n      cb = mode;\n      mode = null;\n    }\n\n    const f =\n      w / h > this.bitmap.width / this.bitmap.height\n        ? h / this.bitmap.height\n        : w / this.bitmap.width;\n    this.scale(f, mode);\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  }\n});\n"], "file": "index.js"}