{"version": 3, "sources": ["../../src/utils/promisify.js"], "names": ["promisify", "fun", "ctx", "args", "Promise", "resolve", "reject", "push", "err", "data", "bind"], "mappings": ";;;;;;;AAAA,IAAMA,SAAS,GAAG,SAAZA,SAAY,CAACC,GAAD,EAAMC,GAAN;AAAA,oCAAcC,IAAd;AAAcA,IAAAA,IAAd;AAAA;;AAAA,SAChB,IAAIC,OAAJ,CAAY,UAACC,OAAD,EAAUC,MAAV,EAAqB;AAC/BH,IAAAA,IAAI,CAACI,IAAL,CAAU,UAACC,GAAD,EAAMC,IAAN,EAAe;AACvB,UAAID,GAAJ,EAAS;AACPF,QAAAA,MAAM,CAACE,GAAD,CAAN;AACD;;AAEDH,MAAAA,OAAO,CAACI,IAAD,CAAP;AACD,KAND;AAOAR,IAAAA,GAAG,CAACS,IAAJ,CAASR,GAAT,gBAAiBC,IAAjB;AACD,GATD,CADgB;AAAA,CAAlB;;eAYeH,S", "sourcesContent": ["const promisify = (fun, ctx, ...args) =>\n  new Promise((resolve, reject) => {\n    args.push((err, data) => {\n      if (err) {\n        reject(err);\n      }\n\n      resolve(data);\n    });\n    fun.bind(ctx)(...args);\n  });\n\nexport default promisify;\n"], "file": "promisify.js"}