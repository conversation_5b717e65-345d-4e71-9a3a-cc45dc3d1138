{"version": 3, "sources": ["../../src/utils/image-bitmap.js"], "names": ["getMIMEFromBuffer", "buffer", "path", "fileTypeFromBuffer", "mime", "MIME", "getType", "getExifOrientation", "img", "_exif", "tags", "Orientation", "getExifOrientationTransformation", "w", "getWidth", "h", "getHeight", "x", "y", "transformBitmap", "width", "height", "transformation", "_data", "bitmap", "data", "_width", "<PERSON><PERSON><PERSON>", "alloc", "length", "_x", "_y", "idx", "_idx", "pixel", "readUInt32BE", "writeUInt32BE", "exifRotate", "swapDimensions", "newWidth", "newHeight", "parseBitmap", "cb", "Error", "_originalMime", "toLowerCase", "getMIME", "constructor", "decoders", "throwError", "call", "error", "EXIFParser", "create", "parse", "compositeBitmapOverBackground", "<PERSON><PERSON>", "image", "_background", "composite", "<PERSON><PERSON><PERSON><PERSON>", "constants", "AUTO", "_rgba", "has<PERSON><PERSON><PERSON>", "from", "encoders", "getBufferAsync"], "mappings": ";;;;;;;;;;;;;;;AAAA;;AAEA;;AACA;;AAEA;;AACA;;AACA;;AAEA,SAASA,iBAAT,CAA2BC,MAA3B,EAAmCC,IAAnC,EAAyC;AACvC,MAAMC,kBAAkB,GAAG,0BAASF,MAAT,CAA3B;;AAEA,MAAIE,kBAAJ,EAAwB;AACtB;AACA,WAAOA,kBAAkB,CAACC,IAA1B;AACD;;AAED,MAAIF,IAAJ,EAAU;AACR;AACA;AACA,WAAOG,IAAI,CAACC,OAAL,CAAaJ,IAAb,CAAP;AACD;;AAED,SAAO,IAAP;AACD;AAED;;;;;;;;;AAOA,SAASK,kBAAT,CAA4BC,GAA5B,EAAiC;AAC/B,SAAQA,GAAG,CAACC,KAAJ,IAAaD,GAAG,CAACC,KAAJ,CAAUC,IAAvB,IAA+BF,GAAG,CAACC,KAAJ,CAAUC,IAAV,CAAeC,WAA/C,IAA+D,CAAtE;AACD;AAED;;;;;;;;;;;AASA,SAASC,gCAAT,CAA0CJ,GAA1C,EAA+C;AAC7C,MAAMK,CAAC,GAAGL,GAAG,CAACM,QAAJ,EAAV;AACA,MAAMC,CAAC,GAAGP,GAAG,CAACQ,SAAJ,EAAV;;AAEA,UAAQT,kBAAkB,CAACC,GAAD,CAA1B;AACE,SAAK,CAAL;AAAQ;AACN;AACA,aAAO,IAAP;;AAEF,SAAK,CAAL;AAAQ;AACN,aAAO,UAASS,CAAT,EAAYC,CAAZ,EAAe;AACpB,eAAO,CAACL,CAAC,GAAGI,CAAJ,GAAQ,CAAT,EAAYC,CAAZ,CAAP;AACD,OAFD;;AAIF,SAAK,CAAL;AAAQ;AACN,aAAO,UAASD,CAAT,EAAYC,CAAZ,EAAe;AACpB,eAAO,CAACL,CAAC,GAAGI,CAAJ,GAAQ,CAAT,EAAYF,CAAC,GAAGG,CAAJ,GAAQ,CAApB,CAAP;AACD,OAFD;;AAIF,SAAK,CAAL;AAAQ;AACN,aAAO,UAASD,CAAT,EAAYC,CAAZ,EAAe;AACpB,eAAO,CAACD,CAAD,EAAIF,CAAC,GAAGG,CAAJ,GAAQ,CAAZ,CAAP;AACD,OAFD;;AAIF,SAAK,CAAL;AAAQ;AACN,aAAO,UAASD,CAAT,EAAYC,CAAZ,EAAe;AACpB,eAAO,CAACA,CAAD,EAAID,CAAJ,CAAP;AACD,OAFD;;AAIF,SAAK,CAAL;AAAQ;AACN,aAAO,UAASA,CAAT,EAAYC,CAAZ,EAAe;AACpB,eAAO,CAACA,CAAD,EAAIH,CAAC,GAAGE,CAAJ,GAAQ,CAAZ,CAAP;AACD,OAFD;;AAIF,SAAK,CAAL;AAAQ;AACN,aAAO,UAASA,CAAT,EAAYC,CAAZ,EAAe;AACpB,eAAO,CAACL,CAAC,GAAGK,CAAJ,GAAQ,CAAT,EAAYH,CAAC,GAAGE,CAAJ,GAAQ,CAApB,CAAP;AACD,OAFD;;AAIF,SAAK,CAAL;AAAQ;AACN,aAAO,UAASA,CAAT,EAAYC,CAAZ,EAAe;AACpB,eAAO,CAACL,CAAC,GAAGK,CAAJ,GAAQ,CAAT,EAAYD,CAAZ,CAAP;AACD,OAFD;;AAIF;AACE,aAAO,IAAP;AAzCJ;AA2CD;AAED;;;;;;;;;;;;;;;;AAcA,SAASE,eAAT,CAAyBX,GAAzB,EAA8BY,KAA9B,EAAqCC,MAArC,EAA6CC,cAA7C,EAA6D;AAC3D;AACA;AACA,MAAMC,KAAK,GAAGf,GAAG,CAACgB,MAAJ,CAAWC,IAAzB;AACA,MAAMC,MAAM,GAAGlB,GAAG,CAACgB,MAAJ,CAAWJ,KAA1B;AAEA,MAAMK,IAAI,GAAGE,MAAM,CAACC,KAAP,CAAaL,KAAK,CAACM,MAAnB,CAAb;;AAEA,OAAK,IAAIZ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGG,KAApB,EAA2BH,CAAC,EAA5B,EAAgC;AAC9B,SAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGG,MAApB,EAA4BH,CAAC,EAA7B,EAAiC;AAAA,4BACdI,cAAc,CAACL,CAAD,EAAIC,CAAJ,CADA;AAAA;AAAA,UACxBY,EADwB;AAAA,UACpBC,EADoB;;AAG/B,UAAMC,GAAG,GAAIZ,KAAK,GAAGF,CAAR,GAAYD,CAAb,IAAmB,CAA/B;;AACA,UAAMgB,IAAI,GAAIP,MAAM,GAAGK,EAAT,GAAcD,EAAf,IAAsB,CAAnC;;AAEA,UAAMI,KAAK,GAAGX,KAAK,CAACY,YAAN,CAAmBF,IAAnB,CAAd;;AACAR,MAAAA,IAAI,CAACW,aAAL,CAAmBF,KAAnB,EAA0BF,GAA1B;AACD;AACF;;AAEDxB,EAAAA,GAAG,CAACgB,MAAJ,CAAWC,IAAX,GAAkBA,IAAlB;AACAjB,EAAAA,GAAG,CAACgB,MAAJ,CAAWJ,KAAX,GAAmBA,KAAnB;AACAZ,EAAAA,GAAG,CAACgB,MAAJ,CAAWH,MAAX,GAAoBA,MAApB;AACD;AAED;;;;;;AAIA,SAASgB,UAAT,CAAoB7B,GAApB,EAAyB;AACvB,MAAID,kBAAkB,CAACC,GAAD,CAAlB,GAA0B,CAA9B,EAAiC;AAEjC,MAAMc,cAAc,GAAGV,gCAAgC,CAACJ,GAAD,CAAvD;AACA,MAAM8B,cAAc,GAAG/B,kBAAkB,CAACC,GAAD,CAAlB,GAA0B,CAAjD;AAEA,MAAM+B,QAAQ,GAAGD,cAAc,GAAG9B,GAAG,CAACgB,MAAJ,CAAWH,MAAd,GAAuBb,GAAG,CAACgB,MAAJ,CAAWJ,KAAjE;AACA,MAAMoB,SAAS,GAAGF,cAAc,GAAG9B,GAAG,CAACgB,MAAJ,CAAWJ,KAAd,GAAsBZ,GAAG,CAACgB,MAAJ,CAAWH,MAAjE;AAEAF,EAAAA,eAAe,CAACX,GAAD,EAAM+B,QAAN,EAAgBC,SAAhB,EAA2BlB,cAA3B,CAAf;AACD,C,CAED;;;AACO,SAASmB,WAAT,CAAqBhB,IAArB,EAA2BvB,IAA3B,EAAiCwC,EAAjC,EAAqC;AAC1C,MAAMtC,IAAI,GAAGJ,iBAAiB,CAACyB,IAAD,EAAOvB,IAAP,CAA9B;;AAEA,MAAI,OAAOE,IAAP,KAAgB,QAApB,EAA8B;AAC5B,WAAOsC,EAAE,CAAC,IAAIC,KAAJ,CAAU,qCAAqCzC,IAArC,GAA4C,GAAtD,CAAD,CAAT;AACD;;AAED,OAAK0C,aAAL,GAAqBxC,IAAI,CAACyC,WAAL,EAArB;;AAEA,MAAI;AACF,QAAMzC,KAAI,GAAG,KAAK0C,OAAL,EAAb;;AAEA,QAAI,KAAKC,WAAL,CAAiBC,QAAjB,CAA0B5C,KAA1B,CAAJ,EAAqC;AACnC,WAAKoB,MAAL,GAAc,KAAKuB,WAAL,CAAiBC,QAAjB,CAA0B5C,KAA1B,EAAgCqB,IAAhC,CAAd;AACD,KAFD,MAEO;AACL,aAAOwB,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,4BAA4B9C,KAAlD,EAAwDsC,EAAxD,CAAP;AACD;AACF,GARD,CAQE,OAAOS,KAAP,EAAc;AACd,WAAOT,EAAE,CAACQ,IAAH,CAAQ,IAAR,EAAcC,KAAd,EAAqB,IAArB,CAAP;AACD;;AAED,MAAI;AACF,SAAK1C,KAAL,GAAa2C,uBAAWC,MAAX,CAAkB5B,IAAlB,EAAwB6B,KAAxB,EAAb;AACAjB,IAAAA,UAAU,CAAC,IAAD,CAAV,CAFE,CAEgB;AACnB,GAHD,CAGE,OAAOc,KAAP,EAAc;AACd;AACD;;AAEDT,EAAAA,EAAE,CAACQ,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AAEA,SAAO,IAAP;AACD;;AAED,SAASK,6BAAT,CAAuCC,IAAvC,EAA6CC,KAA7C,EAAoD;AAClD,SAAO,IAAID,IAAJ,CACLC,KAAK,CAACjC,MAAN,CAAaJ,KADR,EAELqC,KAAK,CAACjC,MAAN,CAAaH,MAFR,EAGLoC,KAAK,CAACC,WAHD,EAILC,SAJK,CAIKF,KAJL,EAIY,CAJZ,EAIe,CAJf,EAIkBjC,MAJzB;AAKD;AAED;;;;;;;;AAMO,SAASoC,SAAT,CAAmBxD,IAAnB,EAAyBsC,EAAzB,EAA6B;AAClC,MAAItC,IAAI,KAAKyD,SAAS,CAACC,IAAvB,EAA6B;AAC3B;AACA1D,IAAAA,IAAI,GAAG,KAAK0C,OAAL,EAAP;AACD;;AAED,MAAI,OAAO1C,IAAP,KAAgB,QAApB,EAA8B;AAC5B,WAAO6C,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,uBAAtB,EAA+CR,EAA/C,CAAP;AACD;;AAED,MAAI,OAAOA,EAAP,KAAc,UAAlB,EAA8B;AAC5B,WAAOO,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,uBAAtB,EAA+CR,EAA/C,CAAP;AACD;;AAEDtC,EAAAA,IAAI,GAAGA,IAAI,CAACyC,WAAL,EAAP;;AAEA,MAAI,KAAKkB,KAAL,IAAc,KAAKhB,WAAL,CAAiBiB,QAAjB,CAA0B5D,IAA1B,CAAlB,EAAmD;AACjD,SAAKoB,MAAL,CAAYC,IAAZ,GAAmBE,MAAM,CAACsC,IAAP,CAAY,KAAKzC,MAAL,CAAYC,IAAxB,CAAnB;AACD,GAFD,MAEO;AACL;AACA;AACA,SAAKD,MAAL,CAAYC,IAAZ,GAAmB8B,6BAA6B,CAC9C,KAAKR,WADyC,EAE9C,IAF8C,CAA7B,CAGjBtB,IAHF;AAID;;AAED,MAAI,KAAKsB,WAAL,CAAiBmB,QAAjB,CAA0B9D,IAA1B,CAAJ,EAAqC;AACnC,QAAMH,MAAM,GAAG,KAAK8C,WAAL,CAAiBmB,QAAjB,CAA0B9D,IAA1B,EAAgC,IAAhC,CAAf;AACAsC,IAAAA,EAAE,CAACQ,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoBjD,MAApB;AACD,GAHD,MAGO;AACLyC,IAAAA,EAAE,CAACQ,IAAH,CAAQ,IAAR,EAAc,4BAA4B9C,IAA1C;AACD;;AAED,SAAO,IAAP;AACD;;AAEM,SAAS+D,cAAT,CAAwB/D,IAAxB,EAA8B;AACnC,SAAO,2BAAUwD,SAAV,EAAqB,IAArB,EAA2BxD,IAA3B,CAAP;AACD", "sourcesContent": ["import fileType from 'file-type';\n\nimport <PERSON><PERSON><PERSON><PERSON>arser from 'exif-parser';\nimport { throwError } from '@jimp/utils';\n\nimport * as constants from '../constants';\nimport * as MIME from './mime';\nimport promisify from './promisify';\n\nfunction getMIMEFromBuffer(buffer, path) {\n  const fileTypeFromBuffer = fileType(buffer);\n\n  if (fileTypeFromBuffer) {\n    // If fileType returns something for buffer, then return the mime given\n    return fileTypeFromBuffer.mime;\n  }\n\n  if (path) {\n    // If a path is supplied, and fileType yields no results, then retry with MIME\n    // Path can be either a file path or a url\n    return MIME.getType(path);\n  }\n\n  return null;\n}\n\n/*\n * Obtains image orientation from EXIF metadata.\n *\n * @param img {Jimp} a Jimp image object\n * @returns {number} a number 1-8 representing EXIF orientation,\n *          in particular 1 if orientation tag is missing\n */\nfunction getExifOrientation(img) {\n  return (img._exif && img._exif.tags && img._exif.tags.Orientation) || 1;\n}\n\n/**\n * Returns a function which translates EXIF-rotated coordinates into\n * non-rotated ones.\n *\n * Transformation reference: http://sylvana.net/jpegcrop/exif_orientation.html.\n *\n * @param img {Jimp} a Jimp image object\n * @returns {function} transformation function for transformBitmap().\n */\nfunction getExifOrientationTransformation(img) {\n  const w = img.getWidth();\n  const h = img.getHeight();\n\n  switch (getExifOrientation(img)) {\n    case 1: // Horizontal (normal)\n      // does not need to be supported here\n      return null;\n\n    case 2: // Mirror horizontal\n      return function(x, y) {\n        return [w - x - 1, y];\n      };\n\n    case 3: // Rotate 180\n      return function(x, y) {\n        return [w - x - 1, h - y - 1];\n      };\n\n    case 4: // Mirror vertical\n      return function(x, y) {\n        return [x, h - y - 1];\n      };\n\n    case 5: // Mirror horizontal and rotate 270 CW\n      return function(x, y) {\n        return [y, x];\n      };\n\n    case 6: // Rotate 90 CW\n      return function(x, y) {\n        return [y, h - x - 1];\n      };\n\n    case 7: // Mirror horizontal and rotate 90 CW\n      return function(x, y) {\n        return [w - y - 1, h - x - 1];\n      };\n\n    case 8: // Rotate 270 CW\n      return function(x, y) {\n        return [w - y - 1, x];\n      };\n\n    default:\n      return null;\n  }\n}\n\n/*\n * Transforms bitmap in place (moves pixels around) according to given\n * transformation function.\n *\n * @param img {Jimp} a Jimp image object, which bitmap is supposed to\n *        be transformed\n * @param width {number} bitmap width after the transformation\n * @param height {number} bitmap height after the transformation\n * @param transformation {function} transformation function which defines pixel\n *        mapping between new and source bitmap. It takes a pair of coordinates\n *        in the target, and returns a respective pair of coordinates in\n *        the source bitmap, i.e. has following form:\n *        `function(new_x, new_y) { return [src_x, src_y] }`.\n */\nfunction transformBitmap(img, width, height, transformation) {\n  // Underscore-prefixed values are related to the source bitmap\n  // Their counterparts with no prefix are related to the target bitmap\n  const _data = img.bitmap.data;\n  const _width = img.bitmap.width;\n\n  const data = Buffer.alloc(_data.length);\n\n  for (let x = 0; x < width; x++) {\n    for (let y = 0; y < height; y++) {\n      const [_x, _y] = transformation(x, y);\n\n      const idx = (width * y + x) << 2;\n      const _idx = (_width * _y + _x) << 2;\n\n      const pixel = _data.readUInt32BE(_idx);\n      data.writeUInt32BE(pixel, idx);\n    }\n  }\n\n  img.bitmap.data = data;\n  img.bitmap.width = width;\n  img.bitmap.height = height;\n}\n\n/*\n * Automagically rotates an image based on its EXIF data (if present).\n * @param img {Jimp} a Jimp image object\n */\nfunction exifRotate(img) {\n  if (getExifOrientation(img) < 2) return;\n\n  const transformation = getExifOrientationTransformation(img);\n  const swapDimensions = getExifOrientation(img) > 4;\n\n  const newWidth = swapDimensions ? img.bitmap.height : img.bitmap.width;\n  const newHeight = swapDimensions ? img.bitmap.width : img.bitmap.height;\n\n  transformBitmap(img, newWidth, newHeight, transformation);\n}\n\n// parses a bitmap from the constructor to the JIMP bitmap property\nexport function parseBitmap(data, path, cb) {\n  const mime = getMIMEFromBuffer(data, path);\n\n  if (typeof mime !== 'string') {\n    return cb(new Error('Could not find MIME for Buffer <' + path + '>'));\n  }\n\n  this._originalMime = mime.toLowerCase();\n\n  try {\n    const mime = this.getMIME();\n\n    if (this.constructor.decoders[mime]) {\n      this.bitmap = this.constructor.decoders[mime](data);\n    } else {\n      return throwError.call(this, 'Unsupported MIME type: ' + mime, cb);\n    }\n  } catch (error) {\n    return cb.call(this, error, this);\n  }\n\n  try {\n    this._exif = EXIFParser.create(data).parse();\n    exifRotate(this); // EXIF data\n  } catch (error) {\n    /* meh */\n  }\n\n  cb.call(this, null, this);\n\n  return this;\n}\n\nfunction compositeBitmapOverBackground(Jimp, image) {\n  return new Jimp(\n    image.bitmap.width,\n    image.bitmap.height,\n    image._background\n  ).composite(image, 0, 0).bitmap;\n}\n\n/**\n * Converts the image to a buffer\n * @param {string} mime the mime type of the image buffer to be created\n * @param {function(Error, Jimp)} cb a Node-style function to call with the buffer as the second argument\n * @returns {Jimp} this for chaining of methods\n */\nexport function getBuffer(mime, cb) {\n  if (mime === constants.AUTO) {\n    // allow auto MIME detection\n    mime = this.getMIME();\n  }\n\n  if (typeof mime !== 'string') {\n    return throwError.call(this, 'mime must be a string', cb);\n  }\n\n  if (typeof cb !== 'function') {\n    return throwError.call(this, 'cb must be a function', cb);\n  }\n\n  mime = mime.toLowerCase();\n\n  if (this._rgba && this.constructor.hasAlpha[mime]) {\n    this.bitmap.data = Buffer.from(this.bitmap.data);\n  } else {\n    // when format doesn't support alpha\n    // composite onto a new image so that the background shows through alpha channels\n    this.bitmap.data = compositeBitmapOverBackground(\n      this.constructor,\n      this\n    ).data;\n  }\n\n  if (this.constructor.encoders[mime]) {\n    const buffer = this.constructor.encoders[mime](this);\n    cb.call(this, null, buffer);\n  } else {\n    cb.call(this, 'Unsupported MIME type: ' + mime);\n  }\n\n  return this;\n}\n\nexport function getBufferAsync(mime) {\n  return promisify(getBuffer, this, mime);\n}\n"], "file": "image-bitmap.js"}