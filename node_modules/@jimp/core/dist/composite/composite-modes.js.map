{"version": 3, "sources": ["../../src/composite/composite-modes.js"], "names": ["srcOver", "src", "dst", "ops", "a", "r", "g", "b", "dstOver", "multiply", "sra", "sga", "sba", "dra", "dga", "dba", "screen", "overlay", "darken", "Math", "min", "lighten", "max", "hardLight", "difference", "exclusion"], "mappings": ";;;;;;;;;;;;;;;;AAAO,SAASA,OAAT,CAAiBC,GAAjB,EAAsBC,GAAtB,EAAoC;AAAA,MAATC,GAAS,uEAAH,CAAG;AACzCF,EAAAA,GAAG,CAACG,CAAJ,IAASD,GAAT;AAEA,MAAMC,CAAC,GAAGF,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAAZ,GAAgBF,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAAtC;AAEA,MAAMC,CAAC,GAAG,CAACJ,GAAG,CAACI,CAAJ,GAAQJ,GAAG,CAACG,CAAZ,GAAgBF,GAAG,CAACG,CAAJ,GAAQH,GAAG,CAACE,CAAZ,IAAiB,IAAIH,GAAG,CAACG,CAAzB,CAAjB,IAAgDA,CAA1D;AACA,MAAME,CAAC,GAAG,CAACL,GAAG,CAACK,CAAJ,GAAQL,GAAG,CAACG,CAAZ,GAAgBF,GAAG,CAACI,CAAJ,GAAQJ,GAAG,CAACE,CAAZ,IAAiB,IAAIH,GAAG,CAACG,CAAzB,CAAjB,IAAgDA,CAA1D;AACA,MAAMG,CAAC,GAAG,CAACN,GAAG,CAACM,CAAJ,GAAQN,GAAG,CAACG,CAAZ,GAAgBF,GAAG,CAACK,CAAJ,GAAQL,GAAG,CAACE,CAAZ,IAAiB,IAAIH,GAAG,CAACG,CAAzB,CAAjB,IAAgDA,CAA1D;AAEA,SAAO;AAAEC,IAAAA,CAAC,EAADA,CAAF;AAAKC,IAAAA,CAAC,EAADA,CAAL;AAAQC,IAAAA,CAAC,EAADA,CAAR;AAAWH,IAAAA,CAAC,EAADA;AAAX,GAAP;AACD;;AAEM,SAASI,OAAT,CAAiBP,GAAjB,EAAsBC,GAAtB,EAAoC;AAAA,MAATC,GAAS,uEAAH,CAAG;AACzCF,EAAAA,GAAG,CAACG,CAAJ,IAASD,GAAT;AAEA,MAAMC,CAAC,GAAGF,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAAZ,GAAgBF,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAAtC;AAEA,MAAMC,CAAC,GAAG,CAACH,GAAG,CAACG,CAAJ,GAAQH,GAAG,CAACE,CAAZ,GAAgBH,GAAG,CAACI,CAAJ,GAAQJ,GAAG,CAACG,CAAZ,IAAiB,IAAIF,GAAG,CAACE,CAAzB,CAAjB,IAAgDA,CAA1D;AACA,MAAME,CAAC,GAAG,CAACJ,GAAG,CAACI,CAAJ,GAAQJ,GAAG,CAACE,CAAZ,GAAgBH,GAAG,CAACK,CAAJ,GAAQL,GAAG,CAACG,CAAZ,IAAiB,IAAIF,GAAG,CAACE,CAAzB,CAAjB,IAAgDA,CAA1D;AACA,MAAMG,CAAC,GAAG,CAACL,GAAG,CAACK,CAAJ,GAAQL,GAAG,CAACE,CAAZ,GAAgBH,GAAG,CAACM,CAAJ,GAAQN,GAAG,CAACG,CAAZ,IAAiB,IAAIF,GAAG,CAACE,CAAzB,CAAjB,IAAgDA,CAA1D;AAEA,SAAO;AAAEC,IAAAA,CAAC,EAADA,CAAF;AAAKC,IAAAA,CAAC,EAADA,CAAL;AAAQC,IAAAA,CAAC,EAADA,CAAR;AAAWH,IAAAA,CAAC,EAADA;AAAX,GAAP;AACD;;AAEM,SAASK,QAAT,CAAkBR,GAAlB,EAAuBC,GAAvB,EAAqC;AAAA,MAATC,GAAS,uEAAH,CAAG;AAC1CF,EAAAA,GAAG,CAACG,CAAJ,IAASD,GAAT;AAEA,MAAMC,CAAC,GAAGF,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAAZ,GAAgBF,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAAtC;AAEA,MAAMM,GAAG,GAAGT,GAAG,CAACI,CAAJ,GAAQJ,GAAG,CAACG,CAAxB;AACA,MAAMO,GAAG,GAAGV,GAAG,CAACK,CAAJ,GAAQL,GAAG,CAACG,CAAxB;AACA,MAAMQ,GAAG,GAAGX,GAAG,CAACM,CAAJ,GAAQN,GAAG,CAACG,CAAxB;AAEA,MAAMS,GAAG,GAAGX,GAAG,CAACG,CAAJ,GAAQH,GAAG,CAACE,CAAxB;AACA,MAAMU,GAAG,GAAGZ,GAAG,CAACI,CAAJ,GAAQJ,GAAG,CAACE,CAAxB;AACA,MAAMW,GAAG,GAAGb,GAAG,CAACK,CAAJ,GAAQL,GAAG,CAACE,CAAxB;AAEA,MAAMC,CAAC,GAAG,CAACK,GAAG,GAAGG,GAAN,GAAYH,GAAG,IAAI,IAAIR,GAAG,CAACE,CAAZ,CAAf,GAAgCS,GAAG,IAAI,IAAIZ,GAAG,CAACG,CAAZ,CAApC,IAAsDA,CAAhE;AACA,MAAME,CAAC,GAAG,CAACK,GAAG,GAAGG,GAAN,GAAYH,GAAG,IAAI,IAAIT,GAAG,CAACE,CAAZ,CAAf,GAAgCU,GAAG,IAAI,IAAIb,GAAG,CAACG,CAAZ,CAApC,IAAsDA,CAAhE;AACA,MAAMG,CAAC,GAAG,CAACK,GAAG,GAAGG,GAAN,GAAYH,GAAG,IAAI,IAAIV,GAAG,CAACE,CAAZ,CAAf,GAAgCW,GAAG,IAAI,IAAId,GAAG,CAACG,CAAZ,CAApC,IAAsDA,CAAhE;AAEA,SAAO;AAAEC,IAAAA,CAAC,EAADA,CAAF;AAAKC,IAAAA,CAAC,EAADA,CAAL;AAAQC,IAAAA,CAAC,EAADA,CAAR;AAAWH,IAAAA,CAAC,EAADA;AAAX,GAAP;AACD;;AAEM,SAASY,MAAT,CAAgBf,GAAhB,EAAqBC,GAArB,EAAmC;AAAA,MAATC,GAAS,uEAAH,CAAG;AACxCF,EAAAA,GAAG,CAACG,CAAJ,IAASD,GAAT;AAEA,MAAMC,CAAC,GAAGF,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAAZ,GAAgBF,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAAtC;AAEA,MAAMM,GAAG,GAAGT,GAAG,CAACI,CAAJ,GAAQJ,GAAG,CAACG,CAAxB;AACA,MAAMO,GAAG,GAAGV,GAAG,CAACK,CAAJ,GAAQL,GAAG,CAACG,CAAxB;AACA,MAAMQ,GAAG,GAAGX,GAAG,CAACM,CAAJ,GAAQN,GAAG,CAACG,CAAxB;AAEA,MAAMS,GAAG,GAAGX,GAAG,CAACG,CAAJ,GAAQH,GAAG,CAACE,CAAxB;AACA,MAAMU,GAAG,GAAGZ,GAAG,CAACI,CAAJ,GAAQJ,GAAG,CAACE,CAAxB;AACA,MAAMW,GAAG,GAAGb,GAAG,CAACK,CAAJ,GAAQL,GAAG,CAACE,CAAxB;AAEA,MAAMC,CAAC,GACL,CAACK,GAAG,GAAGR,GAAG,CAACE,CAAV,GACCS,GAAG,GAAGZ,GAAG,CAACG,CADX,GAECM,GAAG,GAAGG,GAFP,GAGCH,GAAG,IAAI,IAAIR,GAAG,CAACE,CAAZ,CAHJ,GAICS,GAAG,IAAI,IAAIZ,GAAG,CAACG,CAAZ,CAJL,IAKAA,CANF;AAOA,MAAME,CAAC,GACL,CAACK,GAAG,GAAGT,GAAG,CAACE,CAAV,GACCU,GAAG,GAAGb,GAAG,CAACG,CADX,GAECO,GAAG,GAAGG,GAFP,GAGCH,GAAG,IAAI,IAAIT,GAAG,CAACE,CAAZ,CAHJ,GAICU,GAAG,IAAI,IAAIb,GAAG,CAACG,CAAZ,CAJL,IAKAA,CANF;AAOA,MAAMG,CAAC,GACL,CAACK,GAAG,GAAGV,GAAG,CAACE,CAAV,GACCW,GAAG,GAAGd,GAAG,CAACG,CADX,GAECQ,GAAG,GAAGG,GAFP,GAGCH,GAAG,IAAI,IAAIV,GAAG,CAACE,CAAZ,CAHJ,GAICW,GAAG,IAAI,IAAId,GAAG,CAACG,CAAZ,CAJL,IAKAA,CANF;AAQA,SAAO;AAAEC,IAAAA,CAAC,EAADA,CAAF;AAAKC,IAAAA,CAAC,EAADA,CAAL;AAAQC,IAAAA,CAAC,EAADA,CAAR;AAAWH,IAAAA,CAAC,EAADA;AAAX,GAAP;AACD;;AAEM,SAASa,OAAT,CAAiBhB,GAAjB,EAAsBC,GAAtB,EAAoC;AAAA,MAATC,GAAS,uEAAH,CAAG;AACzCF,EAAAA,GAAG,CAACG,CAAJ,IAASD,GAAT;AAEA,MAAMC,CAAC,GAAGF,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAAZ,GAAgBF,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAAtC;AAEA,MAAMM,GAAG,GAAGT,GAAG,CAACI,CAAJ,GAAQJ,GAAG,CAACG,CAAxB;AACA,MAAMO,GAAG,GAAGV,GAAG,CAACK,CAAJ,GAAQL,GAAG,CAACG,CAAxB;AACA,MAAMQ,GAAG,GAAGX,GAAG,CAACM,CAAJ,GAAQN,GAAG,CAACG,CAAxB;AAEA,MAAMS,GAAG,GAAGX,GAAG,CAACG,CAAJ,GAAQH,GAAG,CAACE,CAAxB;AACA,MAAMU,GAAG,GAAGZ,GAAG,CAACI,CAAJ,GAAQJ,GAAG,CAACE,CAAxB;AACA,MAAMW,GAAG,GAAGb,GAAG,CAACK,CAAJ,GAAQL,GAAG,CAACE,CAAxB;AAEA,MAAMC,CAAC,GACL,CAAC,IAAIQ,GAAJ,IAAWX,GAAG,CAACE,CAAf,GACG,IAAIM,GAAJ,GAAUG,GAAV,GAAgBH,GAAG,IAAI,IAAIR,GAAG,CAACE,CAAZ,CAAnB,GAAoCS,GAAG,IAAI,IAAIZ,GAAG,CAACG,CAAZ,CAD1C,GAEGM,GAAG,IAAI,IAAIR,GAAG,CAACE,CAAZ,CAAH,GAAoBS,GAAG,IAAI,IAAIZ,GAAG,CAACG,CAAZ,CAAvB,GAAwC,IAAIS,GAAJ,GAAUH,GAAlD,GAAwDR,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAFxE,IAGAA,CAJF;AAMA,MAAME,CAAC,GACL,CAAC,IAAIQ,GAAJ,IAAWZ,GAAG,CAACE,CAAf,GACG,IAAIO,GAAJ,GAAUG,GAAV,GAAgBH,GAAG,IAAI,IAAIT,GAAG,CAACE,CAAZ,CAAnB,GAAoCU,GAAG,IAAI,IAAIb,GAAG,CAACG,CAAZ,CAD1C,GAEGO,GAAG,IAAI,IAAIT,GAAG,CAACE,CAAZ,CAAH,GAAoBU,GAAG,IAAI,IAAIb,GAAG,CAACG,CAAZ,CAAvB,GAAwC,IAAIU,GAAJ,GAAUH,GAAlD,GAAwDT,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAFxE,IAGAA,CAJF;AAMA,MAAMG,CAAC,GACL,CAAC,IAAIQ,GAAJ,IAAWb,GAAG,CAACE,CAAf,GACG,IAAIQ,GAAJ,GAAUG,GAAV,GAAgBH,GAAG,IAAI,IAAIV,GAAG,CAACE,CAAZ,CAAnB,GAAoCW,GAAG,IAAI,IAAId,GAAG,CAACG,CAAZ,CAD1C,GAEGQ,GAAG,IAAI,IAAIV,GAAG,CAACE,CAAZ,CAAH,GAAoBW,GAAG,IAAI,IAAId,GAAG,CAACG,CAAZ,CAAvB,GAAwC,IAAIW,GAAJ,GAAUH,GAAlD,GAAwDV,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAFxE,IAGAA,CAJF;AAMA,SAAO;AAAEC,IAAAA,CAAC,EAADA,CAAF;AAAKC,IAAAA,CAAC,EAADA,CAAL;AAAQC,IAAAA,CAAC,EAADA,CAAR;AAAWH,IAAAA,CAAC,EAADA;AAAX,GAAP;AACD;;AAEM,SAASc,MAAT,CAAgBjB,GAAhB,EAAqBC,GAArB,EAAmC;AAAA,MAATC,GAAS,uEAAH,CAAG;AACxCF,EAAAA,GAAG,CAACG,CAAJ,IAASD,GAAT;AAEA,MAAMC,CAAC,GAAGF,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAAZ,GAAgBF,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAAtC;AAEA,MAAMM,GAAG,GAAGT,GAAG,CAACI,CAAJ,GAAQJ,GAAG,CAACG,CAAxB;AACA,MAAMO,GAAG,GAAGV,GAAG,CAACK,CAAJ,GAAQL,GAAG,CAACG,CAAxB;AACA,MAAMQ,GAAG,GAAGX,GAAG,CAACM,CAAJ,GAAQN,GAAG,CAACG,CAAxB;AAEA,MAAMS,GAAG,GAAGX,GAAG,CAACG,CAAJ,GAAQH,GAAG,CAACE,CAAxB;AACA,MAAMU,GAAG,GAAGZ,GAAG,CAACI,CAAJ,GAAQJ,GAAG,CAACE,CAAxB;AACA,MAAMW,GAAG,GAAGb,GAAG,CAACK,CAAJ,GAAQL,GAAG,CAACE,CAAxB;AAEA,MAAMC,CAAC,GACL,CAACc,IAAI,CAACC,GAAL,CAASV,GAAG,GAAGR,GAAG,CAACE,CAAnB,EAAsBS,GAAG,GAAGZ,GAAG,CAACG,CAAhC,IACCM,GAAG,IAAI,IAAIR,GAAG,CAACE,CAAZ,CADJ,GAECS,GAAG,IAAI,IAAIZ,GAAG,CAACG,CAAZ,CAFL,IAGAA,CAJF;AAKA,MAAME,CAAC,GACL,CAACa,IAAI,CAACC,GAAL,CAAST,GAAG,GAAGT,GAAG,CAACE,CAAnB,EAAsBU,GAAG,GAAGb,GAAG,CAACG,CAAhC,IACCO,GAAG,IAAI,IAAIT,GAAG,CAACE,CAAZ,CADJ,GAECU,GAAG,IAAI,IAAIb,GAAG,CAACG,CAAZ,CAFL,IAGAA,CAJF;AAKA,MAAMG,CAAC,GACL,CAACY,IAAI,CAACC,GAAL,CAASR,GAAG,GAAGV,GAAG,CAACE,CAAnB,EAAsBW,GAAG,GAAGd,GAAG,CAACG,CAAhC,IACCQ,GAAG,IAAI,IAAIV,GAAG,CAACE,CAAZ,CADJ,GAECW,GAAG,IAAI,IAAId,GAAG,CAACG,CAAZ,CAFL,IAGAA,CAJF;AAMA,SAAO;AAAEC,IAAAA,CAAC,EAADA,CAAF;AAAKC,IAAAA,CAAC,EAADA,CAAL;AAAQC,IAAAA,CAAC,EAADA,CAAR;AAAWH,IAAAA,CAAC,EAADA;AAAX,GAAP;AACD;;AAEM,SAASiB,OAAT,CAAiBpB,GAAjB,EAAsBC,GAAtB,EAAoC;AAAA,MAATC,GAAS,uEAAH,CAAG;AACzCF,EAAAA,GAAG,CAACG,CAAJ,IAASD,GAAT;AAEA,MAAMC,CAAC,GAAGF,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAAZ,GAAgBF,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAAtC;AAEA,MAAMM,GAAG,GAAGT,GAAG,CAACI,CAAJ,GAAQJ,GAAG,CAACG,CAAxB;AACA,MAAMO,GAAG,GAAGV,GAAG,CAACK,CAAJ,GAAQL,GAAG,CAACG,CAAxB;AACA,MAAMQ,GAAG,GAAGX,GAAG,CAACM,CAAJ,GAAQN,GAAG,CAACG,CAAxB;AAEA,MAAMS,GAAG,GAAGX,GAAG,CAACG,CAAJ,GAAQH,GAAG,CAACE,CAAxB;AACA,MAAMU,GAAG,GAAGZ,GAAG,CAACI,CAAJ,GAAQJ,GAAG,CAACE,CAAxB;AACA,MAAMW,GAAG,GAAGb,GAAG,CAACK,CAAJ,GAAQL,GAAG,CAACE,CAAxB;AAEA,MAAMC,CAAC,GACL,CAACc,IAAI,CAACG,GAAL,CAASZ,GAAG,GAAGR,GAAG,CAACE,CAAnB,EAAsBS,GAAG,GAAGZ,GAAG,CAACG,CAAhC,IACCM,GAAG,IAAI,IAAIR,GAAG,CAACE,CAAZ,CADJ,GAECS,GAAG,IAAI,IAAIZ,GAAG,CAACG,CAAZ,CAFL,IAGAA,CAJF;AAKA,MAAME,CAAC,GACL,CAACa,IAAI,CAACG,GAAL,CAASX,GAAG,GAAGT,GAAG,CAACE,CAAnB,EAAsBU,GAAG,GAAGb,GAAG,CAACG,CAAhC,IACCO,GAAG,IAAI,IAAIT,GAAG,CAACE,CAAZ,CADJ,GAECU,GAAG,IAAI,IAAIb,GAAG,CAACG,CAAZ,CAFL,IAGAA,CAJF;AAKA,MAAMG,CAAC,GACL,CAACY,IAAI,CAACG,GAAL,CAASV,GAAG,GAAGV,GAAG,CAACE,CAAnB,EAAsBW,GAAG,GAAGd,GAAG,CAACG,CAAhC,IACCQ,GAAG,IAAI,IAAIV,GAAG,CAACE,CAAZ,CADJ,GAECW,GAAG,IAAI,IAAId,GAAG,CAACG,CAAZ,CAFL,IAGAA,CAJF;AAMA,SAAO;AAAEC,IAAAA,CAAC,EAADA,CAAF;AAAKC,IAAAA,CAAC,EAADA,CAAL;AAAQC,IAAAA,CAAC,EAADA,CAAR;AAAWH,IAAAA,CAAC,EAADA;AAAX,GAAP;AACD;;AAEM,SAASmB,SAAT,CAAmBtB,GAAnB,EAAwBC,GAAxB,EAAsC;AAAA,MAATC,GAAS,uEAAH,CAAG;AAC3CF,EAAAA,GAAG,CAACG,CAAJ,IAASD,GAAT;AAEA,MAAMC,CAAC,GAAGF,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAAZ,GAAgBF,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAAtC;AAEA,MAAMM,GAAG,GAAGT,GAAG,CAACI,CAAJ,GAAQJ,GAAG,CAACG,CAAxB;AACA,MAAMO,GAAG,GAAGV,GAAG,CAACK,CAAJ,GAAQL,GAAG,CAACG,CAAxB;AACA,MAAMQ,GAAG,GAAGX,GAAG,CAACM,CAAJ,GAAQN,GAAG,CAACG,CAAxB;AAEA,MAAMS,GAAG,GAAGX,GAAG,CAACG,CAAJ,GAAQH,GAAG,CAACE,CAAxB;AACA,MAAMU,GAAG,GAAGZ,GAAG,CAACI,CAAJ,GAAQJ,GAAG,CAACE,CAAxB;AACA,MAAMW,GAAG,GAAGb,GAAG,CAACK,CAAJ,GAAQL,GAAG,CAACE,CAAxB;AAEA,MAAMC,CAAC,GACL,CAAC,IAAIK,GAAJ,IAAWT,GAAG,CAACG,CAAf,GACG,IAAIM,GAAJ,GAAUG,GAAV,GAAgBH,GAAG,IAAI,IAAIR,GAAG,CAACE,CAAZ,CAAnB,GAAoCS,GAAG,IAAI,IAAIZ,GAAG,CAACG,CAAZ,CAD1C,GAEGM,GAAG,IAAI,IAAIR,GAAG,CAACE,CAAZ,CAAH,GAAoBS,GAAG,IAAI,IAAIZ,GAAG,CAACG,CAAZ,CAAvB,GAAwC,IAAIS,GAAJ,GAAUH,GAAlD,GAAwDR,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAFxE,IAGAA,CAJF;AAMA,MAAME,CAAC,GACL,CAAC,IAAIK,GAAJ,IAAWV,GAAG,CAACG,CAAf,GACG,IAAIO,GAAJ,GAAUG,GAAV,GAAgBH,GAAG,IAAI,IAAIT,GAAG,CAACE,CAAZ,CAAnB,GAAoCU,GAAG,IAAI,IAAIb,GAAG,CAACG,CAAZ,CAD1C,GAEGO,GAAG,IAAI,IAAIT,GAAG,CAACE,CAAZ,CAAH,GAAoBU,GAAG,IAAI,IAAIb,GAAG,CAACG,CAAZ,CAAvB,GAAwC,IAAIU,GAAJ,GAAUH,GAAlD,GAAwDT,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAFxE,IAGAA,CAJF;AAMA,MAAMG,CAAC,GACL,CAAC,IAAIK,GAAJ,IAAWX,GAAG,CAACG,CAAf,GACG,IAAIQ,GAAJ,GAAUG,GAAV,GAAgBH,GAAG,IAAI,IAAIV,GAAG,CAACE,CAAZ,CAAnB,GAAoCW,GAAG,IAAI,IAAId,GAAG,CAACG,CAAZ,CAD1C,GAEGQ,GAAG,IAAI,IAAIV,GAAG,CAACE,CAAZ,CAAH,GAAoBW,GAAG,IAAI,IAAId,GAAG,CAACG,CAAZ,CAAvB,GAAwC,IAAIW,GAAJ,GAAUH,GAAlD,GAAwDV,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAFxE,IAGAA,CAJF;AAMA,SAAO;AAAEC,IAAAA,CAAC,EAADA,CAAF;AAAKC,IAAAA,CAAC,EAADA,CAAL;AAAQC,IAAAA,CAAC,EAADA,CAAR;AAAWH,IAAAA,CAAC,EAADA;AAAX,GAAP;AACD;;AAEM,SAASoB,UAAT,CAAoBvB,GAApB,EAAyBC,GAAzB,EAAuC;AAAA,MAATC,GAAS,uEAAH,CAAG;AAC5CF,EAAAA,GAAG,CAACG,CAAJ,IAASD,GAAT;AAEA,MAAMC,CAAC,GAAGF,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAAZ,GAAgBF,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAAtC;AAEA,MAAMM,GAAG,GAAGT,GAAG,CAACI,CAAJ,GAAQJ,GAAG,CAACG,CAAxB;AACA,MAAMO,GAAG,GAAGV,GAAG,CAACK,CAAJ,GAAQL,GAAG,CAACG,CAAxB;AACA,MAAMQ,GAAG,GAAGX,GAAG,CAACM,CAAJ,GAAQN,GAAG,CAACG,CAAxB;AAEA,MAAMS,GAAG,GAAGX,GAAG,CAACG,CAAJ,GAAQH,GAAG,CAACE,CAAxB;AACA,MAAMU,GAAG,GAAGZ,GAAG,CAACI,CAAJ,GAAQJ,GAAG,CAACE,CAAxB;AACA,MAAMW,GAAG,GAAGb,GAAG,CAACK,CAAJ,GAAQL,GAAG,CAACE,CAAxB;AAEA,MAAMC,CAAC,GAAG,CAACK,GAAG,GAAGG,GAAN,GAAY,IAAIM,IAAI,CAACC,GAAL,CAASV,GAAG,GAAGR,GAAG,CAACE,CAAnB,EAAsBS,GAAG,GAAGZ,GAAG,CAACG,CAAhC,CAAjB,IAAuDA,CAAjE;AACA,MAAME,CAAC,GAAG,CAACK,GAAG,GAAGG,GAAN,GAAY,IAAIK,IAAI,CAACC,GAAL,CAAST,GAAG,GAAGT,GAAG,CAACE,CAAnB,EAAsBU,GAAG,GAAGb,GAAG,CAACG,CAAhC,CAAjB,IAAuDA,CAAjE;AACA,MAAMG,CAAC,GAAG,CAACK,GAAG,GAAGG,GAAN,GAAY,IAAII,IAAI,CAACC,GAAL,CAASR,GAAG,GAAGV,GAAG,CAACE,CAAnB,EAAsBW,GAAG,GAAGd,GAAG,CAACG,CAAhC,CAAjB,IAAuDA,CAAjE;AAEA,SAAO;AAAEC,IAAAA,CAAC,EAADA,CAAF;AAAKC,IAAAA,CAAC,EAADA,CAAL;AAAQC,IAAAA,CAAC,EAADA,CAAR;AAAWH,IAAAA,CAAC,EAADA;AAAX,GAAP;AACD;;AAEM,SAASqB,SAAT,CAAmBxB,GAAnB,EAAwBC,GAAxB,EAAsC;AAAA,MAATC,GAAS,uEAAH,CAAG;AAC3CF,EAAAA,GAAG,CAACG,CAAJ,IAASD,GAAT;AAEA,MAAMC,CAAC,GAAGF,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAAZ,GAAgBF,GAAG,CAACE,CAAJ,GAAQH,GAAG,CAACG,CAAtC;AAEA,MAAMM,GAAG,GAAGT,GAAG,CAACI,CAAJ,GAAQJ,GAAG,CAACG,CAAxB;AACA,MAAMO,GAAG,GAAGV,GAAG,CAACK,CAAJ,GAAQL,GAAG,CAACG,CAAxB;AACA,MAAMQ,GAAG,GAAGX,GAAG,CAACM,CAAJ,GAAQN,GAAG,CAACG,CAAxB;AAEA,MAAMS,GAAG,GAAGX,GAAG,CAACG,CAAJ,GAAQH,GAAG,CAACE,CAAxB;AACA,MAAMU,GAAG,GAAGZ,GAAG,CAACI,CAAJ,GAAQJ,GAAG,CAACE,CAAxB;AACA,MAAMW,GAAG,GAAGb,GAAG,CAACK,CAAJ,GAAQL,GAAG,CAACE,CAAxB;AAEA,MAAMC,CAAC,GACL,CAACK,GAAG,GAAGR,GAAG,CAACE,CAAV,GACCS,GAAG,GAAGZ,GAAG,CAACG,CADX,GAEC,IAAIM,GAAJ,GAAUG,GAFX,GAGCH,GAAG,IAAI,IAAIR,GAAG,CAACE,CAAZ,CAHJ,GAICS,GAAG,IAAI,IAAIZ,GAAG,CAACG,CAAZ,CAJL,IAKAA,CANF;AAOA,MAAME,CAAC,GACL,CAACK,GAAG,GAAGT,GAAG,CAACE,CAAV,GACCU,GAAG,GAAGb,GAAG,CAACG,CADX,GAEC,IAAIO,GAAJ,GAAUG,GAFX,GAGCH,GAAG,IAAI,IAAIT,GAAG,CAACE,CAAZ,CAHJ,GAICU,GAAG,IAAI,IAAIb,GAAG,CAACG,CAAZ,CAJL,IAKAA,CANF;AAOA,MAAMG,CAAC,GACL,CAACK,GAAG,GAAGV,GAAG,CAACE,CAAV,GACCW,GAAG,GAAGd,GAAG,CAACG,CADX,GAEC,IAAIQ,GAAJ,GAAUG,GAFX,GAGCH,GAAG,IAAI,IAAIV,GAAG,CAACE,CAAZ,CAHJ,GAICW,GAAG,IAAI,IAAId,GAAG,CAACG,CAAZ,CAJL,IAKAA,CANF;AAQA,SAAO;AAAEC,IAAAA,CAAC,EAADA,CAAF;AAAKC,IAAAA,CAAC,EAADA,CAAL;AAAQC,IAAAA,CAAC,EAADA,CAAR;AAAWH,IAAAA,CAAC,EAADA;AAAX,GAAP;AACD", "sourcesContent": ["export function srcOver(src, dst, ops = 1) {\n  src.a *= ops;\n\n  const a = dst.a + src.a - dst.a * src.a;\n\n  const r = (src.r * src.a + dst.r * dst.a * (1 - src.a)) / a;\n  const g = (src.g * src.a + dst.g * dst.a * (1 - src.a)) / a;\n  const b = (src.b * src.a + dst.b * dst.a * (1 - src.a)) / a;\n\n  return { r, g, b, a };\n}\n\nexport function dstOver(src, dst, ops = 1) {\n  src.a *= ops;\n\n  const a = dst.a + src.a - dst.a * src.a;\n\n  const r = (dst.r * dst.a + src.r * src.a * (1 - dst.a)) / a;\n  const g = (dst.g * dst.a + src.g * src.a * (1 - dst.a)) / a;\n  const b = (dst.b * dst.a + src.b * src.a * (1 - dst.a)) / a;\n\n  return { r, g, b, a };\n}\n\nexport function multiply(src, dst, ops = 1) {\n  src.a *= ops;\n\n  const a = dst.a + src.a - dst.a * src.a;\n\n  const sra = src.r * src.a;\n  const sga = src.g * src.a;\n  const sba = src.b * src.a;\n\n  const dra = dst.r * dst.a;\n  const dga = dst.g * dst.a;\n  const dba = dst.b * dst.a;\n\n  const r = (sra * dra + sra * (1 - dst.a) + dra * (1 - src.a)) / a;\n  const g = (sga * dga + sga * (1 - dst.a) + dga * (1 - src.a)) / a;\n  const b = (sba * dba + sba * (1 - dst.a) + dba * (1 - src.a)) / a;\n\n  return { r, g, b, a };\n}\n\nexport function screen(src, dst, ops = 1) {\n  src.a *= ops;\n\n  const a = dst.a + src.a - dst.a * src.a;\n\n  const sra = src.r * src.a;\n  const sga = src.g * src.a;\n  const sba = src.b * src.a;\n\n  const dra = dst.r * dst.a;\n  const dga = dst.g * dst.a;\n  const dba = dst.b * dst.a;\n\n  const r =\n    (sra * dst.a +\n      dra * src.a -\n      sra * dra +\n      sra * (1 - dst.a) +\n      dra * (1 - src.a)) /\n    a;\n  const g =\n    (sga * dst.a +\n      dga * src.a -\n      sga * dga +\n      sga * (1 - dst.a) +\n      dga * (1 - src.a)) /\n    a;\n  const b =\n    (sba * dst.a +\n      dba * src.a -\n      sba * dba +\n      sba * (1 - dst.a) +\n      dba * (1 - src.a)) /\n    a;\n\n  return { r, g, b, a };\n}\n\nexport function overlay(src, dst, ops = 1) {\n  src.a *= ops;\n\n  const a = dst.a + src.a - dst.a * src.a;\n\n  const sra = src.r * src.a;\n  const sga = src.g * src.a;\n  const sba = src.b * src.a;\n\n  const dra = dst.r * dst.a;\n  const dga = dst.g * dst.a;\n  const dba = dst.b * dst.a;\n\n  const r =\n    (2 * dra <= dst.a\n      ? 2 * sra * dra + sra * (1 - dst.a) + dra * (1 - src.a)\n      : sra * (1 + dst.a) + dra * (1 + src.a) - 2 * dra * sra - dst.a * src.a) /\n    a;\n\n  const g =\n    (2 * dga <= dst.a\n      ? 2 * sga * dga + sga * (1 - dst.a) + dga * (1 - src.a)\n      : sga * (1 + dst.a) + dga * (1 + src.a) - 2 * dga * sga - dst.a * src.a) /\n    a;\n\n  const b =\n    (2 * dba <= dst.a\n      ? 2 * sba * dba + sba * (1 - dst.a) + dba * (1 - src.a)\n      : sba * (1 + dst.a) + dba * (1 + src.a) - 2 * dba * sba - dst.a * src.a) /\n    a;\n\n  return { r, g, b, a };\n}\n\nexport function darken(src, dst, ops = 1) {\n  src.a *= ops;\n\n  const a = dst.a + src.a - dst.a * src.a;\n\n  const sra = src.r * src.a;\n  const sga = src.g * src.a;\n  const sba = src.b * src.a;\n\n  const dra = dst.r * dst.a;\n  const dga = dst.g * dst.a;\n  const dba = dst.b * dst.a;\n\n  const r =\n    (Math.min(sra * dst.a, dra * src.a) +\n      sra * (1 - dst.a) +\n      dra * (1 - src.a)) /\n    a;\n  const g =\n    (Math.min(sga * dst.a, dga * src.a) +\n      sga * (1 - dst.a) +\n      dga * (1 - src.a)) /\n    a;\n  const b =\n    (Math.min(sba * dst.a, dba * src.a) +\n      sba * (1 - dst.a) +\n      dba * (1 - src.a)) /\n    a;\n\n  return { r, g, b, a };\n}\n\nexport function lighten(src, dst, ops = 1) {\n  src.a *= ops;\n\n  const a = dst.a + src.a - dst.a * src.a;\n\n  const sra = src.r * src.a;\n  const sga = src.g * src.a;\n  const sba = src.b * src.a;\n\n  const dra = dst.r * dst.a;\n  const dga = dst.g * dst.a;\n  const dba = dst.b * dst.a;\n\n  const r =\n    (Math.max(sra * dst.a, dra * src.a) +\n      sra * (1 - dst.a) +\n      dra * (1 - src.a)) /\n    a;\n  const g =\n    (Math.max(sga * dst.a, dga * src.a) +\n      sga * (1 - dst.a) +\n      dga * (1 - src.a)) /\n    a;\n  const b =\n    (Math.max(sba * dst.a, dba * src.a) +\n      sba * (1 - dst.a) +\n      dba * (1 - src.a)) /\n    a;\n\n  return { r, g, b, a };\n}\n\nexport function hardLight(src, dst, ops = 1) {\n  src.a *= ops;\n\n  const a = dst.a + src.a - dst.a * src.a;\n\n  const sra = src.r * src.a;\n  const sga = src.g * src.a;\n  const sba = src.b * src.a;\n\n  const dra = dst.r * dst.a;\n  const dga = dst.g * dst.a;\n  const dba = dst.b * dst.a;\n\n  const r =\n    (2 * sra <= src.a\n      ? 2 * sra * dra + sra * (1 - dst.a) + dra * (1 - src.a)\n      : sra * (1 + dst.a) + dra * (1 + src.a) - 2 * dra * sra - dst.a * src.a) /\n    a;\n\n  const g =\n    (2 * sga <= src.a\n      ? 2 * sga * dga + sga * (1 - dst.a) + dga * (1 - src.a)\n      : sga * (1 + dst.a) + dga * (1 + src.a) - 2 * dga * sga - dst.a * src.a) /\n    a;\n\n  const b =\n    (2 * sba <= src.a\n      ? 2 * sba * dba + sba * (1 - dst.a) + dba * (1 - src.a)\n      : sba * (1 + dst.a) + dba * (1 + src.a) - 2 * dba * sba - dst.a * src.a) /\n    a;\n\n  return { r, g, b, a };\n}\n\nexport function difference(src, dst, ops = 1) {\n  src.a *= ops;\n\n  const a = dst.a + src.a - dst.a * src.a;\n\n  const sra = src.r * src.a;\n  const sga = src.g * src.a;\n  const sba = src.b * src.a;\n\n  const dra = dst.r * dst.a;\n  const dga = dst.g * dst.a;\n  const dba = dst.b * dst.a;\n\n  const r = (sra + dra - 2 * Math.min(sra * dst.a, dra * src.a)) / a;\n  const g = (sga + dga - 2 * Math.min(sga * dst.a, dga * src.a)) / a;\n  const b = (sba + dba - 2 * Math.min(sba * dst.a, dba * src.a)) / a;\n\n  return { r, g, b, a };\n}\n\nexport function exclusion(src, dst, ops = 1) {\n  src.a *= ops;\n\n  const a = dst.a + src.a - dst.a * src.a;\n\n  const sra = src.r * src.a;\n  const sga = src.g * src.a;\n  const sba = src.b * src.a;\n\n  const dra = dst.r * dst.a;\n  const dga = dst.g * dst.a;\n  const dba = dst.b * dst.a;\n\n  const r =\n    (sra * dst.a +\n      dra * src.a -\n      2 * sra * dra +\n      sra * (1 - dst.a) +\n      dra * (1 - src.a)) /\n    a;\n  const g =\n    (sga * dst.a +\n      dga * src.a -\n      2 * sga * dga +\n      sga * (1 - dst.a) +\n      dga * (1 - src.a)) /\n    a;\n  const b =\n    (sba * dst.a +\n      dba * src.a -\n      2 * sba * dba +\n      sba * (1 - dst.a) +\n      dba * (1 - src.a)) /\n    a;\n\n  return { r, g, b, a };\n}\n"], "file": "composite-modes.js"}