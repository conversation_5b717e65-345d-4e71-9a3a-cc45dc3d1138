{"version": 3, "sources": ["../src/index.js"], "names": ["circle", "options", "cb", "radius", "bitmap", "width", "height", "center", "x", "y", "scanQuiet", "idx", "curR", "Math", "sqrt", "pow", "data", "call"], "mappings": ";;;;;;;AAAA;;AAEA;;;;;;eAMe;AAAA,SAAO;AACpBA,IAAAA,MADoB,oBACK;AAAA,UAAlBC,OAAkB,uEAAR,EAAQ;AAAA,UAAJC,EAAI;;AACvB,UAAI,OAAOD,OAAP,KAAmB,UAAvB,EAAmC;AACjCC,QAAAA,EAAE,GAAGD,OAAL;AACAA,QAAAA,OAAO,GAAG,EAAV;AACD;;AAED,UAAME,MAAM,GACVF,OAAO,CAACE,MAAR,IACA,CAAC,KAAKC,MAAL,CAAYC,KAAZ,GAAoB,KAAKD,MAAL,CAAYE,MAAhC,GACG,KAAKF,MAAL,CAAYE,MADf,GAEG,KAAKF,MAAL,CAAYC,KAFhB,IAEyB,CAJ3B;AAMA,UAAME,MAAM,GAAG;AACbC,QAAAA,CAAC,EAAE,OAAOP,OAAO,CAACO,CAAf,KAAqB,QAArB,GAAgCP,OAAO,CAACO,CAAxC,GAA4C,KAAKJ,MAAL,CAAYC,KAAZ,GAAoB,CADtD;AAEbI,QAAAA,CAAC,EAAE,OAAOR,OAAO,CAACQ,CAAf,KAAqB,QAArB,GAAgCR,OAAO,CAACQ,CAAxC,GAA4C,KAAKL,MAAL,CAAYE,MAAZ,GAAqB;AAFvD,OAAf;AAKA,WAAKI,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,KAAKN,MAAL,CAAYC,KAAjC,EAAwC,KAAKD,MAAL,CAAYE,MAApD,EAA4D,UAC1DE,CAD0D,EAE1DC,CAF0D,EAG1DE,GAH0D,EAI1D;AACA,YAAMC,IAAI,GAAGC,IAAI,CAACC,IAAL,CACXD,IAAI,CAACE,GAAL,CAASP,CAAC,GAAGD,MAAM,CAACC,CAApB,EAAuB,CAAvB,IAA4BK,IAAI,CAACE,GAAL,CAASN,CAAC,GAAGF,MAAM,CAACE,CAApB,EAAuB,CAAvB,CADjB,CAAb;;AAIA,YAAIN,MAAM,GAAGS,IAAT,IAAiB,GAArB,EAA0B;AACxB,eAAKR,MAAL,CAAYY,IAAZ,CAAiBL,GAAG,GAAG,CAAvB,IAA4B,CAA5B;AACD,SAFD,MAEO,IAAIR,MAAM,GAAGS,IAAT,GAAgB,GAApB,EAAyB;AAC9B,eAAKR,MAAL,CAAYY,IAAZ,CAAiBL,GAAG,GAAG,CAAvB,IAA4B,OAAOR,MAAM,GAAGS,IAAhB,CAA5B;AACD;AACF,OAdD;;AAgBA,UAAI,0BAAcV,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACe,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD;AAvCmB,GAAP;AAAA,C", "sourcesContent": ["import { isNodePattern } from '@jimp/utils';\n\n/**\n * Creates a circle out of an image.\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} options (optional) radius, x, y\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {<PERSON><PERSON>} this for chaining of methods\n */\nexport default () => ({\n  circle(options = {}, cb) {\n    if (typeof options === 'function') {\n      cb = options;\n      options = {};\n    }\n\n    const radius =\n      options.radius ||\n      (this.bitmap.width > this.bitmap.height\n        ? this.bitmap.height\n        : this.bitmap.width) / 2;\n\n    const center = {\n      x: typeof options.x === 'number' ? options.x : this.bitmap.width / 2,\n      y: typeof options.y === 'number' ? options.y : this.bitmap.height / 2\n    };\n\n    this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function(\n      x,\n      y,\n      idx\n    ) {\n      const curR = Math.sqrt(\n        Math.pow(x - center.x, 2) + Math.pow(y - center.y, 2)\n      );\n\n      if (radius - curR <= 0.0) {\n        this.bitmap.data[idx + 3] = 0;\n      } else if (radius - curR < 1.0) {\n        this.bitmap.data[idx + 3] = 255 * (radius - curR);\n      }\n    });\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  }\n});\n"], "file": "index.js"}