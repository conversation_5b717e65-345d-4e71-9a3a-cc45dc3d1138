{"version": 3, "sources": ["../src/index.js"], "names": ["cover", "w", "h", "alignBits", "mode", "cb", "throwError", "call", "constructor", "HORIZONTAL_ALIGN_CENTER", "VERTICAL_ALIGN_MIDDLE", "hbits", "vbits", "alignH", "alignV", "f", "bitmap", "width", "height", "scale", "crop"], "mappings": ";;;;;;;AAAA;;AAEA;;;;;;;;;eASe;AAAA,SAAO;AACpBA,IAAAA,KADoB,iBACdC,CADc,EACXC,CADW,EACRC,SADQ,EACGC,IADH,EACSC,EADT,EACa;AAC/B,UAAI,OAAOJ,CAAP,KAAa,QAAb,IAAyB,OAAOC,CAAP,KAAa,QAA1C,EAAoD;AAClD,eAAOI,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,yBAAtB,EAAiDF,EAAjD,CAAP;AACD;;AAED,UACEF,SAAS,IACT,OAAOA,SAAP,KAAqB,UADrB,IAEA,OAAOE,EAAP,KAAc,WAHhB,EAIE;AACAA,QAAAA,EAAE,GAAGF,SAAL;AACAA,QAAAA,SAAS,GAAG,IAAZ;AACAC,QAAAA,IAAI,GAAG,IAAP;AACD,OARD,MAQO,IAAI,OAAOA,IAAP,KAAgB,UAAhB,IAA8B,OAAOC,EAAP,KAAc,WAAhD,EAA6D;AAClEA,QAAAA,EAAE,GAAGD,IAAL;AACAA,QAAAA,IAAI,GAAG,IAAP;AACD;;AAEDD,MAAAA,SAAS,GACPA,SAAS,IACT,KAAKK,WAAL,CAAiBC,uBAAjB,GACE,KAAKD,WAAL,CAAiBE,qBAHrB;AAIA,UAAMC,KAAK,GAAGR,SAAS,GAAI,CAAC,KAAK,CAAN,IAAW,CAAtC;AACA,UAAMS,KAAK,GAAGT,SAAS,IAAI,CAA3B,CAvB+B,CAyB/B;;AACA,UACE,EACGQ,KAAK,KAAK,CAAV,IAAe,EAAEA,KAAK,GAAIA,KAAK,GAAG,CAAnB,CAAhB,IACCC,KAAK,KAAK,CAAV,IAAe,EAAEA,KAAK,GAAIA,KAAK,GAAG,CAAnB,CAFlB,CADF,EAME,OAAON,kBAAWC,IAAX,CACL,IADK,EAEL,2CAFK,EAGLF,EAHK,CAAP;AAMF,UAAMQ,MAAM,GAAGF,KAAK,IAAI,CAAxB,CAtC+B,CAsCJ;;AAC3B,UAAMG,MAAM,GAAGF,KAAK,IAAI,CAAxB,CAvC+B,CAuCJ;;AAE3B,UAAMG,CAAC,GACLd,CAAC,GAAGC,CAAJ,GAAQ,KAAKc,MAAL,CAAYC,KAAZ,GAAoB,KAAKD,MAAL,CAAYE,MAAxC,GACIjB,CAAC,GAAG,KAAKe,MAAL,CAAYC,KADpB,GAEIf,CAAC,GAAG,KAAKc,MAAL,CAAYE,MAHtB;AAIA,WAAKC,KAAL,CAAWJ,CAAX,EAAcX,IAAd;AACA,WAAKgB,IAAL,CACG,CAAC,KAAKJ,MAAL,CAAYC,KAAZ,GAAoBhB,CAArB,IAA0B,CAA3B,GAAgCY,MADlC,EAEG,CAAC,KAAKG,MAAL,CAAYE,MAAZ,GAAqBhB,CAAtB,IAA2B,CAA5B,GAAiCY,MAFnC,EAGEb,CAHF,EAIEC,CAJF;;AAOA,UAAI,0BAAcG,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACE,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD;AA3DmB,GAAP;AAAA,C", "sourcesContent": ["import { isNodePattern, throwError } from '@jimp/utils';\n\n/**\n * Scale the image so the given width and height keeping the aspect ratio. Some parts of the image may be clipped.\n * @param {number} w the width to resize the image to\n * @param {number} h the height to resize the image to\n * @param {number} alignBits (optional) A bitmask for horizontal and vertical alignment\n * @param {string} mode (optional) a scaling method (e.g. Jimp.RESIZE_BEZIER)\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {Jim<PERSON>} this for chaining of methods\n */\nexport default () => ({\n  cover(w, h, alignBits, mode, cb) {\n    if (typeof w !== 'number' || typeof h !== 'number') {\n      return throwError.call(this, 'w and h must be numbers', cb);\n    }\n\n    if (\n      alignBits &&\n      typeof alignBits === 'function' &&\n      typeof cb === 'undefined'\n    ) {\n      cb = alignBits;\n      alignBits = null;\n      mode = null;\n    } else if (typeof mode === 'function' && typeof cb === 'undefined') {\n      cb = mode;\n      mode = null;\n    }\n\n    alignBits =\n      alignBits ||\n      this.constructor.HORIZONTAL_ALIGN_CENTER |\n        this.constructor.VERTICAL_ALIGN_MIDDLE;\n    const hbits = alignBits & ((1 << 3) - 1);\n    const vbits = alignBits >> 3;\n\n    // check if more flags than one is in the bit sets\n    if (\n      !(\n        (hbits !== 0 && !(hbits & (hbits - 1))) ||\n        (vbits !== 0 && !(vbits & (vbits - 1)))\n      )\n    )\n      return throwError.call(\n        this,\n        'only use one flag per alignment direction',\n        cb\n      );\n\n    const alignH = hbits >> 1; // 0, 1, 2\n    const alignV = vbits >> 1; // 0, 1, 2\n\n    const f =\n      w / h > this.bitmap.width / this.bitmap.height\n        ? w / this.bitmap.width\n        : h / this.bitmap.height;\n    this.scale(f, mode);\n    this.crop(\n      ((this.bitmap.width - w) / 2) * alignH,\n      ((this.bitmap.height - h) / 2) * alignV,\n      w,\n      h\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  }\n});\n"], "file": "index.js"}