{"version": 3, "sources": ["../../src/modules/resize2.js"], "names": ["module", "exports", "nearestNeighbor", "src", "dst", "wSrc", "width", "hSrc", "height", "wDst", "hDst", "bufSrc", "data", "buf<PERSON>t", "i", "j", "posDst", "iSrc", "Math", "floor", "jSrc", "posSrc", "bilinearInterpolation", "interpolate", "k", "kMin", "vMin", "kMax", "vMax", "round", "assign", "pos", "offset", "x", "xMin", "xMax", "y", "yMin", "yMax", "posMin", "posMax", "min", "ceil", "_interpolate2D", "options", "wM", "max", "wDst2", "hM", "hDst2", "buf1", "<PERSON><PERSON><PERSON>", "alloc", "xPos", "t", "srcPos", "buf1Pos", "kPos", "x0", "x1", "x2", "x3", "buf2", "yPos", "buf2Pos", "y0", "y1", "y2", "y3", "m", "r", "g", "b", "a", "realColors", "xyPos", "pixelAlpha", "bicubicInterpolation", "interpolateCubic", "a0", "a1", "a2", "a3", "hermiteInterpolation", "interpolateHermite", "c0", "c1", "c2", "c3", "bezierInterpolation", "interpolate<PERSON><PERSON><PERSON>", "cp1", "cp2", "nt"], "mappings": ";;AAAA;;;;;;;;;;;;;;;;;;;;;AAsBAA,MAAM,CAACC,OAAP,GAAiB;AACfC,EAAAA,eADe,2BACCC,GADD,EACMC,GADN,EACW;AACxB,QAAMC,IAAI,GAAGF,GAAG,CAACG,KAAjB;AACA,QAAMC,IAAI,GAAGJ,GAAG,CAACK,MAAjB;AAEA,QAAMC,IAAI,GAAGL,GAAG,CAACE,KAAjB;AACA,QAAMI,IAAI,GAAGN,GAAG,CAACI,MAAjB;AAEA,QAAMG,MAAM,GAAGR,GAAG,CAACS,IAAnB;AACA,QAAMC,MAAM,GAAGT,GAAG,CAACQ,IAAnB;;AAEA,SAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,IAApB,EAA0BI,CAAC,EAA3B,EAA+B;AAC7B,WAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGN,IAApB,EAA0BM,CAAC,EAA3B,EAA+B;AAC7B,YAAIC,MAAM,GAAG,CAACF,CAAC,GAAGL,IAAJ,GAAWM,CAAZ,IAAiB,CAA9B;AAEA,YAAME,IAAI,GAAGC,IAAI,CAACC,KAAL,CAAYL,CAAC,GAAGP,IAAL,GAAaG,IAAxB,CAAb;AACA,YAAMU,IAAI,GAAGF,IAAI,CAACC,KAAL,CAAYJ,CAAC,GAAGV,IAAL,GAAaI,IAAxB,CAAb;AACA,YAAIY,MAAM,GAAG,CAACJ,IAAI,GAAGZ,IAAP,GAAce,IAAf,IAAuB,CAApC;AAEAP,QAAAA,MAAM,CAACG,MAAM,EAAP,CAAN,GAAmBL,MAAM,CAACU,MAAM,EAAP,CAAzB;AACAR,QAAAA,MAAM,CAACG,MAAM,EAAP,CAAN,GAAmBL,MAAM,CAACU,MAAM,EAAP,CAAzB;AACAR,QAAAA,MAAM,CAACG,MAAM,EAAP,CAAN,GAAmBL,MAAM,CAACU,MAAM,EAAP,CAAzB;AACAR,QAAAA,MAAM,CAACG,MAAM,EAAP,CAAN,GAAmBL,MAAM,CAACU,MAAM,EAAP,CAAzB;AACD;AACF;AACF,GAzBc;AA2BfC,EAAAA,qBA3Be,iCA2BOnB,GA3BP,EA2BYC,GA3BZ,EA2BiB;AAC9B,QAAMC,IAAI,GAAGF,GAAG,CAACG,KAAjB;AACA,QAAMC,IAAI,GAAGJ,GAAG,CAACK,MAAjB;AAEA,QAAMC,IAAI,GAAGL,GAAG,CAACE,KAAjB;AACA,QAAMI,IAAI,GAAGN,GAAG,CAACI,MAAjB;AAEA,QAAMG,MAAM,GAAGR,GAAG,CAACS,IAAnB;AACA,QAAMC,MAAM,GAAGT,GAAG,CAACQ,IAAnB;;AAEA,QAAMW,WAAW,GAAG,SAAdA,WAAc,CAASC,CAAT,EAAYC,IAAZ,EAAkBC,IAAlB,EAAwBC,IAAxB,EAA8BC,IAA9B,EAAoC;AACtD;AACA,UAAIH,IAAI,KAAKE,IAAb,EAAmB;AACjB,eAAOD,IAAP;AACD;;AAED,aAAOR,IAAI,CAACW,KAAL,CAAW,CAACL,CAAC,GAAGC,IAAL,IAAaG,IAAb,GAAoB,CAACD,IAAI,GAAGH,CAAR,IAAaE,IAA5C,CAAP;AACD,KAPD;;AASA,QAAMI,MAAM,GAAG,SAATA,MAAS,CAASC,GAAT,EAAcC,MAAd,EAAsBC,CAAtB,EAAyBC,IAAzB,EAA+BC,IAA/B,EAAqCC,CAArC,EAAwCC,IAAxC,EAA8CC,IAA9C,EAAoD;AACjE,UAAIC,MAAM,GAAG,CAACF,IAAI,GAAGhC,IAAP,GAAc6B,IAAf,IAAuB,CAAvB,GAA2BF,MAAxC;AACA,UAAIQ,MAAM,GAAG,CAACH,IAAI,GAAGhC,IAAP,GAAc8B,IAAf,IAAuB,CAAvB,GAA2BH,MAAxC;AACA,UAAMN,IAAI,GAAGH,WAAW,CAACU,CAAD,EAAIC,IAAJ,EAAUvB,MAAM,CAAC4B,MAAD,CAAhB,EAA0BJ,IAA1B,EAAgCxB,MAAM,CAAC6B,MAAD,CAAtC,CAAxB,CAHiE,CAKjE;;AACA,UAAIF,IAAI,KAAKD,IAAb,EAAmB;AACjBxB,QAAAA,MAAM,CAACkB,GAAG,GAAGC,MAAP,CAAN,GAAuBN,IAAvB;AACD,OAFD,MAEO;AACLa,QAAAA,MAAM,GAAG,CAACD,IAAI,GAAGjC,IAAP,GAAc6B,IAAf,IAAuB,CAAvB,GAA2BF,MAApC;AACAQ,QAAAA,MAAM,GAAG,CAACF,IAAI,GAAGjC,IAAP,GAAc8B,IAAf,IAAuB,CAAvB,GAA2BH,MAApC;AACA,YAAMJ,IAAI,GAAGL,WAAW,CAACU,CAAD,EAAIC,IAAJ,EAAUvB,MAAM,CAAC4B,MAAD,CAAhB,EAA0BJ,IAA1B,EAAgCxB,MAAM,CAAC6B,MAAD,CAAtC,CAAxB;AAEA3B,QAAAA,MAAM,CAACkB,GAAG,GAAGC,MAAP,CAAN,GAAuBT,WAAW,CAACa,CAAD,EAAIC,IAAJ,EAAUX,IAAV,EAAgBY,IAAhB,EAAsBV,IAAtB,CAAlC;AACD;AACF,KAfD;;AAiBA,SAAK,IAAId,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,IAApB,EAA0BI,CAAC,EAA3B,EAA+B;AAC7B,WAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGN,IAApB,EAA0BM,CAAC,EAA3B,EAA+B;AAC7B,YAAMC,MAAM,GAAG,CAACF,CAAC,GAAGL,IAAJ,GAAWM,CAAZ,IAAiB,CAAhC,CAD6B,CAE7B;;AACA,YAAMkB,CAAC,GAAIlB,CAAC,GAAGV,IAAL,GAAaI,IAAvB;AACA,YAAMyB,IAAI,GAAGhB,IAAI,CAACC,KAAL,CAAWc,CAAX,CAAb;AACA,YAAME,IAAI,GAAGjB,IAAI,CAACuB,GAAL,CAASvB,IAAI,CAACwB,IAAL,CAAUT,CAAV,CAAT,EAAuB5B,IAAI,GAAG,CAA9B,CAAb;AAEA,YAAM+B,CAAC,GAAItB,CAAC,GAAGP,IAAL,GAAaG,IAAvB;AACA,YAAM2B,IAAI,GAAGnB,IAAI,CAACC,KAAL,CAAWiB,CAAX,CAAb;AACA,YAAME,IAAI,GAAGpB,IAAI,CAACuB,GAAL,CAASvB,IAAI,CAACwB,IAAL,CAAUN,CAAV,CAAT,EAAuB7B,IAAI,GAAG,CAA9B,CAAb;AAEAuB,QAAAA,MAAM,CAACd,MAAD,EAAS,CAAT,EAAYiB,CAAZ,EAAeC,IAAf,EAAqBC,IAArB,EAA2BC,CAA3B,EAA8BC,IAA9B,EAAoCC,IAApC,CAAN;AACAR,QAAAA,MAAM,CAACd,MAAD,EAAS,CAAT,EAAYiB,CAAZ,EAAeC,IAAf,EAAqBC,IAArB,EAA2BC,CAA3B,EAA8BC,IAA9B,EAAoCC,IAApC,CAAN;AACAR,QAAAA,MAAM,CAACd,MAAD,EAAS,CAAT,EAAYiB,CAAZ,EAAeC,IAAf,EAAqBC,IAArB,EAA2BC,CAA3B,EAA8BC,IAA9B,EAAoCC,IAApC,CAAN;AACAR,QAAAA,MAAM,CAACd,MAAD,EAAS,CAAT,EAAYiB,CAAZ,EAAeC,IAAf,EAAqBC,IAArB,EAA2BC,CAA3B,EAA8BC,IAA9B,EAAoCC,IAApC,CAAN;AACD;AACF;AACF,GAjFc;AAmFfK,EAAAA,cAnFe,0BAmFAxC,GAnFA,EAmFKC,GAnFL,EAmFUwC,OAnFV,EAmFmBrB,WAnFnB,EAmFgC;AAC7C,QAAMZ,MAAM,GAAGR,GAAG,CAACS,IAAnB;AACA,QAAMC,MAAM,GAAGT,GAAG,CAACQ,IAAnB;AAEA,QAAMP,IAAI,GAAGF,GAAG,CAACG,KAAjB;AACA,QAAMC,IAAI,GAAGJ,GAAG,CAACK,MAAjB;AAEA,QAAMC,IAAI,GAAGL,GAAG,CAACE,KAAjB;AACA,QAAMI,IAAI,GAAGN,GAAG,CAACI,MAAjB,CAR6C,CAU7C;;AACA,QAAMqC,EAAE,GAAG3B,IAAI,CAAC4B,GAAL,CAAS,CAAT,EAAY5B,IAAI,CAACC,KAAL,CAAWd,IAAI,GAAGI,IAAlB,CAAZ,CAAX;AACA,QAAMsC,KAAK,GAAGtC,IAAI,GAAGoC,EAArB;AACA,QAAMG,EAAE,GAAG9B,IAAI,CAAC4B,GAAL,CAAS,CAAT,EAAY5B,IAAI,CAACC,KAAL,CAAWZ,IAAI,GAAGG,IAAlB,CAAZ,CAAX;AACA,QAAMuC,KAAK,GAAGvC,IAAI,GAAGsC,EAArB,CAd6C,CAgB7C;AACA;AACA;;AACA,QAAME,IAAI,GAAGC,MAAM,CAACC,KAAP,CAAaL,KAAK,GAAGxC,IAAR,GAAe,CAA5B,CAAb;;AACA,SAAK,IAAIO,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGP,IAApB,EAA0BO,CAAC,EAA3B,EAA+B;AAC7B,WAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgC,KAApB,EAA2BhC,CAAC,EAA5B,EAAgC;AAC9B;AAEA;AACA;AACA;AACA;AACA,YAAMkB,CAAC,GAAIlB,CAAC,IAAIV,IAAI,GAAG,CAAX,CAAF,GAAmB0C,KAA7B;AACA,YAAMM,IAAI,GAAGnC,IAAI,CAACC,KAAL,CAAWc,CAAX,CAAb;AACA,YAAMqB,CAAC,GAAGrB,CAAC,GAAGoB,IAAd;AACA,YAAME,MAAM,GAAG,CAACzC,CAAC,GAAGT,IAAJ,GAAWgD,IAAZ,IAAoB,CAAnC;AACA,YAAMG,OAAO,GAAG,CAAC1C,CAAC,GAAGiC,KAAJ,GAAYhC,CAAb,IAAkB,CAAlC;;AAEA,aAAK,IAAIS,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,CAApB,EAAuBA,CAAC,EAAxB,EAA4B;AAC1B,cAAMiC,IAAI,GAAGF,MAAM,GAAG/B,CAAtB;AACA,cAAMkC,EAAE,GACNL,IAAI,GAAG,CAAP,GAAW1C,MAAM,CAAC8C,IAAI,GAAG,CAAR,CAAjB,GAA8B,IAAI9C,MAAM,CAAC8C,IAAD,CAAV,GAAmB9C,MAAM,CAAC8C,IAAI,GAAG,CAAR,CADzD;AAEA,cAAME,EAAE,GAAGhD,MAAM,CAAC8C,IAAD,CAAjB;AACA,cAAMG,EAAE,GAAGjD,MAAM,CAAC8C,IAAI,GAAG,CAAR,CAAjB;AACA,cAAMI,EAAE,GACNR,IAAI,GAAGhD,IAAI,GAAG,CAAd,GACIM,MAAM,CAAC8C,IAAI,GAAG,CAAR,CADV,GAEI,IAAI9C,MAAM,CAAC8C,IAAI,GAAG,CAAR,CAAV,GAAuB9C,MAAM,CAAC8C,IAAD,CAHnC;AAIAP,UAAAA,IAAI,CAACM,OAAO,GAAGhC,CAAX,CAAJ,GAAoBD,WAAW,CAACmC,EAAD,EAAKC,EAAL,EAASC,EAAT,EAAaC,EAAb,EAAiBP,CAAjB,CAA/B;AACD;AACF;AACF,KA/C4C,CAgD7C;AAEA;AACA;AACA;;;AACA,QAAMQ,IAAI,GAAGX,MAAM,CAACC,KAAP,CAAaL,KAAK,GAAGE,KAAR,GAAgB,CAA7B,CAAb;;AACA,SAAK,IAAInC,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAGmC,KAApB,EAA2BnC,EAAC,EAA5B,EAAgC;AAC9B,WAAK,IAAIC,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAGgC,KAApB,EAA2BhC,EAAC,EAA5B,EAAgC;AAC9B;AAEA;AACA;AACA;AACA;AACA,YAAMqB,CAAC,GAAItB,EAAC,IAAIP,IAAI,GAAG,CAAX,CAAF,GAAmB0C,KAA7B;AACA,YAAMc,IAAI,GAAG7C,IAAI,CAACC,KAAL,CAAWiB,CAAX,CAAb;;AACA,YAAMkB,EAAC,GAAGlB,CAAC,GAAG2B,IAAd;;AACA,YAAMP,QAAO,GAAG,CAACO,IAAI,GAAGhB,KAAP,GAAehC,EAAhB,IAAqB,CAArC;;AACA,YAAMiD,OAAO,GAAG,CAAClD,EAAC,GAAGiC,KAAJ,GAAYhC,EAAb,IAAkB,CAAlC;;AACA,aAAK,IAAIS,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,CAApB,EAAuBA,EAAC,EAAxB,EAA4B;AAC1B,cAAMiC,KAAI,GAAGD,QAAO,GAAGhC,EAAvB;;AACA,cAAMyC,EAAE,GACNF,IAAI,GAAG,CAAP,GACIb,IAAI,CAACO,KAAI,GAAGV,KAAK,GAAG,CAAhB,CADR,GAEI,IAAIG,IAAI,CAACO,KAAD,CAAR,GAAiBP,IAAI,CAACO,KAAI,GAAGV,KAAK,GAAG,CAAhB,CAH3B;AAIA,cAAMmB,EAAE,GAAGhB,IAAI,CAACO,KAAD,CAAf;AACA,cAAMU,EAAE,GAAGjB,IAAI,CAACO,KAAI,GAAGV,KAAK,GAAG,CAAhB,CAAf;AACA,cAAMqB,EAAE,GACNL,IAAI,GAAGxD,IAAI,GAAG,CAAd,GACI2C,IAAI,CAACO,KAAI,GAAGV,KAAK,GAAG,CAAhB,CADR,GAEI,IAAIG,IAAI,CAACO,KAAI,GAAGV,KAAK,GAAG,CAAhB,CAAR,GAA6BG,IAAI,CAACO,KAAD,CAHvC;AAKAK,UAAAA,IAAI,CAACE,OAAO,GAAGxC,EAAX,CAAJ,GAAoBD,WAAW,CAAC0C,EAAD,EAAKC,EAAL,EAASC,EAAT,EAAaC,EAAb,EAAiBd,EAAjB,CAA/B;AACD;AACF;AACF,KAnF4C,CAoF7C;AAEA;AACA;;;AACA,QAAMe,CAAC,GAAGxB,EAAE,GAAGG,EAAf;;AACA,QAAIqB,CAAC,GAAG,CAAR,EAAW;AACT,WAAK,IAAIvD,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAGJ,IAApB,EAA0BI,GAAC,EAA3B,EAA+B;AAC7B,aAAK,IAAIC,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAGN,IAApB,EAA0BM,GAAC,EAA3B,EAA+B;AAC7B;AACA,cAAIuD,CAAC,GAAG,CAAR;AACA,cAAIC,CAAC,GAAG,CAAR;AACA,cAAIC,CAAC,GAAG,CAAR;AACA,cAAIC,CAAC,GAAG,CAAR;AACA,cAAIC,UAAU,GAAG,CAAjB;;AAEA,eAAK,IAAItC,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAGY,EAApB,EAAwBZ,EAAC,EAAzB,EAA6B;AAC3B,gBAAM2B,KAAI,GAAGjD,GAAC,GAAGkC,EAAJ,GAASZ,EAAtB;;AAEA,iBAAK,IAAIH,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAGY,EAApB,EAAwBZ,EAAC,EAAzB,EAA6B;AAC3B,kBAAMoB,KAAI,GAAGtC,GAAC,GAAG8B,EAAJ,GAASZ,EAAtB;;AACA,kBAAM0C,KAAK,GAAG,CAACZ,KAAI,GAAGhB,KAAP,GAAeM,KAAhB,IAAwB,CAAtC;AACA,kBAAMuB,UAAU,GAAGd,IAAI,CAACa,KAAK,GAAG,CAAT,CAAvB;;AAEA,kBAAIC,UAAJ,EAAgB;AACdN,gBAAAA,CAAC,IAAIR,IAAI,CAACa,KAAD,CAAT;AACAJ,gBAAAA,CAAC,IAAIT,IAAI,CAACa,KAAK,GAAG,CAAT,CAAT;AACAH,gBAAAA,CAAC,IAAIV,IAAI,CAACa,KAAK,GAAG,CAAT,CAAT;AACAD,gBAAAA,UAAU;AACX;;AAEDD,cAAAA,CAAC,IAAIG,UAAL;AACD;AACF;;AAED,cAAM7C,GAAG,GAAG,CAACjB,GAAC,GAAGL,IAAJ,GAAWM,GAAZ,IAAiB,CAA7B;AACAF,UAAAA,MAAM,CAACkB,GAAD,CAAN,GAAc2C,UAAU,GAAGxD,IAAI,CAACW,KAAL,CAAWyC,CAAC,GAAGI,UAAf,CAAH,GAAgC,CAAxD;AACA7D,UAAAA,MAAM,CAACkB,GAAG,GAAG,CAAP,CAAN,GAAkB2C,UAAU,GAAGxD,IAAI,CAACW,KAAL,CAAW0C,CAAC,GAAGG,UAAf,CAAH,GAAgC,CAA5D;AACA7D,UAAAA,MAAM,CAACkB,GAAG,GAAG,CAAP,CAAN,GAAkB2C,UAAU,GAAGxD,IAAI,CAACW,KAAL,CAAW2C,CAAC,GAAGE,UAAf,CAAH,GAAgC,CAA5D;AACA7D,UAAAA,MAAM,CAACkB,GAAG,GAAG,CAAP,CAAN,GAAkBb,IAAI,CAACW,KAAL,CAAW4C,CAAC,GAAGJ,CAAf,CAAlB;AACD;AACF;AACF,KApCD,MAoCO;AACL;AACAjE,MAAAA,GAAG,CAACQ,IAAJ,GAAWkD,IAAX;AACD;AACF,GApNc;AAsNfe,EAAAA,oBAtNe,gCAsNM1E,GAtNN,EAsNWC,GAtNX,EAsNgBwC,OAtNhB,EAsNyB;AACtC,QAAMkC,gBAAgB,GAAG,SAAnBA,gBAAmB,CAASpB,EAAT,EAAaC,EAAb,EAAiBC,EAAjB,EAAqBC,EAArB,EAAyBP,CAAzB,EAA4B;AACnD,UAAMyB,EAAE,GAAGlB,EAAE,GAAGD,EAAL,GAAUF,EAAV,GAAeC,EAA1B;AACA,UAAMqB,EAAE,GAAGtB,EAAE,GAAGC,EAAL,GAAUoB,EAArB;AACA,UAAME,EAAE,GAAGrB,EAAE,GAAGF,EAAhB;AACA,UAAMwB,EAAE,GAAGvB,EAAX;AACA,aAAOzC,IAAI,CAAC4B,GAAL,CACL,CADK,EAEL5B,IAAI,CAACuB,GAAL,CAAS,GAAT,EAAcsC,EAAE,IAAIzB,CAAC,GAAGA,CAAJ,GAAQA,CAAZ,CAAF,GAAmB0B,EAAE,IAAI1B,CAAC,GAAGA,CAAR,CAArB,GAAkC2B,EAAE,GAAG3B,CAAvC,GAA2C4B,EAAzD,CAFK,CAAP;AAID,KATD;;AAWA,WAAO,KAAKvC,cAAL,CAAoBxC,GAApB,EAAyBC,GAAzB,EAA8BwC,OAA9B,EAAuCkC,gBAAvC,CAAP;AACD,GAnOc;AAqOfK,EAAAA,oBArOe,gCAqOMhF,GArON,EAqOWC,GArOX,EAqOgBwC,OArOhB,EAqOyB;AACtC,QAAMwC,kBAAkB,GAAG,SAArBA,kBAAqB,CAAS1B,EAAT,EAAaC,EAAb,EAAiBC,EAAjB,EAAqBC,EAArB,EAAyBP,CAAzB,EAA4B;AACrD,UAAM+B,EAAE,GAAG1B,EAAX;AACA,UAAM2B,EAAE,GAAG,OAAO1B,EAAE,GAAGF,EAAZ,CAAX;AACA,UAAM6B,EAAE,GAAG7B,EAAE,GAAG,MAAMC,EAAX,GAAgB,IAAIC,EAApB,GAAyB,MAAMC,EAA1C;AACA,UAAM2B,EAAE,GAAG,OAAO3B,EAAE,GAAGH,EAAZ,IAAkB,OAAOC,EAAE,GAAGC,EAAZ,CAA7B;AACA,aAAO1C,IAAI,CAAC4B,GAAL,CACL,CADK,EAEL5B,IAAI,CAACuB,GAAL,CAAS,GAAT,EAAcvB,IAAI,CAACW,KAAL,CAAW,CAAC,CAAC2D,EAAE,GAAGlC,CAAL,GAASiC,EAAV,IAAgBjC,CAAhB,GAAoBgC,EAArB,IAA2BhC,CAA3B,GAA+B+B,EAA1C,CAAd,CAFK,CAAP;AAID,KATD;;AAWA,WAAO,KAAK1C,cAAL,CAAoBxC,GAApB,EAAyBC,GAAzB,EAA8BwC,OAA9B,EAAuCwC,kBAAvC,CAAP;AACD,GAlPc;AAoPfK,EAAAA,mBApPe,+BAoPKtF,GApPL,EAoPUC,GApPV,EAoPewC,OApPf,EAoPwB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAM8C,iBAAiB,GAAG,SAApBA,iBAAoB,CAAShC,EAAT,EAAaC,EAAb,EAAiBC,EAAjB,EAAqBC,EAArB,EAAyBP,CAAzB,EAA4B;AACpD;AACA,UAAMqC,GAAG,GAAGhC,EAAE,GAAG,CAACC,EAAE,GAAGF,EAAN,IAAY,CAA7B;AACA,UAAMkC,GAAG,GAAGhC,EAAE,GAAG,CAACC,EAAE,GAAGF,EAAN,IAAY,CAA7B;AACA,UAAMkC,EAAE,GAAG,IAAIvC,CAAf;AACA,UAAM+B,EAAE,GAAG1B,EAAE,GAAGkC,EAAL,GAAUA,EAAV,GAAeA,EAA1B;AACA,UAAMP,EAAE,GAAG,IAAIK,GAAJ,GAAUE,EAAV,GAAeA,EAAf,GAAoBvC,CAA/B;AACA,UAAMiC,EAAE,GAAG,IAAIK,GAAJ,GAAUC,EAAV,GAAevC,CAAf,GAAmBA,CAA9B;AACA,UAAMkC,EAAE,GAAG5B,EAAE,GAAGN,CAAL,GAASA,CAAT,GAAaA,CAAxB;AACA,aAAOpC,IAAI,CAAC4B,GAAL,CAAS,CAAT,EAAY5B,IAAI,CAACuB,GAAL,CAAS,GAAT,EAAcvB,IAAI,CAACW,KAAL,CAAWwD,EAAE,GAAGC,EAAL,GAAUC,EAAV,GAAeC,EAA1B,CAAd,CAAZ,CAAP;AACD,KAVD;;AAYA,WAAO,KAAK7C,cAAL,CAAoBxC,GAApB,EAAyBC,GAAzB,EAA8BwC,OAA9B,EAAuC8C,iBAAvC,CAAP;AACD;AA7Qc,CAAjB", "sourcesContent": ["/**\n * Copyright (c) 2015 <PERSON><PERSON> Roche\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:</p>\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\nmodule.exports = {\n  nearestNeighbor(src, dst) {\n    const wSrc = src.width;\n    const hSrc = src.height;\n\n    const wDst = dst.width;\n    const hDst = dst.height;\n\n    const bufSrc = src.data;\n    const bufDst = dst.data;\n\n    for (let i = 0; i < hDst; i++) {\n      for (let j = 0; j < wDst; j++) {\n        let posDst = (i * wDst + j) * 4;\n\n        const iSrc = Math.floor((i * hSrc) / hDst);\n        const jSrc = Math.floor((j * wSrc) / wDst);\n        let posSrc = (iSrc * wSrc + jSrc) * 4;\n\n        bufDst[posDst++] = bufSrc[posSrc++];\n        bufDst[posDst++] = bufSrc[posSrc++];\n        bufDst[posDst++] = bufSrc[posSrc++];\n        bufDst[posDst++] = bufSrc[posSrc++];\n      }\n    }\n  },\n\n  bilinearInterpolation(src, dst) {\n    const wSrc = src.width;\n    const hSrc = src.height;\n\n    const wDst = dst.width;\n    const hDst = dst.height;\n\n    const bufSrc = src.data;\n    const bufDst = dst.data;\n\n    const interpolate = function(k, kMin, vMin, kMax, vMax) {\n      // special case - k is integer\n      if (kMin === kMax) {\n        return vMin;\n      }\n\n      return Math.round((k - kMin) * vMax + (kMax - k) * vMin);\n    };\n\n    const assign = function(pos, offset, x, xMin, xMax, y, yMin, yMax) {\n      let posMin = (yMin * wSrc + xMin) * 4 + offset;\n      let posMax = (yMin * wSrc + xMax) * 4 + offset;\n      const vMin = interpolate(x, xMin, bufSrc[posMin], xMax, bufSrc[posMax]);\n\n      // special case, y is integer\n      if (yMax === yMin) {\n        bufDst[pos + offset] = vMin;\n      } else {\n        posMin = (yMax * wSrc + xMin) * 4 + offset;\n        posMax = (yMax * wSrc + xMax) * 4 + offset;\n        const vMax = interpolate(x, xMin, bufSrc[posMin], xMax, bufSrc[posMax]);\n\n        bufDst[pos + offset] = interpolate(y, yMin, vMin, yMax, vMax);\n      }\n    };\n\n    for (let i = 0; i < hDst; i++) {\n      for (let j = 0; j < wDst; j++) {\n        const posDst = (i * wDst + j) * 4;\n        // x & y in src coordinates\n        const x = (j * wSrc) / wDst;\n        const xMin = Math.floor(x);\n        const xMax = Math.min(Math.ceil(x), wSrc - 1);\n\n        const y = (i * hSrc) / hDst;\n        const yMin = Math.floor(y);\n        const yMax = Math.min(Math.ceil(y), hSrc - 1);\n\n        assign(posDst, 0, x, xMin, xMax, y, yMin, yMax);\n        assign(posDst, 1, x, xMin, xMax, y, yMin, yMax);\n        assign(posDst, 2, x, xMin, xMax, y, yMin, yMax);\n        assign(posDst, 3, x, xMin, xMax, y, yMin, yMax);\n      }\n    }\n  },\n\n  _interpolate2D(src, dst, options, interpolate) {\n    const bufSrc = src.data;\n    const bufDst = dst.data;\n\n    const wSrc = src.width;\n    const hSrc = src.height;\n\n    const wDst = dst.width;\n    const hDst = dst.height;\n\n    // when dst smaller than src/2, interpolate first to a multiple between 0.5 and 1.0 src, then sum squares\n    const wM = Math.max(1, Math.floor(wSrc / wDst));\n    const wDst2 = wDst * wM;\n    const hM = Math.max(1, Math.floor(hSrc / hDst));\n    const hDst2 = hDst * hM;\n\n    // ===========================================================\n    // Pass 1 - interpolate rows\n    // buf1 has width of dst2 and height of src\n    const buf1 = Buffer.alloc(wDst2 * hSrc * 4);\n    for (let i = 0; i < hSrc; i++) {\n      for (let j = 0; j < wDst2; j++) {\n        // i in src coords, j in dst coords\n\n        // calculate x in src coords\n        // this interpolation requires 4 sample points and the two inner ones must be real\n        // the outer points can be fudged for the edges.\n        // therefore (wSrc-1)/wDst2\n        const x = (j * (wSrc - 1)) / wDst2;\n        const xPos = Math.floor(x);\n        const t = x - xPos;\n        const srcPos = (i * wSrc + xPos) * 4;\n        const buf1Pos = (i * wDst2 + j) * 4;\n\n        for (let k = 0; k < 4; k++) {\n          const kPos = srcPos + k;\n          const x0 =\n            xPos > 0 ? bufSrc[kPos - 4] : 2 * bufSrc[kPos] - bufSrc[kPos + 4];\n          const x1 = bufSrc[kPos];\n          const x2 = bufSrc[kPos + 4];\n          const x3 =\n            xPos < wSrc - 2\n              ? bufSrc[kPos + 8]\n              : 2 * bufSrc[kPos + 4] - bufSrc[kPos];\n          buf1[buf1Pos + k] = interpolate(x0, x1, x2, x3, t);\n        }\n      }\n    }\n    // this._writeFile(wDst2, hSrc, buf1, \"out/buf1.jpg\");\n\n    // ===========================================================\n    // Pass 2 - interpolate columns\n    // buf2 has width and height of dst2\n    const buf2 = Buffer.alloc(wDst2 * hDst2 * 4);\n    for (let i = 0; i < hDst2; i++) {\n      for (let j = 0; j < wDst2; j++) {\n        // i&j in dst2 coords\n\n        // calculate y in buf1 coords\n        // this interpolation requires 4 sample points and the two inner ones must be real\n        // the outer points can be fudged for the edges.\n        // therefore (hSrc-1)/hDst2\n        const y = (i * (hSrc - 1)) / hDst2;\n        const yPos = Math.floor(y);\n        const t = y - yPos;\n        const buf1Pos = (yPos * wDst2 + j) * 4;\n        const buf2Pos = (i * wDst2 + j) * 4;\n        for (let k = 0; k < 4; k++) {\n          const kPos = buf1Pos + k;\n          const y0 =\n            yPos > 0\n              ? buf1[kPos - wDst2 * 4]\n              : 2 * buf1[kPos] - buf1[kPos + wDst2 * 4];\n          const y1 = buf1[kPos];\n          const y2 = buf1[kPos + wDst2 * 4];\n          const y3 =\n            yPos < hSrc - 2\n              ? buf1[kPos + wDst2 * 8]\n              : 2 * buf1[kPos + wDst2 * 4] - buf1[kPos];\n\n          buf2[buf2Pos + k] = interpolate(y0, y1, y2, y3, t);\n        }\n      }\n    }\n    // this._writeFile(wDst2, hDst2, buf2, \"out/buf2.jpg\");\n\n    // ===========================================================\n    // Pass 3 - scale to dst\n    const m = wM * hM;\n    if (m > 1) {\n      for (let i = 0; i < hDst; i++) {\n        for (let j = 0; j < wDst; j++) {\n          // i&j in dst bounded coords\n          let r = 0;\n          let g = 0;\n          let b = 0;\n          let a = 0;\n          let realColors = 0;\n\n          for (let y = 0; y < hM; y++) {\n            const yPos = i * hM + y;\n\n            for (let x = 0; x < wM; x++) {\n              const xPos = j * wM + x;\n              const xyPos = (yPos * wDst2 + xPos) * 4;\n              const pixelAlpha = buf2[xyPos + 3];\n\n              if (pixelAlpha) {\n                r += buf2[xyPos];\n                g += buf2[xyPos + 1];\n                b += buf2[xyPos + 2];\n                realColors++;\n              }\n\n              a += pixelAlpha;\n            }\n          }\n\n          const pos = (i * wDst + j) * 4;\n          bufDst[pos] = realColors ? Math.round(r / realColors) : 0;\n          bufDst[pos + 1] = realColors ? Math.round(g / realColors) : 0;\n          bufDst[pos + 2] = realColors ? Math.round(b / realColors) : 0;\n          bufDst[pos + 3] = Math.round(a / m);\n        }\n      }\n    } else {\n      // replace dst buffer with buf2\n      dst.data = buf2;\n    }\n  },\n\n  bicubicInterpolation(src, dst, options) {\n    const interpolateCubic = function(x0, x1, x2, x3, t) {\n      const a0 = x3 - x2 - x0 + x1;\n      const a1 = x0 - x1 - a0;\n      const a2 = x2 - x0;\n      const a3 = x1;\n      return Math.max(\n        0,\n        Math.min(255, a0 * (t * t * t) + a1 * (t * t) + a2 * t + a3)\n      );\n    };\n\n    return this._interpolate2D(src, dst, options, interpolateCubic);\n  },\n\n  hermiteInterpolation(src, dst, options) {\n    const interpolateHermite = function(x0, x1, x2, x3, t) {\n      const c0 = x1;\n      const c1 = 0.5 * (x2 - x0);\n      const c2 = x0 - 2.5 * x1 + 2 * x2 - 0.5 * x3;\n      const c3 = 0.5 * (x3 - x0) + 1.5 * (x1 - x2);\n      return Math.max(\n        0,\n        Math.min(255, Math.round(((c3 * t + c2) * t + c1) * t + c0))\n      );\n    };\n\n    return this._interpolate2D(src, dst, options, interpolateHermite);\n  },\n\n  bezierInterpolation(src, dst, options) {\n    // between 2 points y(n), y(n+1), use next points out, y(n-1), y(n+2)\n    // to predict control points (a & b) to be placed at n+0.5\n    //  ya(n) = y(n) + (y(n+1)-y(n-1))/4\n    //  yb(n) = y(n+1) - (y(n+2)-y(n))/4\n    // then use std bezier to interpolate [n,n+1)\n    //  y(n+t) = y(n)*(1-t)^3 + 3 * ya(n)*(1-t)^2*t + 3 * yb(n)*(1-t)*t^2 + y(n+1)*t^3\n    //  note the 3* factor for the two control points\n    // for edge cases, can choose:\n    //  y(-1) = y(0) - 2*(y(1)-y(0))\n    //  y(w) = y(w-1) + 2*(y(w-1)-y(w-2))\n    // but can go with y(-1) = y(0) and y(w) = y(w-1)\n    const interpolateBezier = function(x0, x1, x2, x3, t) {\n      // x1, x2 are the knots, use x0 and x3 to calculate control points\n      const cp1 = x1 + (x2 - x0) / 4;\n      const cp2 = x2 - (x3 - x1) / 4;\n      const nt = 1 - t;\n      const c0 = x1 * nt * nt * nt;\n      const c1 = 3 * cp1 * nt * nt * t;\n      const c2 = 3 * cp2 * nt * t * t;\n      const c3 = x2 * t * t * t;\n      return Math.max(0, Math.min(255, Math.round(c0 + c1 + c2 + c3)));\n    };\n\n    return this._interpolate2D(src, dst, options, interpolateBezier);\n  }\n};\n"], "file": "resize2.js"}