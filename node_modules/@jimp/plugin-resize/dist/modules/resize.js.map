{"version": 3, "sources": ["../../src/modules/resize.js"], "names": ["Resize", "widthOriginal", "heightOriginal", "targetWidth", "targetHeight", "blendAlpha", "interpolationPass", "resizeCallback", "Math", "abs", "floor", "colorChannels", "Boolean", "targetWidthMultipliedByChannels", "originalWidthMultipliedByChannels", "originalHeightMultipliedByChannels", "widthPassResultSize", "finalResultSize", "initialize", "prototype", "configure<PERSON><PERSON><PERSON>", "Error", "resizeWidth", "bypassResizer", "ratioWeightWidthPass", "initializeFirstPassBuffers", "resizeWidthInterpolatedRGBA", "resizeWidthInterpolatedRGB", "resizeWidthRGBA", "resizeWidthRGB", "resizeHeight", "ratioWeightHeightPass", "initializeSecondPassBuffers", "resizeHeightInterpolated", "resizeHeightRGBA", "resizeHeightRGB", "_resizeWidthInterpolatedRGBChannels", "buffer", "fourthChannel", "channelsNum", "ratioWeight", "outputBuffer", "widthBuffer", "weight", "finalOffset", "pixelOffset", "firstWeight", "secondWeight", "targetPosition", "interpolationWidthSourceReadStop", "_resizeWidthRGBChannels", "ratioWeightDivisor", "nextLineOffsetOriginalWidth", "nextLineOffsetTargetWidth", "output", "outputWidthWorkBench", "trustworthyColorsCount", "outputWidthWorkBenchOpaquePixelsCount", "amountToNext", "actualPosition", "currentPosition", "line", "outputOffset", "multiplier", "r", "g", "b", "a", "min", "_resizeHeightRGBChannels", "outputHeightWorkBench", "heightBuffer", "outputHeightWorkBenchOpaquePixelsCount", "caret", "round", "pixelOffsetAccumulated", "pixelOffsetAccumulated2", "interpolationHeightSourceReadStop", "resize", "BILINEARAlgo", "generateFloatBuffer", "generateFloat64Buffer", "generateUint8Buffer", "bufferLength", "Float32Array", "error", "Float64Array", "Uint8Array", "module", "exports"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AAEA,SAASA,MAAT,CACEC,aADF,EAEEC,cAFF,EAGEC,WAHF,EAIEC,YAJF,EAKEC,UALF,EAMEC,iBANF,EAOEC,cAPF,EAQE;AACA,OAAKN,aAAL,GAAqBO,IAAI,CAACC,GAAL,CAASD,IAAI,CAACE,KAAL,CAAWT,aAAX,KAA6B,CAAtC,CAArB;AACA,OAAKC,cAAL,GAAsBM,IAAI,CAACC,GAAL,CAASD,IAAI,CAACE,KAAL,CAAWR,cAAX,KAA8B,CAAvC,CAAtB;AACA,OAAKC,WAAL,GAAmBK,IAAI,CAACC,GAAL,CAASD,IAAI,CAACE,KAAL,CAAWP,WAAX,KAA2B,CAApC,CAAnB;AACA,OAAKC,YAAL,GAAoBI,IAAI,CAACC,GAAL,CAASD,IAAI,CAACE,KAAL,CAAWN,YAAX,KAA4B,CAArC,CAApB;AACA,OAAKO,aAAL,GAAqBN,UAAU,GAAG,CAAH,GAAO,CAAtC;AACA,OAAKC,iBAAL,GAAyBM,OAAO,CAACN,iBAAD,CAAhC;AACA,OAAKC,cAAL,GACE,OAAOA,cAAP,KAA0B,UAA1B,GAAuCA,cAAvC,GAAwD,YAAW,CAAE,CADvE;AAGA,OAAKM,+BAAL,GAAuC,KAAKV,WAAL,GAAmB,KAAKQ,aAA/D;AACA,OAAKG,iCAAL,GACE,KAAKb,aAAL,GAAqB,KAAKU,aAD5B;AAEA,OAAKI,kCAAL,GACE,KAAKb,cAAL,GAAsB,KAAKS,aAD7B;AAEA,OAAKK,mBAAL,GACE,KAAKH,+BAAL,GAAuC,KAAKX,cAD9C;AAEA,OAAKe,eAAL,GACE,KAAKJ,+BAAL,GAAuC,KAAKT,YAD9C;AAEA,OAAKc,UAAL;AACD;;AAEDlB,MAAM,CAACmB,SAAP,CAAiBD,UAAjB,GAA8B,YAAW;AACvC;AACA,MACE,KAAKjB,aAAL,GAAqB,CAArB,IACA,KAAKC,cAAL,GAAsB,CADtB,IAEA,KAAKC,WAAL,GAAmB,CAFnB,IAGA,KAAKC,YAAL,GAAoB,CAJtB,EAKE;AACA,SAAKgB,eAAL;AACD,GAPD,MAOO;AACL,UAAM,IAAIC,KAAJ,CAAU,6CAAV,CAAN;AACD;AACF,CAZD;;AAcArB,MAAM,CAACmB,SAAP,CAAiBC,eAAjB,GAAmC,YAAW;AAC5C,MAAI,KAAKnB,aAAL,KAAuB,KAAKE,WAAhC,EAA6C;AAC3C;AACA,SAAKmB,WAAL,GAAmB,KAAKC,aAAxB;AACD,GAHD,MAGO;AACL;AACA,SAAKC,oBAAL,GAA4B,KAAKvB,aAAL,GAAqB,KAAKE,WAAtD;;AACA,QAAI,KAAKqB,oBAAL,GAA4B,CAA5B,IAAiC,KAAKlB,iBAA1C,EAA6D;AAC3D,WAAKmB,0BAAL,CAAgC,IAAhC;AACA,WAAKH,WAAL,GACE,KAAKX,aAAL,KAAuB,CAAvB,GACI,KAAKe,2BADT,GAEI,KAAKC,0BAHX;AAID,KAND,MAMO;AACL,WAAKF,0BAAL,CAAgC,KAAhC;AACA,WAAKH,WAAL,GACE,KAAKX,aAAL,KAAuB,CAAvB,GAA2B,KAAKiB,eAAhC,GAAkD,KAAKC,cADzD;AAED;AACF;;AAED,MAAI,KAAK3B,cAAL,KAAwB,KAAKE,YAAjC,EAA+C;AAC7C;AACA,SAAK0B,YAAL,GAAoB,KAAKP,aAAzB;AACD,GAHD,MAGO;AACL;AACA,SAAKQ,qBAAL,GAA6B,KAAK7B,cAAL,GAAsB,KAAKE,YAAxD;;AACA,QAAI,KAAK2B,qBAAL,GAA6B,CAA7B,IAAkC,KAAKzB,iBAA3C,EAA8D;AAC5D,WAAK0B,2BAAL,CAAiC,IAAjC;AACA,WAAKF,YAAL,GAAoB,KAAKG,wBAAzB;AACD,KAHD,MAGO;AACL,WAAKD,2BAAL,CAAiC,KAAjC;AACA,WAAKF,YAAL,GACE,KAAKnB,aAAL,KAAuB,CAAvB,GAA2B,KAAKuB,gBAAhC,GAAmD,KAAKC,eAD1D;AAED;AACF;AACF,CAnCD;;AAqCAnC,MAAM,CAACmB,SAAP,CAAiBiB,mCAAjB,GAAuD,UACrDC,MADqD,EAErDC,aAFqD,EAGrD;AACA,MAAMC,WAAW,GAAGD,aAAa,GAAG,CAAH,GAAO,CAAxC;AACA,MAAME,WAAW,GAAG,KAAKhB,oBAAzB;AACA,MAAMiB,YAAY,GAAG,KAAKC,WAA1B;AAEA,MAAIC,MAAM,GAAG,CAAb;AACA,MAAIC,WAAW,GAAG,CAAlB;AACA,MAAIC,WAAW,GAAG,CAAlB;AACA,MAAIC,WAAW,GAAG,CAAlB;AACA,MAAIC,YAAY,GAAG,CAAnB;AACA,MAAIC,cAAJ,CAVA,CAYA;;AACA,OACEA,cAAc,GAAG,CADnB,EAEEL,MAAM,GAAG,IAAI,CAFf,EAGEK,cAAc,IAAIT,WAAlB,EAA+BI,MAAM,IAAIH,WAH3C,EAIE;AACA,SACEI,WAAW,GAAGI,cAAd,EAA8BH,WAAW,GAAG,CAD9C,EAEED,WAAW,GAAG,KAAK5B,mBAFrB,EAGE6B,WAAW,IAAI,KAAK/B,iCAApB,EACE8B,WAAW,IAAI,KAAK/B,+BAJxB,EAKE;AACA4B,MAAAA,YAAY,CAACG,WAAD,CAAZ,GAA4BP,MAAM,CAACQ,WAAD,CAAlC;AACAJ,MAAAA,YAAY,CAACG,WAAW,GAAG,CAAf,CAAZ,GAAgCP,MAAM,CAACQ,WAAW,GAAG,CAAf,CAAtC;AACAJ,MAAAA,YAAY,CAACG,WAAW,GAAG,CAAf,CAAZ,GAAgCP,MAAM,CAACQ,WAAW,GAAG,CAAf,CAAtC;AACA,UAAIP,aAAJ,EACEG,YAAY,CAACG,WAAW,GAAG,CAAf,CAAZ,GAAgCP,MAAM,CAACQ,WAAW,GAAG,CAAf,CAAtC;AACH;AACF,GA9BD,CAgCA;;;AACAF,EAAAA,MAAM,IAAI,IAAI,CAAd;AACA,MAAIM,gCAAJ;;AAEA,OACEA,gCAAgC,GAAG,KAAKhD,aAAL,GAAqB,CAD1D,EAEE0C,MAAM,GAAGM,gCAFX,EAGED,cAAc,IAAIT,WAAlB,EAA+BI,MAAM,IAAIH,WAH3C,EAIE;AACA;AACAO,IAAAA,YAAY,GAAGJ,MAAM,GAAG,CAAxB;AACAG,IAAAA,WAAW,GAAG,IAAIC,YAAlB,CAHA,CAIA;;AACA,SACEH,WAAW,GAAGI,cAAd,EACEH,WAAW,GAAGrC,IAAI,CAACE,KAAL,CAAWiC,MAAX,IAAqBJ,WAFvC,EAGEK,WAAW,GAAG,KAAK5B,mBAHrB,EAIE6B,WAAW,IAAI,KAAK/B,iCAApB,EACE8B,WAAW,IAAI,KAAK/B,+BALxB,EAME;AACA4B,MAAAA,YAAY,CAACG,WAAW,GAAG,CAAf,CAAZ,GACEP,MAAM,CAACQ,WAAW,GAAG,CAAf,CAAN,GAA0BC,WAA1B,GACAT,MAAM,CAACQ,WAAW,GAAGN,WAAd,GAA4B,CAA7B,CAAN,GAAwCQ,YAF1C;AAGAN,MAAAA,YAAY,CAACG,WAAW,GAAG,CAAf,CAAZ,GACEP,MAAM,CAACQ,WAAW,GAAG,CAAf,CAAN,GAA0BC,WAA1B,GACAT,MAAM,CAACQ,WAAW,GAAGN,WAAd,GAA4B,CAA7B,CAAN,GAAwCQ,YAF1C;AAGAN,MAAAA,YAAY,CAACG,WAAW,GAAG,CAAf,CAAZ,GACEP,MAAM,CAACQ,WAAW,GAAG,CAAf,CAAN,GAA0BC,WAA1B,GACAT,MAAM,CAACQ,WAAW,GAAGN,WAAd,GAA4B,CAA7B,CAAN,GAAwCQ,YAF1C;AAGA,UAAIT,aAAJ,EACEG,YAAY,CAACG,WAAW,GAAG,CAAf,CAAZ,GACEP,MAAM,CAACQ,WAAW,GAAG,CAAf,CAAN,GAA0BC,WAA1B,GACAT,MAAM,CAACQ,WAAW,GAAGN,WAAd,GAA4B,CAA7B,CAAN,GAAwCQ,YAF1C;AAGH;AACF,GAlED,CAoEA;;;AACA,OACEE,gCAAgC,GAC9B,KAAKnC,iCAAL,GAAyCyB,WAF7C,EAGES,cAAc,GAAG,KAAKnC,+BAHxB,EAIEmC,cAAc,IAAIT,WAJpB,EAKE;AACA,SACEK,WAAW,GAAGI,cAAd,EACEH,WAAW,GAAGI,gCAFlB,EAGEL,WAAW,GAAG,KAAK5B,mBAHrB,EAIE6B,WAAW,IAAI,KAAK/B,iCAApB,EACE8B,WAAW,IAAI,KAAK/B,+BALxB,EAME;AACA4B,MAAAA,YAAY,CAACG,WAAD,CAAZ,GAA4BP,MAAM,CAACQ,WAAD,CAAlC;AACAJ,MAAAA,YAAY,CAACG,WAAW,GAAG,CAAf,CAAZ,GAAgCP,MAAM,CAACQ,WAAW,GAAG,CAAf,CAAtC;AACAJ,MAAAA,YAAY,CAACG,WAAW,GAAG,CAAf,CAAZ,GAAgCP,MAAM,CAACQ,WAAW,GAAG,CAAf,CAAtC;AACA,UAAIP,aAAJ,EACEG,YAAY,CAACG,WAAW,GAAG,CAAf,CAAZ,GAAgCP,MAAM,CAACQ,WAAW,GAAG,CAAf,CAAtC;AACH;AACF;;AAED,SAAOJ,YAAP;AACD,CA9FD;;AAgGAzC,MAAM,CAACmB,SAAP,CAAiB+B,uBAAjB,GAA2C,UAASb,MAAT,EAAiBC,aAAjB,EAAgC;AACzE,MAAMC,WAAW,GAAGD,aAAa,GAAG,CAAH,GAAO,CAAxC;AACA,MAAME,WAAW,GAAG,KAAKhB,oBAAzB;AACA,MAAM2B,kBAAkB,GAAG,IAAIX,WAA/B;AACA,MAAMY,2BAA2B,GAC/B,KAAKtC,iCAAL,GAAyCyB,WAAzC,GAAuD,CADzD;AAEA,MAAMc,yBAAyB,GAC7B,KAAKxC,+BAAL,GAAuC0B,WAAvC,GAAqD,CADvD;AAEA,MAAMe,MAAM,GAAG,KAAKC,oBAApB;AACA,MAAMd,YAAY,GAAG,KAAKC,WAA1B;AACA,MAAMc,sBAAsB,GAAG,KAAKC,qCAApC;AAEA,MAAId,MAAM,GAAG,CAAb;AACA,MAAIe,YAAY,GAAG,CAAnB;AACA,MAAIC,cAAc,GAAG,CAArB;AACA,MAAIC,eAAe,GAAG,CAAtB;AACA,MAAIC,IAAI,GAAG,CAAX;AACA,MAAIhB,WAAW,GAAG,CAAlB;AACA,MAAIiB,YAAY,GAAG,CAAnB;AACA,MAAIC,UAAU,GAAG,CAAjB;AACA,MAAIC,CAAC,GAAG,CAAR;AACA,MAAIC,CAAC,GAAG,CAAR;AACA,MAAIC,CAAC,GAAG,CAAR;AACA,MAAIC,CAAC,GAAG,CAAR;;AAEA,KAAG;AACD,SAAKN,IAAI,GAAG,CAAZ,EAAeA,IAAI,GAAG,KAAK9C,kCAA3B,GAAiE;AAC/DuC,MAAAA,MAAM,CAACO,IAAI,EAAL,CAAN,GAAiB,CAAjB;AACAP,MAAAA,MAAM,CAACO,IAAI,EAAL,CAAN,GAAiB,CAAjB;AACAP,MAAAA,MAAM,CAACO,IAAI,EAAL,CAAN,GAAiB,CAAjB;;AACA,UAAIvB,aAAJ,EAAmB;AACjBgB,QAAAA,MAAM,CAACO,IAAI,EAAL,CAAN,GAAiB,CAAjB;AACAL,QAAAA,sBAAsB,CAACK,IAAI,GAAGtB,WAAP,GAAqB,CAAtB,CAAtB,GAAiD,CAAjD;AACD;AACF;;AAEDI,IAAAA,MAAM,GAAGH,WAAT;;AAEA,OAAG;AACDkB,MAAAA,YAAY,GAAG,IAAIC,cAAJ,GAAqBC,eAApC;AACAG,MAAAA,UAAU,GAAGvD,IAAI,CAAC4D,GAAL,CAASzB,MAAT,EAAiBe,YAAjB,CAAb;;AACA,WACEG,IAAI,GAAG,CAAP,EAAUhB,WAAW,GAAGc,cAD1B,EAEEE,IAAI,GAAG,KAAK9C,kCAFd,EAGE8B,WAAW,IAAIO,2BAHjB,EAIE;AACAY,QAAAA,CAAC,GAAG3B,MAAM,CAACQ,WAAD,CAAV;AACAoB,QAAAA,CAAC,GAAG5B,MAAM,CAAC,EAAEQ,WAAH,CAAV;AACAqB,QAAAA,CAAC,GAAG7B,MAAM,CAAC,EAAEQ,WAAH,CAAV;AACAsB,QAAAA,CAAC,GAAG7B,aAAa,GAAGD,MAAM,CAAC,EAAEQ,WAAH,CAAT,GAA2B,GAA5C,CAJA,CAKA;;AACAS,QAAAA,MAAM,CAACO,IAAI,EAAL,CAAN,IAAkB,CAACM,CAAC,GAAGH,CAAH,GAAO,CAAT,IAAcD,UAAhC;AACAT,QAAAA,MAAM,CAACO,IAAI,EAAL,CAAN,IAAkB,CAACM,CAAC,GAAGF,CAAH,GAAO,CAAT,IAAcF,UAAhC;AACAT,QAAAA,MAAM,CAACO,IAAI,EAAL,CAAN,IAAkB,CAACM,CAAC,GAAGD,CAAH,GAAO,CAAT,IAAcH,UAAhC;;AACA,YAAIzB,aAAJ,EAAmB;AACjBgB,UAAAA,MAAM,CAACO,IAAI,EAAL,CAAN,IAAkBM,CAAC,GAAGJ,UAAtB;AACAP,UAAAA,sBAAsB,CAACK,IAAI,GAAGtB,WAAP,GAAqB,CAAtB,CAAtB,IAAkD4B,CAAC,GAAGJ,UAAH,GAAgB,CAAnE;AACD;AACF;;AAED,UAAIpB,MAAM,IAAIe,YAAd,EAA4B;AAC1BC,QAAAA,cAAc,IAAIpB,WAAlB;AACAqB,QAAAA,eAAe,GAAGD,cAAlB;AACAhB,QAAAA,MAAM,IAAIe,YAAV;AACD,OAJD,MAIO;AACLE,QAAAA,eAAe,IAAIjB,MAAnB;AACA;AACD;AACF,KA9BD,QA+BEA,MAAM,GAAG,CAAT,IACAgB,cAAc,GAAG,KAAK7C,iCAhCxB;;AAmCA,SACE+C,IAAI,GAAG,CAAP,EAAUhB,WAAW,GAAGiB,YAD1B,EAEED,IAAI,GAAG,KAAK9C,kCAFd,EAGE8B,WAAW,IAAIQ,yBAHjB,EAIE;AACAV,MAAAA,MAAM,GAAGL,aAAa,GAAGkB,sBAAsB,CAACK,IAAI,GAAGtB,WAAR,CAAzB,GAAgD,CAAtE;AACAwB,MAAAA,UAAU,GAAGzB,aAAa,GACtBK,MAAM,GACJ,IAAIA,MADA,GAEJ,CAHoB,GAItBQ,kBAJJ;AAKAV,MAAAA,YAAY,CAACI,WAAD,CAAZ,GAA4BS,MAAM,CAACO,IAAI,EAAL,CAAN,GAAiBE,UAA7C;AACAtB,MAAAA,YAAY,CAAC,EAAEI,WAAH,CAAZ,GAA8BS,MAAM,CAACO,IAAI,EAAL,CAAN,GAAiBE,UAA/C;AACAtB,MAAAA,YAAY,CAAC,EAAEI,WAAH,CAAZ,GAA8BS,MAAM,CAACO,IAAI,EAAL,CAAN,GAAiBE,UAA/C;AACA,UAAIzB,aAAJ,EACEG,YAAY,CAAC,EAAEI,WAAH,CAAZ,GAA8BS,MAAM,CAACO,IAAI,EAAL,CAAN,GAAiBV,kBAA/C;AACH;;AAEDW,IAAAA,YAAY,IAAIvB,WAAhB;AACD,GAnED,QAmESuB,YAAY,GAAG,KAAKjD,+BAnE7B;;AAqEA,SAAO4B,YAAP;AACD,CA/FD;;AAiGAzC,MAAM,CAACmB,SAAP,CAAiBkD,wBAAjB,GAA4C,UAAShC,MAAT,EAAiBC,aAAjB,EAAgC;AAC1E,MAAME,WAAW,GAAG,KAAKT,qBAAzB;AACA,MAAMoB,kBAAkB,GAAG,IAAIX,WAA/B;AACA,MAAMc,MAAM,GAAG,KAAKgB,qBAApB;AACA,MAAM7B,YAAY,GAAG,KAAK8B,YAA1B;AACA,MAAMf,sBAAsB,GAAG,KAAKgB,sCAApC;AAEA,MAAI7B,MAAM,GAAG,CAAb;AACA,MAAIe,YAAY,GAAG,CAAnB;AACA,MAAIC,cAAc,GAAG,CAArB;AACA,MAAIC,eAAe,GAAG,CAAtB;AACA,MAAIf,WAAW,GAAG,CAAlB;AACA,MAAIiB,YAAY,GAAG,CAAnB;AACA,MAAIW,KAAK,GAAG,CAAZ;AACA,MAAIV,UAAU,GAAG,CAAjB;AACA,MAAIC,CAAC,GAAG,CAAR;AACA,MAAIC,CAAC,GAAG,CAAR;AACA,MAAIC,CAAC,GAAG,CAAR;AACA,MAAIC,CAAC,GAAG,CAAR;;AAEA,KAAG;AACD,SACEtB,WAAW,GAAG,CADhB,EAEEA,WAAW,GAAG,KAAKhC,+BAFrB,GAIE;AACAyC,MAAAA,MAAM,CAACT,WAAW,EAAZ,CAAN,GAAwB,CAAxB;AACAS,MAAAA,MAAM,CAACT,WAAW,EAAZ,CAAN,GAAwB,CAAxB;AACAS,MAAAA,MAAM,CAACT,WAAW,EAAZ,CAAN,GAAwB,CAAxB;;AAEA,UAAIP,aAAJ,EAAmB;AACjBgB,QAAAA,MAAM,CAACT,WAAW,EAAZ,CAAN,GAAwB,CAAxB;AACAW,QAAAA,sBAAsB,CAACX,WAAW,GAAG,CAAd,GAAkB,CAAnB,CAAtB,GAA8C,CAA9C;AACD;AACF;;AAEDF,IAAAA,MAAM,GAAGH,WAAT;;AAEA,OAAG;AACDkB,MAAAA,YAAY,GAAG,IAAIC,cAAJ,GAAqBC,eAApC;AACAG,MAAAA,UAAU,GAAGvD,IAAI,CAAC4D,GAAL,CAASzB,MAAT,EAAiBe,YAAjB,CAAb;AACAe,MAAAA,KAAK,GAAGd,cAAR;;AAEA,WACEd,WAAW,GAAG,CADhB,EAEEA,WAAW,GAAG,KAAKhC,+BAFrB,GAIE;AACAmD,QAAAA,CAAC,GAAG3B,MAAM,CAACoC,KAAK,EAAN,CAAV;AACAR,QAAAA,CAAC,GAAG5B,MAAM,CAACoC,KAAK,EAAN,CAAV;AACAP,QAAAA,CAAC,GAAG7B,MAAM,CAACoC,KAAK,EAAN,CAAV;AACAN,QAAAA,CAAC,GAAG7B,aAAa,GAAGD,MAAM,CAACoC,KAAK,EAAN,CAAT,GAAqB,GAAtC,CAJA,CAKA;;AACAnB,QAAAA,MAAM,CAACT,WAAW,EAAZ,CAAN,IAAyB,CAACsB,CAAC,GAAGH,CAAH,GAAO,CAAT,IAAcD,UAAvC;AACAT,QAAAA,MAAM,CAACT,WAAW,EAAZ,CAAN,IAAyB,CAACsB,CAAC,GAAGF,CAAH,GAAO,CAAT,IAAcF,UAAvC;AACAT,QAAAA,MAAM,CAACT,WAAW,EAAZ,CAAN,IAAyB,CAACsB,CAAC,GAAGD,CAAH,GAAO,CAAT,IAAcH,UAAvC;;AAEA,YAAIzB,aAAJ,EAAmB;AACjBgB,UAAAA,MAAM,CAACT,WAAW,EAAZ,CAAN,IAAyBsB,CAAC,GAAGJ,UAA7B;AACAP,UAAAA,sBAAsB,CAACX,WAAW,GAAG,CAAd,GAAkB,CAAnB,CAAtB,IAA+CsB,CAAC,GAAGJ,UAAH,GAAgB,CAAhE;AACD;AACF;;AAED,UAAIpB,MAAM,IAAIe,YAAd,EAA4B;AAC1BC,QAAAA,cAAc,GAAGc,KAAjB;AACAb,QAAAA,eAAe,GAAGD,cAAlB;AACAhB,QAAAA,MAAM,IAAIe,YAAV;AACD,OAJD,MAIO;AACLE,QAAAA,eAAe,IAAIjB,MAAnB;AACA;AACD;AACF,KAjCD,QAiCSA,MAAM,GAAG,CAAT,IAAcgB,cAAc,GAAG,KAAK3C,mBAjC7C;;AAmCA,SACE6B,WAAW,GAAG,CADhB,EAEEA,WAAW,GAAG,KAAKhC,+BAFrB,GAIE;AACA8B,MAAAA,MAAM,GAAGL,aAAa,GAAGkB,sBAAsB,CAACX,WAAW,GAAG,CAAf,CAAzB,GAA6C,CAAnE;AACAkB,MAAAA,UAAU,GAAGzB,aAAa,GACtBK,MAAM,GACJ,IAAIA,MADA,GAEJ,CAHoB,GAItBQ,kBAJJ;AAKAV,MAAAA,YAAY,CAACqB,YAAY,EAAb,CAAZ,GAA+BtD,IAAI,CAACkE,KAAL,CAC7BpB,MAAM,CAACT,WAAW,EAAZ,CAAN,GAAwBkB,UADK,CAA/B;AAGAtB,MAAAA,YAAY,CAACqB,YAAY,EAAb,CAAZ,GAA+BtD,IAAI,CAACkE,KAAL,CAC7BpB,MAAM,CAACT,WAAW,EAAZ,CAAN,GAAwBkB,UADK,CAA/B;AAGAtB,MAAAA,YAAY,CAACqB,YAAY,EAAb,CAAZ,GAA+BtD,IAAI,CAACkE,KAAL,CAC7BpB,MAAM,CAACT,WAAW,EAAZ,CAAN,GAAwBkB,UADK,CAA/B;;AAIA,UAAIzB,aAAJ,EAAmB;AACjBG,QAAAA,YAAY,CAACqB,YAAY,EAAb,CAAZ,GAA+BtD,IAAI,CAACkE,KAAL,CAC7BpB,MAAM,CAACT,WAAW,EAAZ,CAAN,GAAwBM,kBADK,CAA/B;AAGD;AACF;AACF,GAhFD,QAgFSW,YAAY,GAAG,KAAK7C,eAhF7B;;AAkFA,SAAOwB,YAAP;AACD,CAvGD;;AAyGAzC,MAAM,CAACmB,SAAP,CAAiBQ,0BAAjB,GAA8C,UAASU,MAAT,EAAiB;AAC7D,SAAO,KAAKD,mCAAL,CAAyCC,MAAzC,EAAiD,KAAjD,CAAP;AACD,CAFD;;AAIArC,MAAM,CAACmB,SAAP,CAAiBO,2BAAjB,GAA+C,UAASW,MAAT,EAAiB;AAC9D,SAAO,KAAKD,mCAAL,CAAyCC,MAAzC,EAAiD,IAAjD,CAAP;AACD,CAFD;;AAIArC,MAAM,CAACmB,SAAP,CAAiBU,cAAjB,GAAkC,UAASQ,MAAT,EAAiB;AACjD,SAAO,KAAKa,uBAAL,CAA6Bb,MAA7B,EAAqC,KAArC,CAAP;AACD,CAFD;;AAIArC,MAAM,CAACmB,SAAP,CAAiBS,eAAjB,GAAmC,UAASS,MAAT,EAAiB;AAClD,SAAO,KAAKa,uBAAL,CAA6Bb,MAA7B,EAAqC,IAArC,CAAP;AACD,CAFD;;AAIArC,MAAM,CAACmB,SAAP,CAAiBc,wBAAjB,GAA4C,UAASI,MAAT,EAAiB;AAC3D,MAAMG,WAAW,GAAG,KAAKT,qBAAzB;AACA,MAAMU,YAAY,GAAG,KAAK8B,YAA1B;AAEA,MAAI5B,MAAM,GAAG,CAAb;AACA,MAAIC,WAAW,GAAG,CAAlB;AACA,MAAIC,WAAW,GAAG,CAAlB;AACA,MAAI8B,sBAAsB,GAAG,CAA7B;AACA,MAAIC,uBAAuB,GAAG,CAA9B;AACA,MAAI9B,WAAW,GAAG,CAAlB;AACA,MAAIC,YAAY,GAAG,CAAnB;AACA,MAAI8B,iCAAJ,CAX2D,CAa3D;;AACA,SAAOlC,MAAM,GAAG,IAAI,CAApB,EAAuBA,MAAM,IAAIH,WAAjC,EAA8C;AAC5C,SACEK,WAAW,GAAG,CADhB,EAEEA,WAAW,GAAG,KAAKhC,+BAFrB,GAIE;AACA4B,MAAAA,YAAY,CAACG,WAAW,EAAZ,CAAZ,GAA8BpC,IAAI,CAACkE,KAAL,CAAWrC,MAAM,CAACQ,WAAW,EAAZ,CAAjB,CAA9B;AACD;AACF,GAtB0D,CAwB3D;;;AACAF,EAAAA,MAAM,IAAI,IAAI,CAAd;;AAEA,OACEkC,iCAAiC,GAAG,KAAK3E,cAAL,GAAsB,CAD5D,EAEEyC,MAAM,GAAGkC,iCAFX,EAGElC,MAAM,IAAIH,WAHZ,EAIE;AACA;AACAO,IAAAA,YAAY,GAAGJ,MAAM,GAAG,CAAxB;AACAG,IAAAA,WAAW,GAAG,IAAIC,YAAlB,CAHA,CAIA;;AACA4B,IAAAA,sBAAsB,GACpBnE,IAAI,CAACE,KAAL,CAAWiC,MAAX,IAAqB,KAAK9B,+BAD5B;AAEA+D,IAAAA,uBAAuB,GACrBD,sBAAsB,GAAG,KAAK9D,+BADhC;;AAEA,SACEgC,WAAW,GAAG,CADhB,EAEEA,WAAW,GAAG,KAAKhC,+BAFrB,EAGE,EAAEgC,WAHJ,EAIE;AACAJ,MAAAA,YAAY,CAACG,WAAW,EAAZ,CAAZ,GAA8BpC,IAAI,CAACkE,KAAL,CAC5BrC,MAAM,CAACsC,sBAAsB,EAAvB,CAAN,GAAmC7B,WAAnC,GACET,MAAM,CAACuC,uBAAuB,EAAxB,CAAN,GAAoC7B,YAFV,CAA9B;AAID;AACF,GAlD0D,CAoD3D;;;AACA,SAAOH,WAAW,GAAG,KAAK3B,eAA1B,EAA2C;AACzC,SACE4B,WAAW,GAAG,CAAd,EACE8B,sBAAsB,GACpBE,iCAAiC,GACjC,KAAKhE,+BAJX,EAKEgC,WAAW,GAAG,KAAKhC,+BALrB,EAME,EAAEgC,WANJ,EAOE;AACAJ,MAAAA,YAAY,CAACG,WAAW,EAAZ,CAAZ,GAA8BpC,IAAI,CAACkE,KAAL,CAC5BrC,MAAM,CAACsC,sBAAsB,EAAvB,CADsB,CAA9B;AAGD;AACF;;AAED,SAAOlC,YAAP;AACD,CArED;;AAuEAzC,MAAM,CAACmB,SAAP,CAAiBgB,eAAjB,GAAmC,UAASE,MAAT,EAAiB;AAClD,SAAO,KAAKgC,wBAAL,CAA8BhC,MAA9B,EAAsC,KAAtC,CAAP;AACD,CAFD;;AAIArC,MAAM,CAACmB,SAAP,CAAiBe,gBAAjB,GAAoC,UAASG,MAAT,EAAiB;AACnD,SAAO,KAAKgC,wBAAL,CAA8BhC,MAA9B,EAAsC,IAAtC,CAAP;AACD,CAFD;;AAIArC,MAAM,CAACmB,SAAP,CAAiB2D,MAAjB,GAA0B,UAASzC,MAAT,EAAiB;AACzC,OAAK9B,cAAL,CAAoB,KAAKuB,YAAL,CAAkB,KAAKR,WAAL,CAAiBe,MAAjB,CAAlB,CAApB;AACD,CAFD;;AAIArC,MAAM,CAACmB,SAAP,CAAiBI,aAAjB,GAAiC,UAASc,MAAT,EAAiB;AAChD;AACA,SAAOA,MAAP;AACD,CAHD;;AAKArC,MAAM,CAACmB,SAAP,CAAiBM,0BAAjB,GAA8C,UAASsD,YAAT,EAAuB;AACnE;AACA,OAAKrC,WAAL,GAAmB,KAAKsC,mBAAL,CAAyB,KAAKhE,mBAA9B,CAAnB;;AAEA,MAAI,CAAC+D,YAAL,EAAmB;AACjB,SAAKxB,oBAAL,GAA4B,KAAKyB,mBAAL,CAC1B,KAAKjE,kCADqB,CAA5B;;AAIA,QAAI,KAAKJ,aAAL,GAAqB,CAAzB,EAA4B;AAC1B,WAAK8C,qCAAL,GAA6C,KAAKwB,qBAAL,CAC3C,KAAK/E,cADsC,CAA7C;AAGD;AACF;AACF,CAfD;;AAiBAF,MAAM,CAACmB,SAAP,CAAiBa,2BAAjB,GAA+C,UAAS+C,YAAT,EAAuB;AACpE;AACA,OAAKR,YAAL,GAAoB,KAAKW,mBAAL,CAAyB,KAAKjE,eAA9B,CAApB;;AAEA,MAAI,CAAC8D,YAAL,EAAmB;AACjB,SAAKT,qBAAL,GAA6B,KAAKU,mBAAL,CAC3B,KAAKnE,+BADsB,CAA7B;;AAIA,QAAI,KAAKF,aAAL,GAAqB,CAAzB,EAA4B;AAC1B,WAAK6D,sCAAL,GAA8C,KAAKS,qBAAL,CAC5C,KAAK9E,WADuC,CAA9C;AAGD;AACF;AACF,CAfD;;AAiBAH,MAAM,CAACmB,SAAP,CAAiB6D,mBAAjB,GAAuC,UAASG,YAAT,EAAuB;AAC5D;AACA,MAAI;AACF,WAAO,IAAIC,YAAJ,CAAiBD,YAAjB,CAAP;AACD,GAFD,CAEE,OAAOE,KAAP,EAAc;AACd,WAAO,EAAP;AACD;AACF,CAPD;;AASArF,MAAM,CAACmB,SAAP,CAAiB8D,qBAAjB,GAAyC,UAASE,YAAT,EAAuB;AAC9D;AACA,MAAI;AACF,WAAO,IAAIG,YAAJ,CAAiBH,YAAjB,CAAP;AACD,GAFD,CAEE,OAAOE,KAAP,EAAc;AACd,WAAO,EAAP;AACD;AACF,CAPD;;AASArF,MAAM,CAACmB,SAAP,CAAiB+D,mBAAjB,GAAuC,UAASC,YAAT,EAAuB;AAC5D;AACA,MAAI;AACF,WAAO,IAAII,UAAJ,CAAeJ,YAAf,CAAP;AACD,GAFD,CAEE,OAAOE,KAAP,EAAc;AACd,WAAO,EAAP;AACD;AACF,CAPD;;AASAG,MAAM,CAACC,OAAP,GAAiBzF,MAAjB", "sourcesContent": ["// JavaScript Image Resizer (c) 2012 - <PERSON>\n// Released to public domain 29 July 2013: https://github.com/grantgalitz/JS-Image-Resizer/issues/4\n\nfunction Resize(\n  widthOriginal,\n  heightOriginal,\n  targetWidth,\n  targetHeight,\n  blendAlpha,\n  interpolationPass,\n  resizeCallback\n) {\n  this.widthOriginal = Math.abs(Math.floor(widthOriginal) || 0);\n  this.heightOriginal = Math.abs(Math.floor(heightOriginal) || 0);\n  this.targetWidth = Math.abs(Math.floor(targetWidth) || 0);\n  this.targetHeight = Math.abs(Math.floor(targetHeight) || 0);\n  this.colorChannels = blendAlpha ? 4 : 3;\n  this.interpolationPass = Boolean(interpolationPass);\n  this.resizeCallback =\n    typeof resizeCallback === 'function' ? resizeCallback : function() {};\n\n  this.targetWidthMultipliedByChannels = this.targetWidth * this.colorChannels;\n  this.originalWidthMultipliedByChannels =\n    this.widthOriginal * this.colorChannels;\n  this.originalHeightMultipliedByChannels =\n    this.heightOriginal * this.colorChannels;\n  this.widthPassResultSize =\n    this.targetWidthMultipliedByChannels * this.heightOriginal;\n  this.finalResultSize =\n    this.targetWidthMultipliedByChannels * this.targetHeight;\n  this.initialize();\n}\n\nResize.prototype.initialize = function() {\n  // Perform some checks:\n  if (\n    this.widthOriginal > 0 &&\n    this.heightOriginal > 0 &&\n    this.targetWidth > 0 &&\n    this.targetHeight > 0\n  ) {\n    this.configurePasses();\n  } else {\n    throw new Error('Invalid settings specified for the resizer.');\n  }\n};\n\nResize.prototype.configurePasses = function() {\n  if (this.widthOriginal === this.targetWidth) {\n    // Bypass the width resizer pass:\n    this.resizeWidth = this.bypassResizer;\n  } else {\n    // Setup the width resizer pass:\n    this.ratioWeightWidthPass = this.widthOriginal / this.targetWidth;\n    if (this.ratioWeightWidthPass < 1 && this.interpolationPass) {\n      this.initializeFirstPassBuffers(true);\n      this.resizeWidth =\n        this.colorChannels === 4\n          ? this.resizeWidthInterpolatedRGBA\n          : this.resizeWidthInterpolatedRGB;\n    } else {\n      this.initializeFirstPassBuffers(false);\n      this.resizeWidth =\n        this.colorChannels === 4 ? this.resizeWidthRGBA : this.resizeWidthRGB;\n    }\n  }\n\n  if (this.heightOriginal === this.targetHeight) {\n    // Bypass the height resizer pass:\n    this.resizeHeight = this.bypassResizer;\n  } else {\n    // Setup the height resizer pass:\n    this.ratioWeightHeightPass = this.heightOriginal / this.targetHeight;\n    if (this.ratioWeightHeightPass < 1 && this.interpolationPass) {\n      this.initializeSecondPassBuffers(true);\n      this.resizeHeight = this.resizeHeightInterpolated;\n    } else {\n      this.initializeSecondPassBuffers(false);\n      this.resizeHeight =\n        this.colorChannels === 4 ? this.resizeHeightRGBA : this.resizeHeightRGB;\n    }\n  }\n};\n\nResize.prototype._resizeWidthInterpolatedRGBChannels = function(\n  buffer,\n  fourthChannel\n) {\n  const channelsNum = fourthChannel ? 4 : 3;\n  const ratioWeight = this.ratioWeightWidthPass;\n  const outputBuffer = this.widthBuffer;\n\n  let weight = 0;\n  let finalOffset = 0;\n  let pixelOffset = 0;\n  let firstWeight = 0;\n  let secondWeight = 0;\n  let targetPosition;\n\n  // Handle for only one interpolation input being valid for start calculation:\n  for (\n    targetPosition = 0;\n    weight < 1 / 3;\n    targetPosition += channelsNum, weight += ratioWeight\n  ) {\n    for (\n      finalOffset = targetPosition, pixelOffset = 0;\n      finalOffset < this.widthPassResultSize;\n      pixelOffset += this.originalWidthMultipliedByChannels,\n        finalOffset += this.targetWidthMultipliedByChannels\n    ) {\n      outputBuffer[finalOffset] = buffer[pixelOffset];\n      outputBuffer[finalOffset + 1] = buffer[pixelOffset + 1];\n      outputBuffer[finalOffset + 2] = buffer[pixelOffset + 2];\n      if (fourthChannel)\n        outputBuffer[finalOffset + 3] = buffer[pixelOffset + 3];\n    }\n  }\n\n  // Adjust for overshoot of the last pass's counter:\n  weight -= 1 / 3;\n  let interpolationWidthSourceReadStop;\n\n  for (\n    interpolationWidthSourceReadStop = this.widthOriginal - 1;\n    weight < interpolationWidthSourceReadStop;\n    targetPosition += channelsNum, weight += ratioWeight\n  ) {\n    // Calculate weightings:\n    secondWeight = weight % 1;\n    firstWeight = 1 - secondWeight;\n    // Interpolate:\n    for (\n      finalOffset = targetPosition,\n        pixelOffset = Math.floor(weight) * channelsNum;\n      finalOffset < this.widthPassResultSize;\n      pixelOffset += this.originalWidthMultipliedByChannels,\n        finalOffset += this.targetWidthMultipliedByChannels\n    ) {\n      outputBuffer[finalOffset + 0] =\n        buffer[pixelOffset + 0] * firstWeight +\n        buffer[pixelOffset + channelsNum + 0] * secondWeight;\n      outputBuffer[finalOffset + 1] =\n        buffer[pixelOffset + 1] * firstWeight +\n        buffer[pixelOffset + channelsNum + 1] * secondWeight;\n      outputBuffer[finalOffset + 2] =\n        buffer[pixelOffset + 2] * firstWeight +\n        buffer[pixelOffset + channelsNum + 2] * secondWeight;\n      if (fourthChannel)\n        outputBuffer[finalOffset + 3] =\n          buffer[pixelOffset + 3] * firstWeight +\n          buffer[pixelOffset + channelsNum + 3] * secondWeight;\n    }\n  }\n\n  // Handle for only one interpolation input being valid for end calculation:\n  for (\n    interpolationWidthSourceReadStop =\n      this.originalWidthMultipliedByChannels - channelsNum;\n    targetPosition < this.targetWidthMultipliedByChannels;\n    targetPosition += channelsNum\n  ) {\n    for (\n      finalOffset = targetPosition,\n        pixelOffset = interpolationWidthSourceReadStop;\n      finalOffset < this.widthPassResultSize;\n      pixelOffset += this.originalWidthMultipliedByChannels,\n        finalOffset += this.targetWidthMultipliedByChannels\n    ) {\n      outputBuffer[finalOffset] = buffer[pixelOffset];\n      outputBuffer[finalOffset + 1] = buffer[pixelOffset + 1];\n      outputBuffer[finalOffset + 2] = buffer[pixelOffset + 2];\n      if (fourthChannel)\n        outputBuffer[finalOffset + 3] = buffer[pixelOffset + 3];\n    }\n  }\n\n  return outputBuffer;\n};\n\nResize.prototype._resizeWidthRGBChannels = function(buffer, fourthChannel) {\n  const channelsNum = fourthChannel ? 4 : 3;\n  const ratioWeight = this.ratioWeightWidthPass;\n  const ratioWeightDivisor = 1 / ratioWeight;\n  const nextLineOffsetOriginalWidth =\n    this.originalWidthMultipliedByChannels - channelsNum + 1;\n  const nextLineOffsetTargetWidth =\n    this.targetWidthMultipliedByChannels - channelsNum + 1;\n  const output = this.outputWidthWorkBench;\n  const outputBuffer = this.widthBuffer;\n  const trustworthyColorsCount = this.outputWidthWorkBenchOpaquePixelsCount;\n\n  let weight = 0;\n  let amountToNext = 0;\n  let actualPosition = 0;\n  let currentPosition = 0;\n  let line = 0;\n  let pixelOffset = 0;\n  let outputOffset = 0;\n  let multiplier = 1;\n  let r = 0;\n  let g = 0;\n  let b = 0;\n  let a = 0;\n\n  do {\n    for (line = 0; line < this.originalHeightMultipliedByChannels; ) {\n      output[line++] = 0;\n      output[line++] = 0;\n      output[line++] = 0;\n      if (fourthChannel) {\n        output[line++] = 0;\n        trustworthyColorsCount[line / channelsNum - 1] = 0;\n      }\n    }\n\n    weight = ratioWeight;\n\n    do {\n      amountToNext = 1 + actualPosition - currentPosition;\n      multiplier = Math.min(weight, amountToNext);\n      for (\n        line = 0, pixelOffset = actualPosition;\n        line < this.originalHeightMultipliedByChannels;\n        pixelOffset += nextLineOffsetOriginalWidth\n      ) {\n        r = buffer[pixelOffset];\n        g = buffer[++pixelOffset];\n        b = buffer[++pixelOffset];\n        a = fourthChannel ? buffer[++pixelOffset] : 255;\n        // Ignore RGB values if pixel is completely transparent\n        output[line++] += (a ? r : 0) * multiplier;\n        output[line++] += (a ? g : 0) * multiplier;\n        output[line++] += (a ? b : 0) * multiplier;\n        if (fourthChannel) {\n          output[line++] += a * multiplier;\n          trustworthyColorsCount[line / channelsNum - 1] += a ? multiplier : 0;\n        }\n      }\n\n      if (weight >= amountToNext) {\n        actualPosition += channelsNum;\n        currentPosition = actualPosition;\n        weight -= amountToNext;\n      } else {\n        currentPosition += weight;\n        break;\n      }\n    } while (\n      weight > 0 &&\n      actualPosition < this.originalWidthMultipliedByChannels\n    );\n\n    for (\n      line = 0, pixelOffset = outputOffset;\n      line < this.originalHeightMultipliedByChannels;\n      pixelOffset += nextLineOffsetTargetWidth\n    ) {\n      weight = fourthChannel ? trustworthyColorsCount[line / channelsNum] : 1;\n      multiplier = fourthChannel\n        ? weight\n          ? 1 / weight\n          : 0\n        : ratioWeightDivisor;\n      outputBuffer[pixelOffset] = output[line++] * multiplier;\n      outputBuffer[++pixelOffset] = output[line++] * multiplier;\n      outputBuffer[++pixelOffset] = output[line++] * multiplier;\n      if (fourthChannel)\n        outputBuffer[++pixelOffset] = output[line++] * ratioWeightDivisor;\n    }\n\n    outputOffset += channelsNum;\n  } while (outputOffset < this.targetWidthMultipliedByChannels);\n\n  return outputBuffer;\n};\n\nResize.prototype._resizeHeightRGBChannels = function(buffer, fourthChannel) {\n  const ratioWeight = this.ratioWeightHeightPass;\n  const ratioWeightDivisor = 1 / ratioWeight;\n  const output = this.outputHeightWorkBench;\n  const outputBuffer = this.heightBuffer;\n  const trustworthyColorsCount = this.outputHeightWorkBenchOpaquePixelsCount;\n\n  let weight = 0;\n  let amountToNext = 0;\n  let actualPosition = 0;\n  let currentPosition = 0;\n  let pixelOffset = 0;\n  let outputOffset = 0;\n  let caret = 0;\n  let multiplier = 1;\n  let r = 0;\n  let g = 0;\n  let b = 0;\n  let a = 0;\n\n  do {\n    for (\n      pixelOffset = 0;\n      pixelOffset < this.targetWidthMultipliedByChannels;\n\n    ) {\n      output[pixelOffset++] = 0;\n      output[pixelOffset++] = 0;\n      output[pixelOffset++] = 0;\n\n      if (fourthChannel) {\n        output[pixelOffset++] = 0;\n        trustworthyColorsCount[pixelOffset / 4 - 1] = 0;\n      }\n    }\n\n    weight = ratioWeight;\n\n    do {\n      amountToNext = 1 + actualPosition - currentPosition;\n      multiplier = Math.min(weight, amountToNext);\n      caret = actualPosition;\n\n      for (\n        pixelOffset = 0;\n        pixelOffset < this.targetWidthMultipliedByChannels;\n\n      ) {\n        r = buffer[caret++];\n        g = buffer[caret++];\n        b = buffer[caret++];\n        a = fourthChannel ? buffer[caret++] : 255;\n        // Ignore RGB values if pixel is completely transparent\n        output[pixelOffset++] += (a ? r : 0) * multiplier;\n        output[pixelOffset++] += (a ? g : 0) * multiplier;\n        output[pixelOffset++] += (a ? b : 0) * multiplier;\n\n        if (fourthChannel) {\n          output[pixelOffset++] += a * multiplier;\n          trustworthyColorsCount[pixelOffset / 4 - 1] += a ? multiplier : 0;\n        }\n      }\n\n      if (weight >= amountToNext) {\n        actualPosition = caret;\n        currentPosition = actualPosition;\n        weight -= amountToNext;\n      } else {\n        currentPosition += weight;\n        break;\n      }\n    } while (weight > 0 && actualPosition < this.widthPassResultSize);\n\n    for (\n      pixelOffset = 0;\n      pixelOffset < this.targetWidthMultipliedByChannels;\n\n    ) {\n      weight = fourthChannel ? trustworthyColorsCount[pixelOffset / 4] : 1;\n      multiplier = fourthChannel\n        ? weight\n          ? 1 / weight\n          : 0\n        : ratioWeightDivisor;\n      outputBuffer[outputOffset++] = Math.round(\n        output[pixelOffset++] * multiplier\n      );\n      outputBuffer[outputOffset++] = Math.round(\n        output[pixelOffset++] * multiplier\n      );\n      outputBuffer[outputOffset++] = Math.round(\n        output[pixelOffset++] * multiplier\n      );\n\n      if (fourthChannel) {\n        outputBuffer[outputOffset++] = Math.round(\n          output[pixelOffset++] * ratioWeightDivisor\n        );\n      }\n    }\n  } while (outputOffset < this.finalResultSize);\n\n  return outputBuffer;\n};\n\nResize.prototype.resizeWidthInterpolatedRGB = function(buffer) {\n  return this._resizeWidthInterpolatedRGBChannels(buffer, false);\n};\n\nResize.prototype.resizeWidthInterpolatedRGBA = function(buffer) {\n  return this._resizeWidthInterpolatedRGBChannels(buffer, true);\n};\n\nResize.prototype.resizeWidthRGB = function(buffer) {\n  return this._resizeWidthRGBChannels(buffer, false);\n};\n\nResize.prototype.resizeWidthRGBA = function(buffer) {\n  return this._resizeWidthRGBChannels(buffer, true);\n};\n\nResize.prototype.resizeHeightInterpolated = function(buffer) {\n  const ratioWeight = this.ratioWeightHeightPass;\n  const outputBuffer = this.heightBuffer;\n\n  let weight = 0;\n  let finalOffset = 0;\n  let pixelOffset = 0;\n  let pixelOffsetAccumulated = 0;\n  let pixelOffsetAccumulated2 = 0;\n  let firstWeight = 0;\n  let secondWeight = 0;\n  let interpolationHeightSourceReadStop;\n\n  // Handle for only one interpolation input being valid for start calculation:\n  for (; weight < 1 / 3; weight += ratioWeight) {\n    for (\n      pixelOffset = 0;\n      pixelOffset < this.targetWidthMultipliedByChannels;\n\n    ) {\n      outputBuffer[finalOffset++] = Math.round(buffer[pixelOffset++]);\n    }\n  }\n\n  // Adjust for overshoot of the last pass's counter:\n  weight -= 1 / 3;\n\n  for (\n    interpolationHeightSourceReadStop = this.heightOriginal - 1;\n    weight < interpolationHeightSourceReadStop;\n    weight += ratioWeight\n  ) {\n    // Calculate weightings:\n    secondWeight = weight % 1;\n    firstWeight = 1 - secondWeight;\n    // Interpolate:\n    pixelOffsetAccumulated =\n      Math.floor(weight) * this.targetWidthMultipliedByChannels;\n    pixelOffsetAccumulated2 =\n      pixelOffsetAccumulated + this.targetWidthMultipliedByChannels;\n    for (\n      pixelOffset = 0;\n      pixelOffset < this.targetWidthMultipliedByChannels;\n      ++pixelOffset\n    ) {\n      outputBuffer[finalOffset++] = Math.round(\n        buffer[pixelOffsetAccumulated++] * firstWeight +\n          buffer[pixelOffsetAccumulated2++] * secondWeight\n      );\n    }\n  }\n\n  // Handle for only one interpolation input being valid for end calculation:\n  while (finalOffset < this.finalResultSize) {\n    for (\n      pixelOffset = 0,\n        pixelOffsetAccumulated =\n          interpolationHeightSourceReadStop *\n          this.targetWidthMultipliedByChannels;\n      pixelOffset < this.targetWidthMultipliedByChannels;\n      ++pixelOffset\n    ) {\n      outputBuffer[finalOffset++] = Math.round(\n        buffer[pixelOffsetAccumulated++]\n      );\n    }\n  }\n\n  return outputBuffer;\n};\n\nResize.prototype.resizeHeightRGB = function(buffer) {\n  return this._resizeHeightRGBChannels(buffer, false);\n};\n\nResize.prototype.resizeHeightRGBA = function(buffer) {\n  return this._resizeHeightRGBChannels(buffer, true);\n};\n\nResize.prototype.resize = function(buffer) {\n  this.resizeCallback(this.resizeHeight(this.resizeWidth(buffer)));\n};\n\nResize.prototype.bypassResizer = function(buffer) {\n  // Just return the buffer passed:\n  return buffer;\n};\n\nResize.prototype.initializeFirstPassBuffers = function(BILINEARAlgo) {\n  // Initialize the internal width pass buffers:\n  this.widthBuffer = this.generateFloatBuffer(this.widthPassResultSize);\n\n  if (!BILINEARAlgo) {\n    this.outputWidthWorkBench = this.generateFloatBuffer(\n      this.originalHeightMultipliedByChannels\n    );\n\n    if (this.colorChannels > 3) {\n      this.outputWidthWorkBenchOpaquePixelsCount = this.generateFloat64Buffer(\n        this.heightOriginal\n      );\n    }\n  }\n};\n\nResize.prototype.initializeSecondPassBuffers = function(BILINEARAlgo) {\n  // Initialize the internal height pass buffers:\n  this.heightBuffer = this.generateUint8Buffer(this.finalResultSize);\n\n  if (!BILINEARAlgo) {\n    this.outputHeightWorkBench = this.generateFloatBuffer(\n      this.targetWidthMultipliedByChannels\n    );\n\n    if (this.colorChannels > 3) {\n      this.outputHeightWorkBenchOpaquePixelsCount = this.generateFloat64Buffer(\n        this.targetWidth\n      );\n    }\n  }\n};\n\nResize.prototype.generateFloatBuffer = function(bufferLength) {\n  // Generate a float32 typed array buffer:\n  try {\n    return new Float32Array(bufferLength);\n  } catch (error) {\n    return [];\n  }\n};\n\nResize.prototype.generateFloat64Buffer = function(bufferLength) {\n  // Generate a float64 typed array buffer:\n  try {\n    return new Float64Array(bufferLength);\n  } catch (error) {\n    return [];\n  }\n};\n\nResize.prototype.generateUint8Buffer = function(bufferLength) {\n  // Generate a uint8 typed array buffer:\n  try {\n    return new Uint8Array(bufferLength);\n  } catch (error) {\n    return [];\n  }\n};\n\nmodule.exports = Resize;\n"], "file": "resize.js"}