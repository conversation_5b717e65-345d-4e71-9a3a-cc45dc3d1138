{"version": 3, "sources": ["../src/index.js"], "names": ["applyKernel", "im", "kernel", "x", "y", "value", "size", "length", "kx", "ky", "idx", "getPixelIndex", "bitmap", "data", "isDef", "v", "greyscale", "cb", "scanQuiet", "width", "height", "grey", "parseInt", "call", "mix", "clr", "clr2", "p", "r", "g", "b", "colorFn", "actions", "Array", "isArray", "throwError", "map", "action", "apply", "params", "toRgb", "colorModifier", "i", "amount", "constructor", "limit255", "for<PERSON>ach", "brightness", "val", "contrast", "factor", "adjust", "Math", "floor", "posterize", "n", "grayscale", "opacity", "f", "sepia", "red", "green", "blue", "fade", "convolution", "edgeHandling", "EDGE_EXTEND", "newData", "<PERSON><PERSON><PERSON>", "from", "kRows", "kCols", "rowEnd", "colEnd", "rowIni", "colIni", "weight", "rSum", "gSum", "bSum", "ri", "gi", "bi", "xi", "yi", "idxi", "row", "col", "opaque", "pixelate", "w", "h", "source", "clone<PERSON>uiet", "xx", "yx", "convolute", "ksize", "color", "colour"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;;AACA;;AAEA,SAASA,WAAT,CAAqBC,EAArB,EAAyBC,MAAzB,EAAiCC,CAAjC,EAAoCC,CAApC,EAAuC;AACrC,MAAMC,KAAK,GAAG,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAAd;AACA,MAAMC,IAAI,GAAG,CAACJ,MAAM,CAACK,MAAP,GAAgB,CAAjB,IAAsB,CAAnC;;AAEA,OAAK,IAAIC,EAAE,GAAG,CAAd,EAAiBA,EAAE,GAAGN,MAAM,CAACK,MAA7B,EAAqCC,EAAE,IAAI,CAA3C,EAA8C;AAC5C,SAAK,IAAIC,EAAE,GAAG,CAAd,EAAiBA,EAAE,GAAGP,MAAM,CAACM,EAAD,CAAN,CAAWD,MAAjC,EAAyCE,EAAE,IAAI,CAA/C,EAAkD;AAChD,UAAMC,GAAG,GAAGT,EAAE,CAACU,aAAH,CAAiBR,CAAC,GAAGK,EAAJ,GAASF,IAA1B,EAAgCF,CAAC,GAAGK,EAAJ,GAASH,IAAzC,CAAZ;AAEAD,MAAAA,KAAK,CAAC,CAAD,CAAL,IAAYJ,EAAE,CAACW,MAAH,CAAUC,IAAV,CAAeH,GAAf,IAAsBR,MAAM,CAACM,EAAD,CAAN,CAAWC,EAAX,CAAlC;AACAJ,MAAAA,KAAK,CAAC,CAAD,CAAL,IAAYJ,EAAE,CAACW,MAAH,CAAUC,IAAV,CAAeH,GAAG,GAAG,CAArB,IAA0BR,MAAM,CAACM,EAAD,CAAN,CAAWC,EAAX,CAAtC;AACAJ,MAAAA,KAAK,CAAC,CAAD,CAAL,IAAYJ,EAAE,CAACW,MAAH,CAAUC,IAAV,CAAeH,GAAG,GAAG,CAArB,IAA0BR,MAAM,CAACM,EAAD,CAAN,CAAWC,EAAX,CAAtC;AACD;AACF;;AAED,SAAOJ,KAAP;AACD;;AAED,IAAMS,KAAK,GAAG,SAARA,KAAQ,CAAAC,CAAC;AAAA,SAAI,OAAOA,CAAP,KAAa,WAAb,IAA4BA,CAAC,KAAK,IAAtC;AAAA,CAAf;;AAEA,SAASC,SAAT,CAAmBC,EAAnB,EAAuB;AACrB,OAAKC,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,KAAKN,MAAL,CAAYO,KAAjC,EAAwC,KAAKP,MAAL,CAAYQ,MAApD,EAA4D,UAC1DjB,CAD0D,EAE1DC,CAF0D,EAG1DM,GAH0D,EAI1D;AACA,QAAMW,IAAI,GAAGC,QAAQ,CACnB,SAAS,KAAKV,MAAL,CAAYC,IAAZ,CAAiBH,GAAjB,CAAT,GACE,SAAS,KAAKE,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,CADX,GAEE,SAAS,KAAKE,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,CAHQ,EAInB,EAJmB,CAArB;AAOA,SAAKE,MAAL,CAAYC,IAAZ,CAAiBH,GAAjB,IAAwBW,IAAxB;AACA,SAAKT,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IAA4BW,IAA5B;AACA,SAAKT,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IAA4BW,IAA5B;AACD,GAfD;;AAiBA,MAAI,0BAAcJ,EAAd,CAAJ,EAAuB;AACrBA,IAAAA,EAAE,CAACM,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,SAAO,IAAP;AACD;;AAED,SAASC,GAAT,CAAaC,GAAb,EAAkBC,IAAlB,EAAgC;AAAA,MAARC,CAAQ,uEAAJ,EAAI;AAC9B,SAAO;AACLC,IAAAA,CAAC,EAAE,CAACF,IAAI,CAACE,CAAL,GAASH,GAAG,CAACG,CAAd,KAAoBD,CAAC,GAAG,GAAxB,IAA+BF,GAAG,CAACG,CADjC;AAELC,IAAAA,CAAC,EAAE,CAACH,IAAI,CAACG,CAAL,GAASJ,GAAG,CAACI,CAAd,KAAoBF,CAAC,GAAG,GAAxB,IAA+BF,GAAG,CAACI,CAFjC;AAGLC,IAAAA,CAAC,EAAE,CAACJ,IAAI,CAACI,CAAL,GAASL,GAAG,CAACK,CAAd,KAAoBH,CAAC,GAAG,GAAxB,IAA+BF,GAAG,CAACK;AAHjC,GAAP;AAKD;;AAED,SAASC,OAAT,CAAiBC,OAAjB,EAA0Bf,EAA1B,EAA8B;AAAA;;AAC5B,MAAI,CAACe,OAAD,IAAY,CAACC,KAAK,CAACC,OAAN,CAAcF,OAAd,CAAjB,EAAyC;AACvC,WAAOG,kBAAWZ,IAAX,CAAgB,IAAhB,EAAsB,0BAAtB,EAAkDN,EAAlD,CAAP;AACD;;AAEDe,EAAAA,OAAO,GAAGA,OAAO,CAACI,GAAR,CAAY,UAAAC,MAAM,EAAI;AAC9B,QAAIA,MAAM,CAACC,KAAP,KAAiB,KAAjB,IAA0BD,MAAM,CAACC,KAAP,KAAiB,KAA/C,EAAsD;AACpDD,MAAAA,MAAM,CAACE,MAAP,CAAc,CAAd,IAAmB,2BAAUF,MAAM,CAACE,MAAP,CAAc,CAAd,CAAV,EAA4BC,KAA5B,EAAnB;AACD;;AAED,WAAOH,MAAP;AACD,GANS,CAAV;AAQA,OAAKnB,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,KAAKN,MAAL,CAAYO,KAAjC,EAAwC,KAAKP,MAAL,CAAYQ,MAApD,EAA4D,UAACjB,CAAD,EAAIC,CAAJ,EAAOM,GAAP,EAAe;AACzE,QAAIe,GAAG,GAAG;AACRG,MAAAA,CAAC,EAAE,KAAI,CAAChB,MAAL,CAAYC,IAAZ,CAAiBH,GAAjB,CADK;AAERmB,MAAAA,CAAC,EAAE,KAAI,CAACjB,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,CAFK;AAGRoB,MAAAA,CAAC,EAAE,KAAI,CAAClB,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB;AAHK,KAAV;;AAMA,QAAM+B,aAAa,GAAG,SAAhBA,aAAgB,CAACC,CAAD,EAAIC,MAAJ;AAAA,aACpB,KAAI,CAACC,WAAL,CAAiBC,QAAjB,CAA0BpB,GAAG,CAACiB,CAAD,CAAH,GAASC,MAAnC,CADoB;AAAA,KAAtB;;AAGAX,IAAAA,OAAO,CAACc,OAAR,CAAgB,UAAAT,MAAM,EAAI;AACxB,UAAIA,MAAM,CAACC,KAAP,KAAiB,KAArB,EAA4B;AAC1Bb,QAAAA,GAAG,GAAGD,GAAG,CAACC,GAAD,EAAMY,MAAM,CAACE,MAAP,CAAc,CAAd,CAAN,EAAwBF,MAAM,CAACE,MAAP,CAAc,CAAd,CAAxB,CAAT;AACD,OAFD,MAEO,IAAIF,MAAM,CAACC,KAAP,KAAiB,MAArB,EAA6B;AAClCb,QAAAA,GAAG,GAAGD,GAAG,CAACC,GAAD,EAAM;AAAEG,UAAAA,CAAC,EAAE,GAAL;AAAUC,UAAAA,CAAC,EAAE,GAAb;AAAkBC,UAAAA,CAAC,EAAE;AAArB,SAAN,EAAkCO,MAAM,CAACE,MAAP,CAAc,CAAd,CAAlC,CAAT;AACD,OAFM,MAEA,IAAIF,MAAM,CAACC,KAAP,KAAiB,OAArB,EAA8B;AACnCb,QAAAA,GAAG,GAAGD,GAAG,CAACC,GAAD,EAAM;AAAEG,UAAAA,CAAC,EAAE,CAAL;AAAQC,UAAAA,CAAC,EAAE,CAAX;AAAcC,UAAAA,CAAC,EAAE;AAAjB,SAAN,EAA4BO,MAAM,CAACE,MAAP,CAAc,CAAd,CAA5B,CAAT;AACD,OAFM,MAEA,IAAIF,MAAM,CAACC,KAAP,KAAiB,KAArB,EAA4B;AACjCb,QAAAA,GAAG,GAAG;AACJG,UAAAA,CAAC,EAAEH,GAAG,CAACG,CAAJ,GAAQS,MAAM,CAACE,MAAP,CAAc,CAAd,EAAiBX,CADxB;AAEJC,UAAAA,CAAC,EAAEJ,GAAG,CAACI,CAAJ,GAAQQ,MAAM,CAACE,MAAP,CAAc,CAAd,EAAiBV,CAFxB;AAGJC,UAAAA,CAAC,EAAEL,GAAG,CAACK,CAAJ,GAAQO,MAAM,CAACE,MAAP,CAAc,CAAd,EAAiBT;AAHxB,SAAN;AAKD,OANM,MAMA,IAAIO,MAAM,CAACC,KAAP,KAAiB,KAArB,EAA4B;AACjCb,QAAAA,GAAG,CAACG,CAAJ,GAAQa,aAAa,CAAC,GAAD,EAAMJ,MAAM,CAACE,MAAP,CAAc,CAAd,CAAN,CAArB;AACD,OAFM,MAEA,IAAIF,MAAM,CAACC,KAAP,KAAiB,OAArB,EAA8B;AACnCb,QAAAA,GAAG,CAACI,CAAJ,GAAQY,aAAa,CAAC,GAAD,EAAMJ,MAAM,CAACE,MAAP,CAAc,CAAd,CAAN,CAArB;AACD,OAFM,MAEA,IAAIF,MAAM,CAACC,KAAP,KAAiB,MAArB,EAA6B;AAClCb,QAAAA,GAAG,CAACK,CAAJ,GAAQW,aAAa,CAAC,GAAD,EAAMJ,MAAM,CAACE,MAAP,CAAc,CAAd,CAAN,CAArB;AACD,OAFM,MAEA;AAAA;;AACL,YAAIF,MAAM,CAACC,KAAP,KAAiB,KAArB,EAA4B;AAC1BD,UAAAA,MAAM,CAACC,KAAP,GAAe,MAAf;AACD;;AAEDb,QAAAA,GAAG,GAAG,2BAAUA,GAAV,CAAN;;AAEA,YAAI,CAACA,GAAG,CAACY,MAAM,CAACC,KAAR,CAAR,EAAwB;AACtB,iBAAOH,kBAAWZ,IAAX,CACL,KADK,EAEL,YAAYc,MAAM,CAACC,KAAnB,GAA2B,gBAFtB,EAGLrB,EAHK,CAAP;AAKD;;AAEDQ,QAAAA,GAAG,GAAG,QAAAA,GAAG,EAACY,MAAM,CAACC,KAAR,CAAH,iDAAqBD,MAAM,CAACE,MAA5B,GAAoCC,KAApC,EAAN;AACD;AACF,KApCD;AAsCA,IAAA,KAAI,CAAC5B,MAAL,CAAYC,IAAZ,CAAiBH,GAAjB,IAAwBe,GAAG,CAACG,CAA5B;AACA,IAAA,KAAI,CAAChB,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IAA4Be,GAAG,CAACI,CAAhC;AACA,IAAA,KAAI,CAACjB,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IAA4Be,GAAG,CAACK,CAAhC;AACD,GAnDD;;AAqDA,MAAI,0BAAcb,EAAd,CAAJ,EAAuB;AACrBA,IAAAA,EAAE,CAACM,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,SAAO,IAAP;AACD;;eAEc;AAAA,SAAO;AACpB;;;;;;AAMAwB,IAAAA,UAPoB,sBAOTC,GAPS,EAOJ/B,EAPI,EAOA;AAClB,UAAI,OAAO+B,GAAP,KAAe,QAAnB,EAA6B;AAC3B,eAAOb,kBAAWZ,IAAX,CAAgB,IAAhB,EAAsB,qBAAtB,EAA6CN,EAA7C,CAAP;AACD;;AAED,UAAI+B,GAAG,GAAG,CAAC,CAAP,IAAYA,GAAG,GAAG,CAAC,CAAvB,EAA0B;AACxB,eAAOb,kBAAWZ,IAAX,CACL,IADK,EAEL,wCAFK,EAGLN,EAHK,CAAP;AAKD;;AAED,WAAKC,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,KAAKN,MAAL,CAAYO,KAAjC,EAAwC,KAAKP,MAAL,CAAYQ,MAApD,EAA4D,UAC1DjB,CAD0D,EAE1DC,CAF0D,EAG1DM,GAH0D,EAI1D;AACA,YAAIsC,GAAG,GAAG,GAAV,EAAe;AACb,eAAKpC,MAAL,CAAYC,IAAZ,CAAiBH,GAAjB,IAAwB,KAAKE,MAAL,CAAYC,IAAZ,CAAiBH,GAAjB,KAAyB,IAAIsC,GAA7B,CAAxB;AACA,eAAKpC,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IAA4B,KAAKE,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,KAA6B,IAAIsC,GAAjC,CAA5B;AACA,eAAKpC,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IAA4B,KAAKE,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,KAA6B,IAAIsC,GAAjC,CAA5B;AACD,SAJD,MAIO;AACL,eAAKpC,MAAL,CAAYC,IAAZ,CAAiBH,GAAjB,IACE,KAAKE,MAAL,CAAYC,IAAZ,CAAiBH,GAAjB,IAAwB,CAAC,MAAM,KAAKE,MAAL,CAAYC,IAAZ,CAAiBH,GAAjB,CAAP,IAAgCsC,GAD1D;AAEA,eAAKpC,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IACE,KAAKE,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IAA4B,CAAC,MAAM,KAAKE,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,CAAP,IAAoCsC,GADlE;AAEA,eAAKpC,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IACE,KAAKE,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IAA4B,CAAC,MAAM,KAAKE,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,CAAP,IAAoCsC,GADlE;AAED;AACF,OAjBD;;AAmBA,UAAI,0BAAc/B,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACM,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD,KA5CmB;;AA8CpB;;;;;;AAMA0B,IAAAA,QApDoB,oBAoDXD,GApDW,EAoDN/B,EApDM,EAoDF;AAChB,UAAI,OAAO+B,GAAP,KAAe,QAAnB,EAA6B;AAC3B,eAAOb,kBAAWZ,IAAX,CAAgB,IAAhB,EAAsB,qBAAtB,EAA6CN,EAA7C,CAAP;AACD;;AAED,UAAI+B,GAAG,GAAG,CAAC,CAAP,IAAYA,GAAG,GAAG,CAAC,CAAvB,EAA0B;AACxB,eAAOb,kBAAWZ,IAAX,CACL,IADK,EAEL,wCAFK,EAGLN,EAHK,CAAP;AAKD;;AAED,UAAMiC,MAAM,GAAG,CAACF,GAAG,GAAG,CAAP,KAAa,IAAIA,GAAjB,CAAf;;AAEA,eAASG,MAAT,CAAgB9C,KAAhB,EAAuB;AACrBA,QAAAA,KAAK,GAAG+C,IAAI,CAACC,KAAL,CAAWH,MAAM,IAAI7C,KAAK,GAAG,GAAZ,CAAN,GAAyB,GAApC,CAAR;AAEA,eAAOA,KAAK,GAAG,CAAR,GAAY,CAAZ,GAAgBA,KAAK,GAAG,GAAR,GAAc,GAAd,GAAoBA,KAA3C;AACD;;AAED,WAAKa,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,KAAKN,MAAL,CAAYO,KAAjC,EAAwC,KAAKP,MAAL,CAAYQ,MAApD,EAA4D,UAC1DjB,CAD0D,EAE1DC,CAF0D,EAG1DM,GAH0D,EAI1D;AACA,aAAKE,MAAL,CAAYC,IAAZ,CAAiBH,GAAjB,IAAwByC,MAAM,CAAC,KAAKvC,MAAL,CAAYC,IAAZ,CAAiBH,GAAjB,CAAD,CAA9B;AACA,aAAKE,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IAA4ByC,MAAM,CAAC,KAAKvC,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,CAAD,CAAlC;AACA,aAAKE,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IAA4ByC,MAAM,CAAC,KAAKvC,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,CAAD,CAAlC;AACD,OARD;;AAUA,UAAI,0BAAcO,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACM,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD,KAxFmB;;AA0FpB;;;;;;AAMA+B,IAAAA,SAhGoB,qBAgGVC,CAhGU,EAgGPtC,EAhGO,EAgGH;AACf,UAAI,OAAOsC,CAAP,KAAa,QAAjB,EAA2B;AACzB,eAAOpB,kBAAWZ,IAAX,CAAgB,IAAhB,EAAsB,mBAAtB,EAA2CN,EAA3C,CAAP;AACD;;AAED,UAAIsC,CAAC,GAAG,CAAR,EAAW;AACTA,QAAAA,CAAC,GAAG,CAAJ;AACD,OAPc,CAOb;;;AAEF,WAAKrC,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,KAAKN,MAAL,CAAYO,KAAjC,EAAwC,KAAKP,MAAL,CAAYQ,MAApD,EAA4D,UAC1DjB,CAD0D,EAE1DC,CAF0D,EAG1DM,GAH0D,EAI1D;AACA,aAAKE,MAAL,CAAYC,IAAZ,CAAiBH,GAAjB,IACG0C,IAAI,CAACC,KAAL,CAAY,KAAKzC,MAAL,CAAYC,IAAZ,CAAiBH,GAAjB,IAAwB,GAAzB,IAAiC6C,CAAC,GAAG,CAArC,CAAX,KAAuDA,CAAC,GAAG,CAA3D,CAAD,GAAkE,GADpE;AAEA,aAAK3C,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IACG0C,IAAI,CAACC,KAAL,CAAY,KAAKzC,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IAA4B,GAA7B,IAAqC6C,CAAC,GAAG,CAAzC,CAAX,KAA2DA,CAAC,GAAG,CAA/D,CAAD,GACA,GAFF;AAGA,aAAK3C,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IACG0C,IAAI,CAACC,KAAL,CAAY,KAAKzC,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IAA4B,GAA7B,IAAqC6C,CAAC,GAAG,CAAzC,CAAX,KAA2DA,CAAC,GAAG,CAA/D,CAAD,GACA,GAFF;AAGD,OAbD;;AAeA,UAAI,0BAActC,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACM,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD,KA7HmB;;AA+HpB;;;;;AAKAP,IAAAA,SAAS,EAATA,SApIoB;AAsIpB;AACAwC,IAAAA,SAAS,EAAExC,SAvIS;;AAyIpB;;;;;;AAMAyC,IAAAA,OA/IoB,mBA+IZC,CA/IY,EA+ITzC,EA/IS,EA+IL;AACb,UAAI,OAAOyC,CAAP,KAAa,QAAjB,EACE,OAAOvB,kBAAWZ,IAAX,CAAgB,IAAhB,EAAsB,oBAAtB,EAA4CN,EAA5C,CAAP;AACF,UAAIyC,CAAC,GAAG,CAAJ,IAASA,CAAC,GAAG,CAAjB,EACE,OAAOvB,kBAAWZ,IAAX,CAAgB,IAAhB,EAAsB,gCAAtB,EAAwDN,EAAxD,CAAP;AAEF,WAAKC,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,KAAKN,MAAL,CAAYO,KAAjC,EAAwC,KAAKP,MAAL,CAAYQ,MAApD,EAA4D,UAC1DjB,CAD0D,EAE1DC,CAF0D,EAG1DM,GAH0D,EAI1D;AACA,YAAMK,CAAC,GAAG,KAAKH,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IAA4BgD,CAAtC;AACA,aAAK9C,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IAA4BK,CAA5B;AACD,OAPD;;AASA,UAAI,0BAAcE,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACM,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD,KAnKmB;;AAqKpB;;;;;AAKAoC,IAAAA,KA1KoB,iBA0Kd1C,EA1Kc,EA0KV;AACR,WAAKC,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,KAAKN,MAAL,CAAYO,KAAjC,EAAwC,KAAKP,MAAL,CAAYQ,MAApD,EAA4D,UAC1DjB,CAD0D,EAE1DC,CAF0D,EAG1DM,GAH0D,EAI1D;AACA,YAAIkD,GAAG,GAAG,KAAKhD,MAAL,CAAYC,IAAZ,CAAiBH,GAAjB,CAAV;AACA,YAAImD,KAAK,GAAG,KAAKjD,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,CAAZ;AACA,YAAIoD,IAAI,GAAG,KAAKlD,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,CAAX;AAEAkD,QAAAA,GAAG,GAAGA,GAAG,GAAG,KAAN,GAAcC,KAAK,GAAG,KAAtB,GAA8BC,IAAI,GAAG,KAA3C;AACAD,QAAAA,KAAK,GAAGD,GAAG,GAAG,KAAN,GAAcC,KAAK,GAAG,KAAtB,GAA8BC,IAAI,GAAG,KAA7C;AACAA,QAAAA,IAAI,GAAGF,GAAG,GAAG,KAAN,GAAcC,KAAK,GAAG,KAAtB,GAA8BC,IAAI,GAAG,KAA5C;AAEA,aAAKlD,MAAL,CAAYC,IAAZ,CAAiBH,GAAjB,IAAwBkD,GAAG,GAAG,GAAN,GAAYA,GAAZ,GAAkB,GAA1C;AACA,aAAKhD,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IAA4BmD,KAAK,GAAG,GAAR,GAAcA,KAAd,GAAsB,GAAlD;AACA,aAAKjD,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IAA4BoD,IAAI,GAAG,GAAP,GAAaA,IAAb,GAAoB,GAAhD;AACD,OAhBD;;AAkBA,UAAI,0BAAc7C,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACM,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD,KAlMmB;;AAoMpB;;;;;;AAMAwC,IAAAA,IA1MoB,gBA0MfL,CA1Me,EA0MZzC,EA1MY,EA0MR;AACV,UAAI,OAAOyC,CAAP,KAAa,QAAjB,EAA2B;AACzB,eAAOvB,kBAAWZ,IAAX,CAAgB,IAAhB,EAAsB,oBAAtB,EAA4CN,EAA5C,CAAP;AACD;;AAED,UAAIyC,CAAC,GAAG,CAAJ,IAASA,CAAC,GAAG,CAAjB,EAAoB;AAClB,eAAOvB,kBAAWZ,IAAX,CAAgB,IAAhB,EAAsB,gCAAtB,EAAwDN,EAAxD,CAAP;AACD,OAPS,CASV;;;AACA,WAAKwC,OAAL,CAAa,IAAIC,CAAjB;;AAEA,UAAI,0BAAczC,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACM,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD,KA3NmB;;AA6NpB;;;;;;;AAOAyC,IAAAA,WApOoB,uBAoOR9D,MApOQ,EAoOA+D,YApOA,EAoOchD,EApOd,EAoOkB;AACpC,UAAI,OAAOgD,YAAP,KAAwB,UAAxB,IAAsC,OAAOhD,EAAP,KAAc,WAAxD,EAAqE;AACnEA,QAAAA,EAAE,GAAGgD,YAAL;AACAA,QAAAA,YAAY,GAAG,IAAf;AACD;;AAED,UAAI,CAACA,YAAL,EAAmB;AACjBA,QAAAA,YAAY,GAAG,KAAKrB,WAAL,CAAiBsB,WAAhC;AACD;;AAED,UAAMC,OAAO,GAAGC,MAAM,CAACC,IAAP,CAAY,KAAKzD,MAAL,CAAYC,IAAxB,CAAhB;AACA,UAAMyD,KAAK,GAAGpE,MAAM,CAACK,MAArB;AACA,UAAMgE,KAAK,GAAGrE,MAAM,CAAC,CAAD,CAAN,CAAUK,MAAxB;AACA,UAAMiE,MAAM,GAAGpB,IAAI,CAACC,KAAL,CAAWiB,KAAK,GAAG,CAAnB,CAAf;AACA,UAAMG,MAAM,GAAGrB,IAAI,CAACC,KAAL,CAAWkB,KAAK,GAAG,CAAnB,CAAf;AACA,UAAMG,MAAM,GAAG,CAACF,MAAhB;AACA,UAAMG,MAAM,GAAG,CAACF,MAAhB;AAEA,UAAIG,MAAJ;AACA,UAAIC,IAAJ;AACA,UAAIC,IAAJ;AACA,UAAIC,IAAJ;AACA,UAAIC,EAAJ;AACA,UAAIC,EAAJ;AACA,UAAIC,EAAJ;AACA,UAAIC,EAAJ;AACA,UAAIC,EAAJ;AACA,UAAIC,IAAJ;AAEA,WAAKnE,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,KAAKN,MAAL,CAAYO,KAAjC,EAAwC,KAAKP,MAAL,CAAYQ,MAApD,EAA4D,UAC1DjB,CAD0D,EAE1DC,CAF0D,EAG1DM,GAH0D,EAI1D;AACAqE,QAAAA,IAAI,GAAG,CAAP;AACAD,QAAAA,IAAI,GAAG,CAAP;AACAD,QAAAA,IAAI,GAAG,CAAP;;AAEA,aAAK,IAAIS,GAAG,GAAGZ,MAAf,EAAuBY,GAAG,IAAId,MAA9B,EAAsCc,GAAG,EAAzC,EAA6C;AAC3C,eAAK,IAAIC,GAAG,GAAGZ,MAAf,EAAuBY,GAAG,IAAId,MAA9B,EAAsCc,GAAG,EAAzC,EAA6C;AAC3CJ,YAAAA,EAAE,GAAGhF,CAAC,GAAGoF,GAAT;AACAH,YAAAA,EAAE,GAAGhF,CAAC,GAAGkF,GAAT;AACAV,YAAAA,MAAM,GAAG1E,MAAM,CAACoF,GAAG,GAAGd,MAAP,CAAN,CAAqBe,GAAG,GAAGd,MAA3B,CAAT;AACAY,YAAAA,IAAI,GAAG,KAAK1E,aAAL,CAAmBwE,EAAnB,EAAuBC,EAAvB,EAA2BnB,YAA3B,CAAP;;AAEA,gBAAIoB,IAAI,KAAK,CAAC,CAAd,EAAiB;AACfH,cAAAA,EAAE,GAAG,CAAL;AACAD,cAAAA,EAAE,GAAG,CAAL;AACAD,cAAAA,EAAE,GAAG,CAAL;AACD,aAJD,MAIO;AACLA,cAAAA,EAAE,GAAG,KAAKpE,MAAL,CAAYC,IAAZ,CAAiBwE,IAAI,GAAG,CAAxB,CAAL;AACAJ,cAAAA,EAAE,GAAG,KAAKrE,MAAL,CAAYC,IAAZ,CAAiBwE,IAAI,GAAG,CAAxB,CAAL;AACAH,cAAAA,EAAE,GAAG,KAAKtE,MAAL,CAAYC,IAAZ,CAAiBwE,IAAI,GAAG,CAAxB,CAAL;AACD;;AAEDR,YAAAA,IAAI,IAAID,MAAM,GAAGI,EAAjB;AACAF,YAAAA,IAAI,IAAIF,MAAM,GAAGK,EAAjB;AACAF,YAAAA,IAAI,IAAIH,MAAM,GAAGM,EAAjB;AACD;AACF;;AAED,YAAIL,IAAI,GAAG,CAAX,EAAc;AACZA,UAAAA,IAAI,GAAG,CAAP;AACD;;AAED,YAAIC,IAAI,GAAG,CAAX,EAAc;AACZA,UAAAA,IAAI,GAAG,CAAP;AACD;;AAED,YAAIC,IAAI,GAAG,CAAX,EAAc;AACZA,UAAAA,IAAI,GAAG,CAAP;AACD;;AAED,YAAIF,IAAI,GAAG,GAAX,EAAgB;AACdA,UAAAA,IAAI,GAAG,GAAP;AACD;;AAED,YAAIC,IAAI,GAAG,GAAX,EAAgB;AACdA,UAAAA,IAAI,GAAG,GAAP;AACD;;AAED,YAAIC,IAAI,GAAG,GAAX,EAAgB;AACdA,UAAAA,IAAI,GAAG,GAAP;AACD;;AAEDZ,QAAAA,OAAO,CAACzD,GAAG,GAAG,CAAP,CAAP,GAAmBmE,IAAnB;AACAV,QAAAA,OAAO,CAACzD,GAAG,GAAG,CAAP,CAAP,GAAmBoE,IAAnB;AACAX,QAAAA,OAAO,CAACzD,GAAG,GAAG,CAAP,CAAP,GAAmBqE,IAAnB;AACD,OA3DD;AA6DA,WAAKnE,MAAL,CAAYC,IAAZ,GAAmBsD,OAAnB;;AAEA,UAAI,0BAAclD,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACM,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD,KArUmB;;AAuUpB;;;;;AAKAiE,IAAAA,MA5UoB,kBA4UbvE,EA5Ua,EA4UT;AACT,WAAKC,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,KAAKN,MAAL,CAAYO,KAAjC,EAAwC,KAAKP,MAAL,CAAYQ,MAApD,EAA4D,UAC1DjB,CAD0D,EAE1DC,CAF0D,EAG1DM,GAH0D,EAI1D;AACA,aAAKE,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IAA4B,GAA5B;AACD,OAND;;AAQA,UAAI,0BAAcO,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACM,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD,KA1VmB;;AA4VpB;;;;;;;;;;AAUAkE,IAAAA,QAtWoB,oBAsWXnF,IAtWW,EAsWLH,CAtWK,EAsWFC,CAtWE,EAsWCsF,CAtWD,EAsWIC,CAtWJ,EAsWO1E,EAtWP,EAsWW;AAC7B,UAAI,OAAOd,CAAP,KAAa,UAAjB,EAA6B;AAC3Bc,QAAAA,EAAE,GAAGd,CAAL;AACAwF,QAAAA,CAAC,GAAG,IAAJ;AACAD,QAAAA,CAAC,GAAG,IAAJ;AACAtF,QAAAA,CAAC,GAAG,IAAJ;AACAD,QAAAA,CAAC,GAAG,IAAJ;AACD,OAND,MAMO;AACL,YAAI,OAAOG,IAAP,KAAgB,QAApB,EAA8B;AAC5B,iBAAO6B,kBAAWZ,IAAX,CAAgB,IAAhB,EAAsB,uBAAtB,EAA+CN,EAA/C,CAAP;AACD;;AAED,YAAIH,KAAK,CAACX,CAAD,CAAL,IAAY,OAAOA,CAAP,KAAa,QAA7B,EAAuC;AACrC,iBAAOgC,kBAAWZ,IAAX,CAAgB,IAAhB,EAAsB,oBAAtB,EAA4CN,EAA5C,CAAP;AACD;;AAED,YAAIH,KAAK,CAACV,CAAD,CAAL,IAAY,OAAOA,CAAP,KAAa,QAA7B,EAAuC;AACrC,iBAAO+B,kBAAWZ,IAAX,CAAgB,IAAhB,EAAsB,oBAAtB,EAA4CN,EAA5C,CAAP;AACD;;AAED,YAAIH,KAAK,CAAC4E,CAAD,CAAL,IAAY,OAAOA,CAAP,KAAa,QAA7B,EAAuC;AACrC,iBAAOvD,kBAAWZ,IAAX,CAAgB,IAAhB,EAAsB,oBAAtB,EAA4CN,EAA5C,CAAP;AACD;;AAED,YAAIH,KAAK,CAAC6E,CAAD,CAAL,IAAY,OAAOA,CAAP,KAAa,QAA7B,EAAuC;AACrC,iBAAOxD,kBAAWZ,IAAX,CAAgB,IAAhB,EAAsB,oBAAtB,EAA4CN,EAA5C,CAAP;AACD;AACF;;AAED,UAAMf,MAAM,GAAG,CACb,CAAC,IAAI,EAAL,EAAS,IAAI,EAAb,EAAiB,IAAI,EAArB,CADa,EAEb,CAAC,IAAI,EAAL,EAAS,IAAI,EAAb,EAAiB,IAAI,EAArB,CAFa,EAGb,CAAC,IAAI,EAAL,EAAS,IAAI,EAAb,EAAiB,IAAI,EAArB,CAHa,CAAf;AAMAC,MAAAA,CAAC,GAAGA,CAAC,IAAI,CAAT;AACAC,MAAAA,CAAC,GAAGA,CAAC,IAAI,CAAT;AACAsF,MAAAA,CAAC,GAAG5E,KAAK,CAAC4E,CAAD,CAAL,GAAWA,CAAX,GAAe,KAAK9E,MAAL,CAAYO,KAAZ,GAAoBhB,CAAvC;AACAwF,MAAAA,CAAC,GAAG7E,KAAK,CAAC6E,CAAD,CAAL,GAAWA,CAAX,GAAe,KAAK/E,MAAL,CAAYQ,MAAZ,GAAqBhB,CAAxC;AAEA,UAAMwF,MAAM,GAAG,KAAKC,UAAL,EAAf;AAEA,WAAK3E,SAAL,CAAef,CAAf,EAAkBC,CAAlB,EAAqBsF,CAArB,EAAwBC,CAAxB,EAA2B,UAASG,EAAT,EAAaC,EAAb,EAAiBrF,GAAjB,EAAsB;AAC/CoF,QAAAA,EAAE,GAAGxF,IAAI,GAAG8C,IAAI,CAACC,KAAL,CAAWyC,EAAE,GAAGxF,IAAhB,CAAZ;AACAyF,QAAAA,EAAE,GAAGzF,IAAI,GAAG8C,IAAI,CAACC,KAAL,CAAW0C,EAAE,GAAGzF,IAAhB,CAAZ;AAEA,YAAMD,KAAK,GAAGL,WAAW,CAAC4F,MAAD,EAAS1F,MAAT,EAAiB4F,EAAjB,EAAqBC,EAArB,CAAzB;AAEA,aAAKnF,MAAL,CAAYC,IAAZ,CAAiBH,GAAjB,IAAwBL,KAAK,CAAC,CAAD,CAA7B;AACA,aAAKO,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IAA4BL,KAAK,CAAC,CAAD,CAAjC;AACA,aAAKO,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IAA4BL,KAAK,CAAC,CAAD,CAAjC;AACD,OATD;;AAWA,UAAI,0BAAcY,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACM,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD,KAhamB;;AAkapB;;;;;;;;;;AAUAyE,IAAAA,SA5aoB,qBA4aV9F,MA5aU,EA4aFC,CA5aE,EA4aCC,CA5aD,EA4aIsF,CA5aJ,EA4aOC,CA5aP,EA4aU1E,EA5aV,EA4ac;AAChC,UAAI,CAACgB,KAAK,CAACC,OAAN,CAAchC,MAAd,CAAL,EACE,OAAOiC,kBAAWZ,IAAX,CAAgB,IAAhB,EAAsB,6BAAtB,EAAqDN,EAArD,CAAP;;AAEF,UAAI,OAAOd,CAAP,KAAa,UAAjB,EAA6B;AAC3Bc,QAAAA,EAAE,GAAGd,CAAL;AACAA,QAAAA,CAAC,GAAG,IAAJ;AACAC,QAAAA,CAAC,GAAG,IAAJ;AACAsF,QAAAA,CAAC,GAAG,IAAJ;AACAC,QAAAA,CAAC,GAAG,IAAJ;AACD,OAND,MAMO;AACL,YAAI7E,KAAK,CAACX,CAAD,CAAL,IAAY,OAAOA,CAAP,KAAa,QAA7B,EAAuC;AACrC,iBAAOgC,kBAAWZ,IAAX,CAAgB,IAAhB,EAAsB,oBAAtB,EAA4CN,EAA5C,CAAP;AACD;;AAED,YAAIH,KAAK,CAACV,CAAD,CAAL,IAAY,OAAOA,CAAP,KAAa,QAA7B,EAAuC;AACrC,iBAAO+B,kBAAWZ,IAAX,CAAgB,IAAhB,EAAsB,oBAAtB,EAA4CN,EAA5C,CAAP;AACD;;AAED,YAAIH,KAAK,CAAC4E,CAAD,CAAL,IAAY,OAAOA,CAAP,KAAa,QAA7B,EAAuC;AACrC,iBAAOvD,kBAAWZ,IAAX,CAAgB,IAAhB,EAAsB,oBAAtB,EAA4CN,EAA5C,CAAP;AACD;;AAED,YAAIH,KAAK,CAAC6E,CAAD,CAAL,IAAY,OAAOA,CAAP,KAAa,QAA7B,EAAuC;AACrC,iBAAOxD,kBAAWZ,IAAX,CAAgB,IAAhB,EAAsB,oBAAtB,EAA4CN,EAA5C,CAAP;AACD;AACF;;AAED,UAAMgF,KAAK,GAAG,CAAC/F,MAAM,CAACK,MAAP,GAAgB,CAAjB,IAAsB,CAApC;AAEAJ,MAAAA,CAAC,GAAGW,KAAK,CAACX,CAAD,CAAL,GAAWA,CAAX,GAAe8F,KAAnB;AACA7F,MAAAA,CAAC,GAAGU,KAAK,CAACV,CAAD,CAAL,GAAWA,CAAX,GAAe6F,KAAnB;AACAP,MAAAA,CAAC,GAAG5E,KAAK,CAAC4E,CAAD,CAAL,GAAWA,CAAX,GAAe,KAAK9E,MAAL,CAAYO,KAAZ,GAAoBhB,CAAvC;AACAwF,MAAAA,CAAC,GAAG7E,KAAK,CAAC6E,CAAD,CAAL,GAAWA,CAAX,GAAe,KAAK/E,MAAL,CAAYQ,MAAZ,GAAqBhB,CAAxC;AAEA,UAAMwF,MAAM,GAAG,KAAKC,UAAL,EAAf;AAEA,WAAK3E,SAAL,CAAef,CAAf,EAAkBC,CAAlB,EAAqBsF,CAArB,EAAwBC,CAAxB,EAA2B,UAASG,EAAT,EAAaC,EAAb,EAAiBrF,GAAjB,EAAsB;AAC/C,YAAML,KAAK,GAAGL,WAAW,CAAC4F,MAAD,EAAS1F,MAAT,EAAiB4F,EAAjB,EAAqBC,EAArB,CAAzB;AAEA,aAAKnF,MAAL,CAAYC,IAAZ,CAAiBH,GAAjB,IAAwB,KAAKkC,WAAL,CAAiBC,QAAjB,CAA0BxC,KAAK,CAAC,CAAD,CAA/B,CAAxB;AACA,aAAKO,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IAA4B,KAAKkC,WAAL,CAAiBC,QAAjB,CAA0BxC,KAAK,CAAC,CAAD,CAA/B,CAA5B;AACA,aAAKO,MAAL,CAAYC,IAAZ,CAAiBH,GAAG,GAAG,CAAvB,IAA4B,KAAKkC,WAAL,CAAiBC,QAAjB,CAA0BxC,KAAK,CAAC,CAAD,CAA/B,CAA5B;AACD,OAND;;AAQA,UAAI,0BAAcY,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACM,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD,KA9dmB;;AAgepB;;;;;;AAMA2E,IAAAA,KAAK,EAAEnE,OAtea;AAuepBoE,IAAAA,MAAM,EAAEpE;AAveY,GAAP;AAAA,C", "sourcesContent": ["import tinyColor from 'tinycolor2';\nimport { throwError, isNodePattern } from '@jimp/utils';\n\nfunction applyKernel(im, kernel, x, y) {\n  const value = [0, 0, 0];\n  const size = (kernel.length - 1) / 2;\n\n  for (let kx = 0; kx < kernel.length; kx += 1) {\n    for (let ky = 0; ky < kernel[kx].length; ky += 1) {\n      const idx = im.getPixelIndex(x + kx - size, y + ky - size);\n\n      value[0] += im.bitmap.data[idx] * kernel[kx][ky];\n      value[1] += im.bitmap.data[idx + 1] * kernel[kx][ky];\n      value[2] += im.bitmap.data[idx + 2] * kernel[kx][ky];\n    }\n  }\n\n  return value;\n}\n\nconst isDef = v => typeof v !== 'undefined' && v !== null;\n\nfunction greyscale(cb) {\n  this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function(\n    x,\n    y,\n    idx\n  ) {\n    const grey = parseInt(\n      0.2126 * this.bitmap.data[idx] +\n        0.7152 * this.bitmap.data[idx + 1] +\n        0.0722 * this.bitmap.data[idx + 2],\n      10\n    );\n\n    this.bitmap.data[idx] = grey;\n    this.bitmap.data[idx + 1] = grey;\n    this.bitmap.data[idx + 2] = grey;\n  });\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, this);\n  }\n\n  return this;\n}\n\nfunction mix(clr, clr2, p = 50) {\n  return {\n    r: (clr2.r - clr.r) * (p / 100) + clr.r,\n    g: (clr2.g - clr.g) * (p / 100) + clr.g,\n    b: (clr2.b - clr.b) * (p / 100) + clr.b\n  };\n}\n\nfunction colorFn(actions, cb) {\n  if (!actions || !Array.isArray(actions)) {\n    return throwError.call(this, 'actions must be an array', cb);\n  }\n\n  actions = actions.map(action => {\n    if (action.apply === 'xor' || action.apply === 'mix') {\n      action.params[0] = tinyColor(action.params[0]).toRgb();\n    }\n\n    return action;\n  });\n\n  this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, (x, y, idx) => {\n    let clr = {\n      r: this.bitmap.data[idx],\n      g: this.bitmap.data[idx + 1],\n      b: this.bitmap.data[idx + 2]\n    };\n\n    const colorModifier = (i, amount) =>\n      this.constructor.limit255(clr[i] + amount);\n\n    actions.forEach(action => {\n      if (action.apply === 'mix') {\n        clr = mix(clr, action.params[0], action.params[1]);\n      } else if (action.apply === 'tint') {\n        clr = mix(clr, { r: 255, g: 255, b: 255 }, action.params[0]);\n      } else if (action.apply === 'shade') {\n        clr = mix(clr, { r: 0, g: 0, b: 0 }, action.params[0]);\n      } else if (action.apply === 'xor') {\n        clr = {\n          r: clr.r ^ action.params[0].r,\n          g: clr.g ^ action.params[0].g,\n          b: clr.b ^ action.params[0].b\n        };\n      } else if (action.apply === 'red') {\n        clr.r = colorModifier('r', action.params[0]);\n      } else if (action.apply === 'green') {\n        clr.g = colorModifier('g', action.params[0]);\n      } else if (action.apply === 'blue') {\n        clr.b = colorModifier('b', action.params[0]);\n      } else {\n        if (action.apply === 'hue') {\n          action.apply = 'spin';\n        }\n\n        clr = tinyColor(clr);\n\n        if (!clr[action.apply]) {\n          return throwError.call(\n            this,\n            'action ' + action.apply + ' not supported',\n            cb\n          );\n        }\n\n        clr = clr[action.apply](...action.params).toRgb();\n      }\n    });\n\n    this.bitmap.data[idx] = clr.r;\n    this.bitmap.data[idx + 1] = clr.g;\n    this.bitmap.data[idx + 2] = clr.b;\n  });\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, this);\n  }\n\n  return this;\n}\n\nexport default () => ({\n  /**\n   * Adjusts the brightness of the image\n   * @param {number} val the amount to adjust the brightness, a number between -1 and +1\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  brightness(val, cb) {\n    if (typeof val !== 'number') {\n      return throwError.call(this, 'val must be numbers', cb);\n    }\n\n    if (val < -1 || val > +1) {\n      return throwError.call(\n        this,\n        'val must be a number between -1 and +1',\n        cb\n      );\n    }\n\n    this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function(\n      x,\n      y,\n      idx\n    ) {\n      if (val < 0.0) {\n        this.bitmap.data[idx] = this.bitmap.data[idx] * (1 + val);\n        this.bitmap.data[idx + 1] = this.bitmap.data[idx + 1] * (1 + val);\n        this.bitmap.data[idx + 2] = this.bitmap.data[idx + 2] * (1 + val);\n      } else {\n        this.bitmap.data[idx] =\n          this.bitmap.data[idx] + (255 - this.bitmap.data[idx]) * val;\n        this.bitmap.data[idx + 1] =\n          this.bitmap.data[idx + 1] + (255 - this.bitmap.data[idx + 1]) * val;\n        this.bitmap.data[idx + 2] =\n          this.bitmap.data[idx + 2] + (255 - this.bitmap.data[idx + 2]) * val;\n      }\n    });\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Adjusts the contrast of the image\n   * @param {number} val the amount to adjust the contrast, a number between -1 and +1\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  contrast(val, cb) {\n    if (typeof val !== 'number') {\n      return throwError.call(this, 'val must be numbers', cb);\n    }\n\n    if (val < -1 || val > +1) {\n      return throwError.call(\n        this,\n        'val must be a number between -1 and +1',\n        cb\n      );\n    }\n\n    const factor = (val + 1) / (1 - val);\n\n    function adjust(value) {\n      value = Math.floor(factor * (value - 127) + 127);\n\n      return value < 0 ? 0 : value > 255 ? 255 : value;\n    }\n\n    this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function(\n      x,\n      y,\n      idx\n    ) {\n      this.bitmap.data[idx] = adjust(this.bitmap.data[idx]);\n      this.bitmap.data[idx + 1] = adjust(this.bitmap.data[idx + 1]);\n      this.bitmap.data[idx + 2] = adjust(this.bitmap.data[idx + 2]);\n    });\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Apply a posterize effect\n   * @param {number} n the amount to adjust the contrast, minimum threshold is two\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  posterize(n, cb) {\n    if (typeof n !== 'number') {\n      return throwError.call(this, 'n must be numbers', cb);\n    }\n\n    if (n < 2) {\n      n = 2;\n    } // minimum of 2 levels\n\n    this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function(\n      x,\n      y,\n      idx\n    ) {\n      this.bitmap.data[idx] =\n        (Math.floor((this.bitmap.data[idx] / 255) * (n - 1)) / (n - 1)) * 255;\n      this.bitmap.data[idx + 1] =\n        (Math.floor((this.bitmap.data[idx + 1] / 255) * (n - 1)) / (n - 1)) *\n        255;\n      this.bitmap.data[idx + 2] =\n        (Math.floor((this.bitmap.data[idx + 2] / 255) * (n - 1)) / (n - 1)) *\n        255;\n    });\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Removes colour from the image using ITU Rec 709 luminance values\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  greyscale,\n\n  // Alias of greyscale for our American friends\n  grayscale: greyscale,\n\n  /**\n   * Multiplies the opacity of each pixel by a factor between 0 and 1\n   * @param {number} f A number, the factor by which to multiply the opacity of each pixel\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  opacity(f, cb) {\n    if (typeof f !== 'number')\n      return throwError.call(this, 'f must be a number', cb);\n    if (f < 0 || f > 1)\n      return throwError.call(this, 'f must be a number from 0 to 1', cb);\n\n    this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function(\n      x,\n      y,\n      idx\n    ) {\n      const v = this.bitmap.data[idx + 3] * f;\n      this.bitmap.data[idx + 3] = v;\n    });\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Applies a sepia tone to the image\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  sepia(cb) {\n    this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function(\n      x,\n      y,\n      idx\n    ) {\n      let red = this.bitmap.data[idx];\n      let green = this.bitmap.data[idx + 1];\n      let blue = this.bitmap.data[idx + 2];\n\n      red = red * 0.393 + green * 0.769 + blue * 0.189;\n      green = red * 0.349 + green * 0.686 + blue * 0.168;\n      blue = red * 0.272 + green * 0.534 + blue * 0.131;\n\n      this.bitmap.data[idx] = red < 255 ? red : 255;\n      this.bitmap.data[idx + 1] = green < 255 ? green : 255;\n      this.bitmap.data[idx + 2] = blue < 255 ? blue : 255;\n    });\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Fades each pixel by a factor between 0 and 1\n   * @param {number} f A number from 0 to 1. 0 will haven no effect. 1 will turn the image completely transparent.\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  fade(f, cb) {\n    if (typeof f !== 'number') {\n      return throwError.call(this, 'f must be a number', cb);\n    }\n\n    if (f < 0 || f > 1) {\n      return throwError.call(this, 'f must be a number from 0 to 1', cb);\n    }\n\n    // this method is an alternative to opacity (which may be deprecated)\n    this.opacity(1 - f);\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Adds each element of the image to its local neighbors, weighted by the kernel\n   * @param {array} kernel a matrix to weight the neighbors sum\n   * @param {string} edgeHandling (optional) define how to sum pixels from outside the border\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  convolution(kernel, edgeHandling, cb) {\n    if (typeof edgeHandling === 'function' && typeof cb === 'undefined') {\n      cb = edgeHandling;\n      edgeHandling = null;\n    }\n\n    if (!edgeHandling) {\n      edgeHandling = this.constructor.EDGE_EXTEND;\n    }\n\n    const newData = Buffer.from(this.bitmap.data);\n    const kRows = kernel.length;\n    const kCols = kernel[0].length;\n    const rowEnd = Math.floor(kRows / 2);\n    const colEnd = Math.floor(kCols / 2);\n    const rowIni = -rowEnd;\n    const colIni = -colEnd;\n\n    let weight;\n    let rSum;\n    let gSum;\n    let bSum;\n    let ri;\n    let gi;\n    let bi;\n    let xi;\n    let yi;\n    let idxi;\n\n    this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function(\n      x,\n      y,\n      idx\n    ) {\n      bSum = 0;\n      gSum = 0;\n      rSum = 0;\n\n      for (let row = rowIni; row <= rowEnd; row++) {\n        for (let col = colIni; col <= colEnd; col++) {\n          xi = x + col;\n          yi = y + row;\n          weight = kernel[row + rowEnd][col + colEnd];\n          idxi = this.getPixelIndex(xi, yi, edgeHandling);\n\n          if (idxi === -1) {\n            bi = 0;\n            gi = 0;\n            ri = 0;\n          } else {\n            ri = this.bitmap.data[idxi + 0];\n            gi = this.bitmap.data[idxi + 1];\n            bi = this.bitmap.data[idxi + 2];\n          }\n\n          rSum += weight * ri;\n          gSum += weight * gi;\n          bSum += weight * bi;\n        }\n      }\n\n      if (rSum < 0) {\n        rSum = 0;\n      }\n\n      if (gSum < 0) {\n        gSum = 0;\n      }\n\n      if (bSum < 0) {\n        bSum = 0;\n      }\n\n      if (rSum > 255) {\n        rSum = 255;\n      }\n\n      if (gSum > 255) {\n        gSum = 255;\n      }\n\n      if (bSum > 255) {\n        bSum = 255;\n      }\n\n      newData[idx + 0] = rSum;\n      newData[idx + 1] = gSum;\n      newData[idx + 2] = bSum;\n    });\n\n    this.bitmap.data = newData;\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Set the alpha channel on every pixel to fully opaque\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  opaque(cb) {\n    this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function(\n      x,\n      y,\n      idx\n    ) {\n      this.bitmap.data[idx + 3] = 255;\n    });\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Pixelates the image or a region\n   * @param {number} size the size of the pixels\n   * @param {number} x (optional) the x position of the region to pixelate\n   * @param {number} y (optional) the y position of the region to pixelate\n   * @param {number} w (optional) the width of the region to pixelate\n   * @param {number} h (optional) the height of the region to pixelate\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  pixelate(size, x, y, w, h, cb) {\n    if (typeof x === 'function') {\n      cb = x;\n      h = null;\n      w = null;\n      y = null;\n      x = null;\n    } else {\n      if (typeof size !== 'number') {\n        return throwError.call(this, 'size must be a number', cb);\n      }\n\n      if (isDef(x) && typeof x !== 'number') {\n        return throwError.call(this, 'x must be a number', cb);\n      }\n\n      if (isDef(y) && typeof y !== 'number') {\n        return throwError.call(this, 'y must be a number', cb);\n      }\n\n      if (isDef(w) && typeof w !== 'number') {\n        return throwError.call(this, 'w must be a number', cb);\n      }\n\n      if (isDef(h) && typeof h !== 'number') {\n        return throwError.call(this, 'h must be a number', cb);\n      }\n    }\n\n    const kernel = [\n      [1 / 16, 2 / 16, 1 / 16],\n      [2 / 16, 4 / 16, 2 / 16],\n      [1 / 16, 2 / 16, 1 / 16]\n    ];\n\n    x = x || 0;\n    y = y || 0;\n    w = isDef(w) ? w : this.bitmap.width - x;\n    h = isDef(h) ? h : this.bitmap.height - y;\n\n    const source = this.cloneQuiet();\n\n    this.scanQuiet(x, y, w, h, function(xx, yx, idx) {\n      xx = size * Math.floor(xx / size);\n      yx = size * Math.floor(yx / size);\n\n      const value = applyKernel(source, kernel, xx, yx);\n\n      this.bitmap.data[idx] = value[0];\n      this.bitmap.data[idx + 1] = value[1];\n      this.bitmap.data[idx + 2] = value[2];\n    });\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Applies a convolution kernel to the image or a region\n   * @param {array} kernel the convolution kernel\n   * @param {number} x (optional) the x position of the region to apply convolution to\n   * @param {number} y (optional) the y position of the region to apply convolution to\n   * @param {number} w (optional) the width of the region to apply convolution to\n   * @param {number} h (optional) the height of the region to apply convolution to\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  convolute(kernel, x, y, w, h, cb) {\n    if (!Array.isArray(kernel))\n      return throwError.call(this, 'the kernel must be an array', cb);\n\n    if (typeof x === 'function') {\n      cb = x;\n      x = null;\n      y = null;\n      w = null;\n      h = null;\n    } else {\n      if (isDef(x) && typeof x !== 'number') {\n        return throwError.call(this, 'x must be a number', cb);\n      }\n\n      if (isDef(y) && typeof y !== 'number') {\n        return throwError.call(this, 'y must be a number', cb);\n      }\n\n      if (isDef(w) && typeof w !== 'number') {\n        return throwError.call(this, 'w must be a number', cb);\n      }\n\n      if (isDef(h) && typeof h !== 'number') {\n        return throwError.call(this, 'h must be a number', cb);\n      }\n    }\n\n    const ksize = (kernel.length - 1) / 2;\n\n    x = isDef(x) ? x : ksize;\n    y = isDef(y) ? y : ksize;\n    w = isDef(w) ? w : this.bitmap.width - x;\n    h = isDef(h) ? h : this.bitmap.height - y;\n\n    const source = this.cloneQuiet();\n\n    this.scanQuiet(x, y, w, h, function(xx, yx, idx) {\n      const value = applyKernel(source, kernel, xx, yx);\n\n      this.bitmap.data[idx] = this.constructor.limit255(value[0]);\n      this.bitmap.data[idx + 1] = this.constructor.limit255(value[1]);\n      this.bitmap.data[idx + 2] = this.constructor.limit255(value[2]);\n    });\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Apply multiple color modification rules\n   * @param {array} actions list of color modification rules, in following format: { apply: '<rule-name>', params: [ <rule-parameters> ]  }\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  color: colorFn,\n  colour: colorFn\n});\n"], "file": "index.js"}