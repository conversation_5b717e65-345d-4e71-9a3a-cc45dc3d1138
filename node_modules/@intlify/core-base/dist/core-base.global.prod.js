/*!
  * @intlify/core-base v9.1.9
  * (c) 2021 ka<PERSON><PERSON>
  * Released under the MIT License.
  */
var IntlifyCoreBase=function(e){"use strict";const t=/\{([0-9a-zA-Z]+)\}/g;const n=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),r=e=>"number"==typeof e&&isFinite(e),o=e=>"[object RegExp]"===g(e),s=e=>b(e)&&0===Object.keys(e).length;function c(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const a=Object.assign;function u(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const l=Object.prototype.hasOwnProperty;function i(e,t){return l.call(e,t)}const f=Array.isArray,p=e=>"function"==typeof e,m=e=>"string"==typeof e,d=e=>"boolean"==typeof e,k=e=>null!==e&&"object"==typeof e,h=Object.prototype.toString,g=e=>h.call(e),b=e=>"[object Object]"===g(e),y=[];y[0]={w:[0],i:[3,0],"[":[4],o:[7]},y[1]={w:[1],".":[2],"[":[4],o:[7]},y[2]={w:[2],i:[3,0],0:[3,0]},y[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},y[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},y[5]={"'":[4,0],o:8,l:[5,0]},y[6]={'"':[4,0],o:8,l:[6,0]};const x=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function C(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function T(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(x.test(t)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t)}function _(e){const t=[];let n,r,o,s,c,a,u,l=-1,i=0,f=0;const p=[];function m(){const t=e[l+1];if(5===i&&"'"===t||6===i&&'"'===t)return l++,o="\\"+t,p[0](),!0}for(p[0]=()=>{void 0===r?r=o:r+=o},p[1]=()=>{void 0!==r&&(t.push(r),r=void 0)},p[2]=()=>{p[0](),f++},p[3]=()=>{if(f>0)f--,i=4,p[0]();else{if(f=0,void 0===r)return!1;if(r=T(r),!1===r)return!1;p[1]()}};null!==i;)if(l++,n=e[l],"\\"!==n||!m()){if(s=C(n),u=y[i],c=u[s]||u.l||8,8===c)return;if(i=c[0],void 0!==c[1]&&(a=p[c[1]],a&&(o=n,!1===a())))return;if(7===i)return t}}const v=new Map;function L(e,t){if(!k(e))return null;let n=v.get(t);if(n||(n=_(t),n&&v.set(t,n)),!n)return null;const r=n.length;let o=e,s=0;for(;s<r;){const e=o[n[s]];if(void 0===e)return null;o=e,s++}return o}const w=e=>e,P=e=>"",O="text",F=e=>0===e.length?"":e.join(""),N=e=>null==e?"":f(e)||b(e)&&e.toString===h?JSON.stringify(e,null,2):String(e);function S(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function $(e={}){const t=e.locale,n=function(e){const t=r(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(r(e.named.count)||r(e.named.n))?r(e.named.count)?e.named.count:r(e.named.n)?e.named.n:t:t}(e),o=k(e.pluralRules)&&m(t)&&p(e.pluralRules[t])?e.pluralRules[t]:S,s=k(e.pluralRules)&&m(t)&&p(e.pluralRules[t])?S:void 0,c=e.list||[],a=e.named||{};r(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(n,a);function u(t){const n=p(e.messages)?e.messages(t):!!k(e.messages)&&e.messages[t];return n||(e.parent?e.parent.message(t):P)}const l=b(e.processor)&&p(e.processor.normalize)?e.processor.normalize:F,i=b(e.processor)&&p(e.processor.interpolate)?e.processor.interpolate:N,f={list:e=>c[e],named:e=>a[e],plural:e=>e[o(n,e.length,s)],linked:(t,n)=>{const r=u(t)(f);return m(n)?(o=n,e.modifiers?e.modifiers[o]:w)(r):r;var o},message:u,type:b(e.processor)&&m(e.processor.type)?e.processor.type:O,interpolate:i,normalize:l};return f}function E(e,t,n={}){const{domain:r}=n,o=new SyntaxError(String(e));return o.code=e,t&&(o.location=t),o.domain=r,o}function I(e){throw e}function A(e,t,n){const r={start:e,end:t};return null!=n&&(r.source=n),r}const M=" ",W="\n",j=String.fromCharCode(8232),D=String.fromCharCode(8233);function R(e){const t=e;let n=0,r=1,o=1,s=0;const c=e=>"\r"===t[e]&&t[e+1]===W,a=e=>t[e]===D,u=e=>t[e]===j,l=e=>c(e)||(e=>t[e]===W)(e)||a(e)||u(e),i=e=>c(e)||a(e)||u(e)?W:t[e];function f(){return s=0,l(n)&&(r++,o=0),c(n)&&n++,n++,o++,t[n]}return{index:()=>n,line:()=>r,column:()=>o,peekOffset:()=>s,charAt:i,currentChar:()=>i(n),currentPeek:()=>i(n+s),next:f,peek:function(){return c(n+s)&&s++,s++,t[n+s]},reset:function(){n=0,r=1,o=1,s=0},resetPeek:function(e=0){s=e},skipToPeek:function(){const e=n+s;for(;e!==n;)f();s=0}}}const J=void 0;function z(e,t={}){const n=!1!==t.location,r=R(e),o=()=>r.index(),s=()=>{return e=r.line(),t=r.column(),n=r.index(),{line:e,column:t,offset:n};var e,t,n},c=s(),a=o(),u={currentType:14,offset:a,startLoc:c,endLoc:c,lastType:14,lastOffset:a,lastStartLoc:c,lastEndLoc:c,braceNest:0,inLinked:!1,text:""},l=()=>u,{onError:i}=t;function f(e,t,r){e.endLoc=s(),e.currentType=t;const o={type:t};return n&&(o.loc=A(e.startLoc,e.endLoc)),null!=r&&(o.value=r),o}const p=e=>f(e,14);function m(e,t){return e.currentChar()===t?(e.next(),t):(s(),"")}function d(e){let t="";for(;e.currentPeek()===M||e.currentPeek()===W;)t+=e.currentPeek(),e.peek();return t}function k(e){const t=d(e);return e.skipToPeek(),t}function h(e){if(e===J)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function g(e,t){const{currentType:n}=t;if(2!==n)return!1;d(e);const r=function(e){if(e===J)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function b(e){d(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function y(e,t=!0){const n=(t=!1,r="",o=!1)=>{const s=e.currentPeek();return"{"===s?"%"!==r&&t:"@"!==s&&s?"%"===s?(e.peek(),n(t,"%",!0)):"|"===s?!("%"!==r&&!o)||!(r===M||r===W):s===M?(e.peek(),n(!0,M,o)):s!==W||(e.peek(),n(!0,W,o)):"%"===r||t},r=n();return t&&e.resetPeek(),r}function x(e,t){const n=e.currentChar();return n===J?J:t(n)?(e.next(),n):null}function C(e){return x(e,(e=>{const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}))}function T(e){return x(e,(e=>{const t=e.charCodeAt(0);return t>=48&&t<=57}))}function _(e){return x(e,(e=>{const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}))}function v(e){let t="",n="";for(;t=T(e);)n+=t;return n}function L(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return w(e,t,4);case"U":return w(e,t,6);default:return s(),""}}function w(e,t,n){m(e,t);let r="";for(let t=0;t<n;t++){const t=_(e);if(!t){s(),e.currentChar();break}r+=t}return`\\${t}${r}`}function P(e){k(e);const t=m(e,"|");return k(e),t}function O(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&s(),e.next(),n=f(t,2,"{"),k(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&s(),e.next(),n=f(t,3,"}"),t.braceNest--,t.braceNest>0&&k(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&s(),n=F(e,t)||p(t),t.braceNest=0,n;default:let r=!0,o=!0,c=!0;if(b(e))return t.braceNest>0&&s(),n=f(t,1,P(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(5===t.currentType||6===t.currentType||7===t.currentType))return s(),t.braceNest=0,N(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;d(e);const r=h(e.currentPeek());return e.resetPeek(),r}(e,t))return n=f(t,5,function(e){k(e);let t="",n="";for(;t=C(e);)n+=t;return e.currentChar()===J&&s(),n}(e)),k(e),n;if(o=g(e,t))return n=f(t,6,function(e){k(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${v(e)}`):t+=v(e),e.currentChar()===J&&s(),t}(e)),k(e),n;if(c=function(e,t){const{currentType:n}=t;if(2!==n)return!1;d(e);const r="'"===e.currentPeek();return e.resetPeek(),r}(e,t))return n=f(t,7,function(e){k(e),m(e,"'");let t="",n="";const r=e=>"'"!==e&&e!==W;for(;t=x(e,r);)n+="\\"===t?L(e):t;const o=e.currentChar();return o===W||o===J?(s(),o===W&&(e.next(),m(e,"'")),n):(m(e,"'"),n)}(e)),k(e),n;if(!r&&!o&&!c)return n=f(t,13,function(e){k(e);let t="",n="";const r=e=>"{"!==e&&"}"!==e&&e!==M&&e!==W;for(;t=x(e,r);)n+=t;return n}(e)),s(),k(e),n}return n}function F(e,t){const{currentType:n}=t;let r=null;const o=e.currentChar();switch(8!==n&&9!==n&&12!==n&&10!==n||o!==W&&o!==M||s(),o){case"@":return e.next(),r=f(t,8,"@"),t.inLinked=!0,r;case".":return k(e),e.next(),f(t,9,".");case":":return k(e),e.next(),f(t,10,":");default:return b(e)?(r=f(t,1,P(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(8!==n)return!1;d(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(8!==n&&12!==n)return!1;d(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(k(e),F(e,t)):function(e,t){const{currentType:n}=t;if(9!==n)return!1;d(e);const r=h(e.currentPeek());return e.resetPeek(),r}(e,t)?(k(e),f(t,12,function(e){let t="",n="";for(;t=C(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(10!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?h(e.peek()):!("@"===t||"%"===t||"|"===t||":"===t||"."===t||t===M||!t)&&(t===W?(e.peek(),r()):h(t))},o=r();return e.resetPeek(),o}(e,t)?(k(e),"{"===o?O(e,t)||r:f(t,11,function(e){const t=(n=!1,r)=>{const o=e.currentChar();return"{"!==o&&"%"!==o&&"@"!==o&&"|"!==o&&o?o===M?r:o===W?(r+=o,e.next(),t(n,r)):(r+=o,e.next(),t(!0,r)):r};return t(!1,"")}(e))):(8===n&&s(),t.braceNest=0,t.inLinked=!1,N(e,t))}}function N(e,t){let n={type:14};if(t.braceNest>0)return O(e,t)||p(t);if(t.inLinked)return F(e,t)||p(t);const r=e.currentChar();switch(r){case"{":return O(e,t)||p(t);case"}":return s(),e.next(),f(t,3,"}");case"@":return F(e,t)||p(t);default:if(b(e))return n=f(t,1,P(e)),t.braceNest=0,t.inLinked=!1,n;if(y(e))return f(t,0,function(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if("%"===n){if(!y(e))break;t+=n,e.next()}else if(n===M||n===W)if(y(e))t+=n,e.next();else{if(b(e))break;t+=n,e.next()}else t+=n,e.next()}return t}(e));if("%"===r)return e.next(),f(t,4,"%")}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:c}=u;return u.lastType=e,u.lastOffset=t,u.lastStartLoc=n,u.lastEndLoc=c,u.offset=o(),u.startLoc=s(),r.currentChar()===J?f(u,14):N(r,u)},currentOffset:o,currentPosition:s,context:l}}const H=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function U(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function V(e={}){const t=!1!==e.location,{onError:n}=e;function r(e,n,r){const o={type:e,start:n,end:n};return t&&(o.loc={start:r,end:r}),o}function o(e,n,r,o){e.end=n,o&&(e.type=o),t&&e.loc&&(e.loc.end=r)}function s(e,t){const n=e.context(),s=r(3,n.offset,n.startLoc);return s.value=t,o(s,e.currentOffset(),e.currentPosition()),s}function c(e,t){const n=e.context(),{lastOffset:s,lastStartLoc:c}=n,a=r(5,s,c);return a.index=parseInt(t,10),e.nextToken(),o(a,e.currentOffset(),e.currentPosition()),a}function u(e,t){const n=e.context(),{lastOffset:s,lastStartLoc:c}=n,a=r(4,s,c);return a.key=t,e.nextToken(),o(a,e.currentOffset(),e.currentPosition()),a}function l(e,t){const n=e.context(),{lastOffset:s,lastStartLoc:c}=n,a=r(9,s,c);return a.value=t.replace(H,U),e.nextToken(),o(a,e.currentOffset(),e.currentPosition()),a}function i(e){const t=e.context(),n=r(6,t.offset,t.startLoc);let s=e.nextToken();if(9===s.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:s,lastStartLoc:c}=n,a=r(8,s,c);return 12!==t.type?(a.value="",o(a,s,c),{nextConsumeToken:t,node:a}):(null==t.value&&G(t),a.value=t.value||"",o(a,e.currentOffset(),e.currentPosition()),{node:a})}(e);n.modifier=t.node,s=t.nextConsumeToken||e.nextToken()}switch(10!==s.type&&G(s),s=e.nextToken(),2===s.type&&(s=e.nextToken()),s.type){case 11:null==s.value&&G(s),n.key=function(e,t){const n=e.context(),s=r(7,n.offset,n.startLoc);return s.value=t,o(s,e.currentOffset(),e.currentPosition()),s}(e,s.value||"");break;case 5:null==s.value&&G(s),n.key=u(e,s.value||"");break;case 6:null==s.value&&G(s),n.key=c(e,s.value||"");break;case 7:null==s.value&&G(s),n.key=l(e,s.value||"");break;default:const t=e.context(),a=r(7,t.offset,t.startLoc);return a.value="",o(a,t.offset,t.startLoc),n.key=a,o(n,t.offset,t.startLoc),{nextConsumeToken:s,node:n}}return o(n,e.currentOffset(),e.currentPosition()),{node:n}}function f(e){const t=e.context(),n=r(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let a=null;do{const t=a||e.nextToken();switch(a=null,t.type){case 0:null==t.value&&G(t),n.items.push(s(e,t.value||""));break;case 6:null==t.value&&G(t),n.items.push(c(e,t.value||""));break;case 5:null==t.value&&G(t),n.items.push(u(e,t.value||""));break;case 7:null==t.value&&G(t),n.items.push(l(e,t.value||""));break;case 8:const r=i(e);n.items.push(r.node),a=r.nextConsumeToken||null}}while(14!==t.currentType&&1!==t.currentType);return o(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function p(e){const t=e.context(),{offset:n,startLoc:s}=t,c=f(e);return 14===t.currentType?c:function(e,t,n,s){const c=e.context();let a=0===s.items.length;const u=r(1,t,n);u.cases=[],u.cases.push(s);do{const t=f(e);a||(a=0===t.items.length),u.cases.push(t)}while(14!==c.currentType);return o(u,e.currentOffset(),e.currentPosition()),u}(e,n,s,c)}return{parse:function(n){const s=z(n,a({},e)),c=s.context(),u=r(0,c.offset,c.startLoc);return t&&u.loc&&(u.loc.source=n),u.body=p(s),o(u,s.currentOffset(),s.currentPosition()),u}}}function G(e){if(14===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function K(e,t){for(let n=0;n<e.length;n++)q(e[n],t)}function q(e,t){switch(e.type){case 1:K(e.cases,t),t.helper("plural");break;case 2:K(e.items,t);break;case 6:q(e.key,t),t.helper("linked");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function B(e,t={}){const n=function(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:e=>(n.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&q(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function Y(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?Y(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const o=t.cases.length;for(let n=0;n<o&&(Y(e,t.cases[n]),n!==o-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const o=t.items.length;for(let n=0;n<o&&(Y(e,t.items[n]),n!==o-1);n++)e.push(", ");e.deindent(r()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),Y(e,t.key),t.modifier&&(e.push(", "),Y(e,t.modifier)),e.push(")")}(e,t);break;case 8:case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t);break;case 9:case 3:e.push(JSON.stringify(t.value),t)}}function Z(e,t={}){const n=a({},t),r=V(n).parse(e);return B(r,n),((e,t={})=>{const n=m(t.mode)?t.mode:"normal",r=m(t.filename)?t.filename:"message.intl",o=t.needIndent?t.needIndent:"arrow"!==n,s=e.helpers||[],c=function(e,t){const{filename:n,breakLineCode:r,needIndent:o}=t,s={source:e.loc.source,filename:n,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:r,needIndent:o,indentLevel:0};function c(e,t){s.code+=e}function a(e,t=!0){const n=t?r:"";c(o?n+"  ".repeat(e):n)}return{context:()=>s,push:c,indent:function(e=!0){const t=++s.indentLevel;e&&a(t)},deindent:function(e=!0){const t=--s.indentLevel;e&&a(t)},newline:function(){a(s.indentLevel)},helper:e=>`_${e}`,needIndent:()=>s.needIndent}}(e,{mode:n,filename:r,sourceMap:!!t.sourceMap,breakLineCode:null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",needIndent:o});c.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),c.indent(o),s.length>0&&(c.push(`const { ${s.map((e=>`${e}: _${e}`)).join(", ")} } = ctx`),c.newline()),c.push("return "),Y(c,e),c.deindent(o),c.push("}");const{code:a,map:u}=c.context();return{ast:e,code:a,map:u?u.toJSON():void 0}})(r,n)}const Q="i18n:init";let X=null;const ee=te("function:translate");function te(e){return t=>X&&X.emit(e,t)}const ne={0:"Not found '{key}' key in '{locale}' locale messages.",1:"Fall back to translate '{key}' key with '{target}' locale.",2:"Cannot format a number value due to not supported Intl.NumberFormat.",3:"Fall back to number format '{key}' key with '{target}' locale.",4:"Cannot format a date value due to not supported Intl.DateTimeFormat.",5:"Fall back to datetime format '{key}' key with '{target}' locale."};const re="9.1.9";let oe;let se=null;let ce=0;function ae(e,t,n,r,o){const{missing:s}=e;if(null!==s){const r=s(e,n,t,o);return m(r)?r:t}return t}function ue(e,t,n){const r=e;r.__localeChainCache||(r.__localeChainCache=new Map);let o=r.__localeChainCache.get(n);if(!o){o=[];let e=[n];for(;f(e);)e=le(o,e,t);const s=f(t)?t:b(t)?t.default?t.default:null:t;e=m(s)?[s]:s,f(e)&&le(o,e,!1),r.__localeChainCache.set(n,o)}return o}function le(e,t,n){let r=!0;for(let o=0;o<t.length&&d(r);o++){m(t[o])&&(r=ie(e,t[o],n))}return r}function ie(e,t,n){let r;const o=t.split("-");do{r=fe(e,o.join("-"),n),o.splice(-1,1)}while(o.length&&!0===r);return r}function fe(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r="!"!==t[t.length-1];const o=t.replace(/!/g,"");e.push(o),(f(n)||b(n))&&n[o]&&(r=n[o])}return r}const pe=e=>e;let me=Object.create(null);const de=()=>"",ke=e=>p(e);function he(e,t,r,o,s,c){const{messageCompiler:a,warnHtmlMessage:u}=e;if(ke(o)){const e=o;return e.locale=e.locale||r,e.key=e.key||t,e}const l=a(o,function(e,t,r,o,s,c){return{warnHtmlMessage:s,onError:e=>{throw c&&c(e),e},onCacheKey:e=>((e,t,r)=>n({l:e,k:t,s:r}))(t,r,e)}}(0,r,s,0,u,c));return l.locale=r,l.key=t,l.source=o,l}function ge(...e){const[t,n,o]=e,c={};if(!m(t)&&!r(t)&&!ke(t))throw Error(14);const u=r(t)?String(t):(ke(t),t);return r(n)?c.plural=n:m(n)?c.default=n:b(n)&&!s(n)?c.named=n:f(n)&&(c.list=n),r(o)?c.plural=o:m(o)?c.default=o:b(o)&&a(c,o),[u,c]}function be(...e){const[t,n,o,s]=e;let c,a={},u={};if(m(t)){if(!/\d{4}-\d{2}-\d{2}(T.*)?/.test(t))throw Error(16);c=new Date(t);try{c.toISOString()}catch(e){throw Error(16)}}else if("[object Date]"===g(t)){if(isNaN(t.getTime()))throw Error(15);c=t}else{if(!r(t))throw Error(14);c=t}return m(n)?a.key=n:b(n)&&(a=n),m(o)?a.locale=o:b(o)&&(u=o),b(s)&&(u=s),[a.key||"",c,a,u]}function ye(...e){const[t,n,o,s]=e;let c={},a={};if(!r(t))throw Error(14);const u=t;return m(n)?c.key=n:b(n)&&(c=n),m(o)?c.locale=o:b(o)&&(a=o),b(s)&&(a=s),[c.key||"",u,c,a]}return e.DEFAULT_MESSAGE_DATA_TYPE=O,e.MISSING_RESOLVE_VALUE="",e.NOT_REOSLVED=-1,e.VERSION=re,e.clearCompileCache=function(){me=Object.create(null)},e.clearDateTimeFormat=function(e,t,n){const r=e;for(const e in n){const n=`${t}__${e}`;r.__datetimeFormatters.has(n)&&r.__datetimeFormatters.delete(n)}},e.clearNumberFormat=function(e,t,n){const r=e;for(const e in n){const n=`${t}__${e}`;r.__numberFormatters.has(n)&&r.__numberFormatters.delete(n)}},e.compileToFunction=function(e,t={}){{const n=(t.onCacheKey||pe)(e),r=me[n];if(r)return r;let o=!1;const s=t.onError||I;t.onError=e=>{o=!0,s(e)};const{code:c}=Z(e,t),a=new Function(`return ${c}`)();return o?a:me[n]=a}},e.createCompileError=E,e.createCoreContext=function(e={}){const t=m(e.version)?e.version:re,n=m(e.locale)?e.locale:"en-US",r=f(e.fallbackLocale)||b(e.fallbackLocale)||m(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:n,s=b(e.messages)?e.messages:{[n]:{}},u=b(e.datetimeFormats)?e.datetimeFormats:{[n]:{}},l=b(e.numberFormats)?e.numberFormats:{[n]:{}},i=a({},e.modifiers||{},{upper:e=>m(e)?e.toUpperCase():e,lower:e=>m(e)?e.toLowerCase():e,capitalize:e=>m(e)?`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`:e}),h=e.pluralRules||{},g=p(e.missing)?e.missing:null,y=!d(e.missingWarn)&&!o(e.missingWarn)||e.missingWarn,x=!d(e.fallbackWarn)&&!o(e.fallbackWarn)||e.fallbackWarn,C=!!e.fallbackFormat,T=!!e.unresolving,_=p(e.postTranslation)?e.postTranslation:null,v=b(e.processor)?e.processor:null,L=!d(e.warnHtmlMessage)||e.warnHtmlMessage,w=!!e.escapeParameter,P=p(e.messageCompiler)?e.messageCompiler:oe,O=p(e.onWarn)?e.onWarn:c,F=e,N=k(F.__datetimeFormatters)?F.__datetimeFormatters:new Map,S=k(F.__numberFormatters)?F.__numberFormatters:new Map,$=k(F.__meta)?F.__meta:{};return ce++,{version:t,cid:ce,locale:n,fallbackLocale:r,messages:s,datetimeFormats:u,numberFormats:l,modifiers:i,pluralRules:h,missing:g,missingWarn:y,fallbackWarn:x,fallbackFormat:C,unresolving:T,postTranslation:_,processor:v,warnHtmlMessage:L,escapeParameter:w,messageCompiler:P,onWarn:O,__datetimeFormatters:N,__numberFormatters:S,__meta:$}},e.createCoreError=function(e){return E(e,null,void 0)},e.createMessageContext=$,e.datetime=function(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:o}=e,{__datetimeFormatters:c}=e,[u,l,i,f]=be(...t);d(i.missingWarn),d(i.fallbackWarn);const p=!!i.part,k=m(i.locale)?i.locale:e.locale,h=ue(e,o,k);if(!m(u)||""===u)return new Intl.DateTimeFormat(k).format(l);let g,y={},x=null;for(let t=0;t<h.length&&(g=h[t],y=n[g]||{},x=y[u],!b(x));t++)ae(e,u,g,0,"datetime format");if(!b(x)||!m(g))return r?-1:u;let C=`${g}__${u}`;s(f)||(C=`${C}__${JSON.stringify(f)}`);let T=c.get(C);return T||(T=new Intl.DateTimeFormat(g,a({},x,f)),c.set(C,T)),p?T.formatToParts(l):T.format(l)},e.getAdditionalMeta=()=>se,e.getDevToolsHook=function(){return X},e.getLocaleChain=ue,e.getWarnMessage=function(e,...n){return function(e,...n){return 1===n.length&&k(n[0])&&(n=n[0]),n&&n.hasOwnProperty||(n={}),e.replace(t,((e,t)=>n.hasOwnProperty(t)?n[t]:""))}(ne[e],...n)},e.handleFlatJson=function e(t){if(!k(t))return t;for(const n in t)if(i(t,n))if(n.includes(".")){const r=n.split("."),o=r.length-1;let s=t;for(let e=0;e<o;e++)r[e]in s||(s[r[e]]={}),s=s[r[e]];s[r[o]]=t[n],delete t[n],k(s[r[o]])&&e(s[r[o]])}else k(t[n])&&e(t[n]);return t},e.handleMissing=ae,e.initI18nDevTools=function(e,t,n){X&&X.emit(Q,{timestamp:Date.now(),i18n:e,version:t,meta:n})},e.isMessageFunction=ke,e.isTranslateFallbackWarn=function(e,t){return e instanceof RegExp?e.test(t):e},e.isTranslateMissingWarn=function(e,t){return e instanceof RegExp?e.test(t):e},e.number=function(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:o}=e,{__numberFormatters:c}=e,[u,l,i,f]=ye(...t);d(i.missingWarn),d(i.fallbackWarn);const p=!!i.part,k=m(i.locale)?i.locale:e.locale,h=ue(e,o,k);if(!m(u)||""===u)return new Intl.NumberFormat(k).format(l);let g,y={},x=null;for(let t=0;t<h.length&&(g=h[t],y=n[g]||{},x=y[u],!b(x));t++)ae(e,u,g,0,"number format");if(!b(x)||!m(g))return r?-1:u;let C=`${g}__${u}`;s(f)||(C=`${C}__${JSON.stringify(f)}`);let T=c.get(C);return T||(T=new Intl.NumberFormat(g,a({},x,f)),c.set(C,T)),p?T.formatToParts(l):T.format(l)},e.parse=_,e.parseDateTimeArgs=be,e.parseNumberArgs=ye,e.parseTranslateArgs=ge,e.registerMessageCompiler=function(e){oe=e},e.resolveValue=L,e.setAdditionalMeta=e=>{se=e},e.setDevToolsHook=function(e){X=e},e.translate=function(e,...t){const{fallbackFormat:n,postTranslation:o,unresolving:s,fallbackLocale:c,messages:a}=e,[l,i]=ge(...t),h=(d(i.missingWarn),d(i.fallbackWarn),d(i.escapeParameter)?i.escapeParameter:e.escapeParameter),g=!!i.resolvedMessage,b=m(i.default)||d(i.default)?d(i.default)?l:i.default:n?l:"",y=n||""!==b,x=m(i.locale)?i.locale:e.locale;h&&function(e){f(e.list)?e.list=e.list.map((e=>m(e)?u(e):e)):k(e.named)&&Object.keys(e.named).forEach((t=>{m(e.named[t])&&(e.named[t]=u(e.named[t]))}))}(i);let[C,T,_]=g?[l,x,a[x]||{}]:function(e,t,n,r,o,s){const{messages:c}=e,a=ue(e,r,n);let u,l={},i=null;const f="translate";for(let n=0;n<a.length&&(u=a[n],l=c[u]||{},null===(i=L(l,t))&&(i=l[t]),!m(i)&&!p(i));n++){const n=ae(e,t,u,0,f);n!==t&&(i=n)}return[i,u,l]}(e,l,x,c),v=l;if(g||m(C)||ke(C)||y&&(C=b,v=C),!(g||(m(C)||ke(C))&&m(T)))return s?-1:l;let w=!1;const P=ke(C)?C:he(e,l,T,C,v,(()=>{w=!0}));if(w)return C;const O=function(e,t,n){return t(n)}(0,P,$(function(e,t,n,o){const{modifiers:s,pluralRules:c}=e,a={locale:t,modifiers:s,pluralRules:c,messages:r=>{const o=L(n,r);if(m(o)){let n=!1;const s=he(e,r,t,o,r,(()=>{n=!0}));return n?de:s}return ke(o)?o:de}};e.processor&&(a.processor=e.processor);o.list&&(a.list=o.list);o.named&&(a.named=o.named);r(o.plural)&&(a.pluralIndex=o.plural);return a}(e,T,_,i)));return o?o(O):O},e.translateDevTools=ee,e.updateFallbackLocale=function(e,t,n){e.__localeChainCache=new Map,ue(e,n,t)},Object.defineProperty(e,"__esModule",{value:!0}),e}({});
