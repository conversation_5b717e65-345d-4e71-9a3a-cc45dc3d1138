{"name": "@intlify/message-resolver", "version": "9.1.9", "description": "@intlify/message-resolver", "keywords": ["i18n", "internationalization", "intlify", "resolver"], "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/intlify/vue-i18n-next/tree/master/packages/message-resolver#readme", "repository": {"type": "git", "url": "git+https://github.com/intlify/vue-i18n-next.git", "directory": "packages/message-resolver"}, "bugs": {"url": "https://github.com/intlify/vue-i18n-next/issues"}, "files": ["index.js", "dist"], "main": "index.js", "module": "dist/message-resolver.esm-bundler.js", "types": "dist/message-resolver.d.ts", "engines": {"node": ">= 10"}, "buildOptions": {"name": "IntlifyMessageResolver", "formats": ["esm-bundler", "cjs"]}, "publishConfig": {"access": "public"}, "sideEffects": false}