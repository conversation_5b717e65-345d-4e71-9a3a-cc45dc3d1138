{"name": "@intlify/runtime", "version": "9.1.9", "description": "@intlify/runtime", "keywords": ["i18n", "internationalization", "intlify", "runtime"], "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/intlify/vue-i18n-next/tree/master/packages/runtime#readme", "repository": {"type": "git", "url": "git+https://github.com/intlify/vue-i18n-next.git", "directory": "packages/runtime"}, "bugs": {"url": "https://github.com/intlify/vue-i18n-next/issues"}, "files": ["index.js", "dist"], "main": "index.js", "module": "dist/runtime.esm-bundler.js", "types": "dist/runtime.d.ts", "dependencies": {"@intlify/message-compiler": "9.1.9", "@intlify/message-resolver": "9.1.9", "@intlify/shared": "9.1.9"}, "engines": {"node": ">= 10"}, "buildOptions": {"name": "IntlifyRuntime", "formats": ["esm-bundler", "cjs"]}, "publishConfig": {"access": "public"}, "sideEffects": false}