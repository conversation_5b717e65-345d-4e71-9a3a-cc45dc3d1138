{"name": "@intlify/devtools-if", "version": "9.1.9", "description": "@intlify/devtools-if", "keywords": ["devtools", "i18n", "internationalization", "intlify"], "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/intlify/vue-i18n-next/tree/master/packages/devtools-if#readme", "repository": {"type": "git", "url": "git+https://github.com/intlify/vue-i18n-next.git", "directory": "packages/devtools-if"}, "bugs": {"url": "https://github.com/intlify/vue-i18n-next/issues"}, "files": ["index.js", "dist"], "main": "index.js", "module": "dist/devtools-if.esm-bundler.js", "types": "dist/devtools-if.d.ts", "dependencies": {"@intlify/shared": "9.1.9"}, "engines": {"node": ">= 10"}, "buildOptions": {"name": "IntlifyDevToolsIf", "formats": ["esm-bundler", "cjs"]}, "publishConfig": {"access": "public"}, "sideEffects": false}