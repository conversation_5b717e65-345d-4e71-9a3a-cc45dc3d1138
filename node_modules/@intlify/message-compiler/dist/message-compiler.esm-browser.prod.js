/*!
  * @intlify/message-compiler v9.1.9
  * (c) 2021 ka<PERSON><PERSON>
  * Released under the MIT License.
  */
const e=Object.assign,n=e=>"string"==typeof e,t={0:"Expected token: '{0}'",1:"Invalid token in placeholder: '{0}'",2:"Unterminated single quote in placeholder",3:"Unknown escape sequence: \\{0}",4:"Invalid unicode escape sequence: {0}",5:"Unbalanced closing brace",6:"Unterminated closing brace",7:"Empty placeholder",8:"Not allowed nest placeholder",9:"Invalid linked format",10:"Plural must have messages",11:"Unexpected empty linked modifier",12:"Unexpected empty linked key",13:"Unexpected lexical analysis in token: '{0}'"};function r(e,n,t={}){const{domain:r}=t,c=new SyntaxError(String(e));return c.code=e,n&&(c.location=n),c.domain=r,c}function c(e){throw e}const o={start:{line:1,column:1,offset:0},end:{line:1,column:1,offset:0}};function u(e,n,t){return{line:e,column:n,offset:t}}function s(e,n,t){const r={start:e,end:n};return null!=t&&(r.source=t),r}const i=String.fromCharCode(8232),a=String.fromCharCode(8233);function l(e){const n=e;let t=0,r=1,c=1,o=0;const u=e=>"\r"===n[e]&&"\n"===n[e+1],s=e=>n[e]===a,l=e=>n[e]===i,f=e=>u(e)||(e=>"\n"===n[e])(e)||s(e)||l(e),d=e=>u(e)||s(e)||l(e)?"\n":n[e];function p(){return o=0,f(t)&&(r++,c=0),u(t)&&t++,t++,c++,n[t]}return{index:()=>t,line:()=>r,column:()=>c,peekOffset:()=>o,charAt:d,currentChar:()=>d(t),currentPeek:()=>d(t+o),next:p,peek:function(){return u(t+o)&&o++,o++,n[t+o]},reset:function(){t=0,r=1,c=1,o=0},resetPeek:function(e=0){o=e},skipToPeek:function(){const e=t+o;for(;e!==t;)p();o=0}}}const f=void 0;function d(e,n={}){const t=!1!==n.location,r=l(e),c=()=>r.index(),o=()=>u(r.line(),r.column(),r.index()),i=o(),a=c(),d={currentType:14,offset:a,startLoc:i,endLoc:i,lastType:14,lastOffset:a,lastStartLoc:i,lastEndLoc:i,braceNest:0,inLinked:!1,text:""},p=()=>d,{onError:k}=n;function h(e,n,r){e.endLoc=o(),e.currentType=n;const c={type:n};return t&&(c.loc=s(e.startLoc,e.endLoc)),null!=r&&(c.value=r),c}const x=e=>h(e,14);function y(e,n){return e.currentChar()===n?(e.next(),n):(o(),"")}function m(e){let n="";for(;" "===e.currentPeek()||"\n"===e.currentPeek();)n+=e.currentPeek(),e.peek();return n}function b(e){const n=m(e);return e.skipToPeek(),n}function L(e){if(e===f)return!1;const n=e.charCodeAt(0);return n>=97&&n<=122||n>=65&&n<=90||95===n}function T(e,n){const{currentType:t}=n;if(2!==t)return!1;m(e);const r=function(e){if(e===f)return!1;const n=e.charCodeAt(0);return n>=48&&n<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function v(e){m(e);const n="|"===e.currentPeek();return e.resetPeek(),n}function P(e,n=!0){const t=(n=!1,r="",c=!1)=>{const o=e.currentPeek();return"{"===o?"%"!==r&&n:"@"!==o&&o?"%"===o?(e.peek(),t(n,"%",!0)):"|"===o?!("%"!==r&&!c)||!(" "===r||"\n"===r):" "===o?(e.peek(),t(!0," ",c)):"\n"!==o||(e.peek(),t(!0,"\n",c)):"%"===r||n},r=t();return n&&e.resetPeek(),r}function C(e,n){const t=e.currentChar();return t===f?f:n(t)?(e.next(),t):null}function O(e){return C(e,(e=>{const n=e.charCodeAt(0);return n>=97&&n<=122||n>=65&&n<=90||n>=48&&n<=57||95===n||36===n}))}function g(e){return C(e,(e=>{const n=e.charCodeAt(0);return n>=48&&n<=57}))}function N(e){return C(e,(e=>{const n=e.charCodeAt(0);return n>=48&&n<=57||n>=65&&n<=70||n>=97&&n<=102}))}function w(e){let n="",t="";for(;n=g(e);)t+=n;return t}function $(e){const n=e.currentChar();switch(n){case"\\":case"'":return e.next(),`\\${n}`;case"u":return S(e,n,4);case"U":return S(e,n,6);default:return o(),""}}function S(e,n,t){y(e,n);let r="";for(let n=0;n<t;n++){const n=N(e);if(!n){o(),e.currentChar();break}r+=n}return`\\${n}${r}`}function I(e){b(e);const n=y(e,"|");return b(e),n}function A(e,n){let t=null;switch(e.currentChar()){case"{":return n.braceNest>=1&&o(),e.next(),t=h(n,2,"{"),b(e),n.braceNest++,t;case"}":return n.braceNest>0&&2===n.currentType&&o(),e.next(),t=h(n,3,"}"),n.braceNest--,n.braceNest>0&&b(e),n.inLinked&&0===n.braceNest&&(n.inLinked=!1),t;case"@":return n.braceNest>0&&o(),t=E(e,n)||x(n),n.braceNest=0,t;default:let r=!0,c=!0,u=!0;if(v(e))return n.braceNest>0&&o(),t=h(n,1,I(e)),n.braceNest=0,n.inLinked=!1,t;if(n.braceNest>0&&(5===n.currentType||6===n.currentType||7===n.currentType))return o(),n.braceNest=0,U(e,n);if(r=function(e,n){const{currentType:t}=n;if(2!==t)return!1;m(e);const r=L(e.currentPeek());return e.resetPeek(),r}(e,n))return t=h(n,5,function(e){b(e);let n="",t="";for(;n=O(e);)t+=n;return e.currentChar()===f&&o(),t}(e)),b(e),t;if(c=T(e,n))return t=h(n,6,function(e){b(e);let n="";return"-"===e.currentChar()?(e.next(),n+=`-${w(e)}`):n+=w(e),e.currentChar()===f&&o(),n}(e)),b(e),t;if(u=function(e,n){const{currentType:t}=n;if(2!==t)return!1;m(e);const r="'"===e.currentPeek();return e.resetPeek(),r}(e,n))return t=h(n,7,function(e){b(e),y(e,"'");let n="",t="";const r=e=>"'"!==e&&"\n"!==e;for(;n=C(e,r);)t+="\\"===n?$(e):n;const c=e.currentChar();return"\n"===c||c===f?(o(),"\n"===c&&(e.next(),y(e,"'")),t):(y(e,"'"),t)}(e)),b(e),t;if(!r&&!c&&!u)return t=h(n,13,function(e){b(e);let n="",t="";const r=e=>"{"!==e&&"}"!==e&&" "!==e&&"\n"!==e;for(;n=C(e,r);)t+=n;return t}(e)),o(),b(e),t}return t}function E(e,n){const{currentType:t}=n;let r=null;const c=e.currentChar();switch(8!==t&&9!==t&&12!==t&&10!==t||"\n"!==c&&" "!==c||o(),c){case"@":return e.next(),r=h(n,8,"@"),n.inLinked=!0,r;case".":return b(e),e.next(),h(n,9,".");case":":return b(e),e.next(),h(n,10,":");default:return v(e)?(r=h(n,1,I(e)),n.braceNest=0,n.inLinked=!1,r):function(e,n){const{currentType:t}=n;if(8!==t)return!1;m(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,n)||function(e,n){const{currentType:t}=n;if(8!==t&&12!==t)return!1;m(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,n)?(b(e),E(e,n)):function(e,n){const{currentType:t}=n;if(9!==t)return!1;m(e);const r=L(e.currentPeek());return e.resetPeek(),r}(e,n)?(b(e),h(n,12,function(e){let n="",t="";for(;n=O(e);)t+=n;return t}(e))):function(e,n){const{currentType:t}=n;if(10!==t)return!1;const r=()=>{const n=e.currentPeek();return"{"===n?L(e.peek()):!("@"===n||"%"===n||"|"===n||":"===n||"."===n||" "===n||!n)&&("\n"===n?(e.peek(),r()):L(n))},c=r();return e.resetPeek(),c}(e,n)?(b(e),"{"===c?A(e,n)||r:h(n,11,function(e){const n=(t=!1,r)=>{const c=e.currentChar();return"{"!==c&&"%"!==c&&"@"!==c&&"|"!==c&&c?" "===c?r:"\n"===c?(r+=c,e.next(),n(t,r)):(r+=c,e.next(),n(!0,r)):r};return n(!1,"")}(e))):(8===t&&o(),n.braceNest=0,n.inLinked=!1,U(e,n))}}function U(e,n){let t={type:14};if(n.braceNest>0)return A(e,n)||x(n);if(n.inLinked)return E(e,n)||x(n);const r=e.currentChar();switch(r){case"{":return A(e,n)||x(n);case"}":return o(),e.next(),h(n,3,"}");case"@":return E(e,n)||x(n);default:if(v(e))return t=h(n,1,I(e)),n.braceNest=0,n.inLinked=!1,t;if(P(e))return h(n,0,function(e){let n="";for(;;){const t=e.currentChar();if("{"===t||"}"===t||"@"===t||"|"===t||!t)break;if("%"===t){if(!P(e))break;n+=t,e.next()}else if(" "===t||"\n"===t)if(P(e))n+=t,e.next();else{if(v(e))break;n+=t,e.next()}else n+=t,e.next()}return n}(e));if("%"===r)return e.next(),h(n,4,"%")}return t}return{nextToken:function(){const{currentType:e,offset:n,startLoc:t,endLoc:u}=d;return d.lastType=e,d.lastOffset=n,d.lastStartLoc=t,d.lastEndLoc=u,d.offset=c(),d.startLoc=o(),r.currentChar()===f?h(d,14):U(r,d)},currentOffset:c,currentPosition:o,context:p}}const p="parser",k=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function h(e,n,t){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(n||t,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function x(n={}){const t=!1!==n.location,{onError:r}=n;function c(e,n,r){const c={type:e,start:n,end:n};return t&&(c.loc={start:r,end:r}),c}function o(e,n,r,c){e.end=n,c&&(e.type=c),t&&e.loc&&(e.loc.end=r)}function u(e,n){const t=e.context(),r=c(3,t.offset,t.startLoc);return r.value=n,o(r,e.currentOffset(),e.currentPosition()),r}function s(e,n){const t=e.context(),{lastOffset:r,lastStartLoc:u}=t,s=c(5,r,u);return s.index=parseInt(n,10),e.nextToken(),o(s,e.currentOffset(),e.currentPosition()),s}function i(e,n){const t=e.context(),{lastOffset:r,lastStartLoc:u}=t,s=c(4,r,u);return s.key=n,e.nextToken(),o(s,e.currentOffset(),e.currentPosition()),s}function a(e,n){const t=e.context(),{lastOffset:r,lastStartLoc:u}=t,s=c(9,r,u);return s.value=n.replace(k,h),e.nextToken(),o(s,e.currentOffset(),e.currentPosition()),s}function l(e){const n=e.context(),t=c(6,n.offset,n.startLoc);let r=e.nextToken();if(9===r.type){const n=function(e){const n=e.nextToken(),t=e.context(),{lastOffset:r,lastStartLoc:u}=t,s=c(8,r,u);return 12!==n.type?(s.value="",o(s,r,u),{nextConsumeToken:n,node:s}):(null==n.value&&y(n),s.value=n.value||"",o(s,e.currentOffset(),e.currentPosition()),{node:s})}(e);t.modifier=n.node,r=n.nextConsumeToken||e.nextToken()}switch(10!==r.type&&y(r),r=e.nextToken(),2===r.type&&(r=e.nextToken()),r.type){case 11:null==r.value&&y(r),t.key=function(e,n){const t=e.context(),r=c(7,t.offset,t.startLoc);return r.value=n,o(r,e.currentOffset(),e.currentPosition()),r}(e,r.value||"");break;case 5:null==r.value&&y(r),t.key=i(e,r.value||"");break;case 6:null==r.value&&y(r),t.key=s(e,r.value||"");break;case 7:null==r.value&&y(r),t.key=a(e,r.value||"");break;default:const n=e.context(),u=c(7,n.offset,n.startLoc);return u.value="",o(u,n.offset,n.startLoc),t.key=u,o(t,n.offset,n.startLoc),{nextConsumeToken:r,node:t}}return o(t,e.currentOffset(),e.currentPosition()),{node:t}}function f(e){const n=e.context(),t=c(2,1===n.currentType?e.currentOffset():n.offset,1===n.currentType?n.endLoc:n.startLoc);t.items=[];let r=null;do{const n=r||e.nextToken();switch(r=null,n.type){case 0:null==n.value&&y(n),t.items.push(u(e,n.value||""));break;case 6:null==n.value&&y(n),t.items.push(s(e,n.value||""));break;case 5:null==n.value&&y(n),t.items.push(i(e,n.value||""));break;case 7:null==n.value&&y(n),t.items.push(a(e,n.value||""));break;case 8:const c=l(e);t.items.push(c.node),r=c.nextConsumeToken||null}}while(14!==n.currentType&&1!==n.currentType);return o(t,1===n.currentType?n.lastOffset:e.currentOffset(),1===n.currentType?n.lastEndLoc:e.currentPosition()),t}function p(e){const n=e.context(),{offset:t,startLoc:r}=n,u=f(e);return 14===n.currentType?u:function(e,n,t,r){const u=e.context();let s=0===r.items.length;const i=c(1,n,t);i.cases=[],i.cases.push(r);do{const n=f(e);s||(s=0===n.items.length),i.cases.push(n)}while(14!==u.currentType);return o(i,e.currentOffset(),e.currentPosition()),i}(e,t,r,u)}return{parse:function(r){const u=d(r,e({},n)),s=u.context(),i=c(0,s.offset,s.startLoc);return t&&i.loc&&(i.loc.source=r),i.body=p(u),o(i,u.currentOffset(),u.currentPosition()),i}}}function y(e){if(14===e.type)return"EOF";const n=(e.value||"").replace(/\r?\n/gu,"\\n");return n.length>10?n.slice(0,9)+"…":n}function m(e,n){for(let t=0;t<e.length;t++)b(e[t],n)}function b(e,n){switch(e.type){case 1:m(e.cases,n),n.helper("plural");break;case 2:m(e.items,n);break;case 6:b(e.key,n),n.helper("linked");break;case 5:n.helper("interpolate"),n.helper("list");break;case 4:n.helper("interpolate"),n.helper("named")}}function L(e,n={}){const t=function(e,n={}){const t={ast:e,helpers:new Set};return{context:()=>t,helper:e=>(t.helpers.add(e),e)}}(e);t.helper("normalize"),e.body&&b(e.body,t);const r=t.context();e.helpers=Array.from(r.helpers)}function T(e,n){const{helper:t}=e;switch(n.type){case 0:!function(e,n){n.body?T(e,n.body):e.push("null")}(e,n);break;case 1:!function(e,n){const{helper:t,needIndent:r}=e;if(n.cases.length>1){e.push(`${t("plural")}([`),e.indent(r());const c=n.cases.length;for(let t=0;t<c&&(T(e,n.cases[t]),t!==c-1);t++)e.push(", ");e.deindent(r()),e.push("])")}}(e,n);break;case 2:!function(e,n){const{helper:t,needIndent:r}=e;e.push(`${t("normalize")}([`),e.indent(r());const c=n.items.length;for(let t=0;t<c&&(T(e,n.items[t]),t!==c-1);t++)e.push(", ");e.deindent(r()),e.push("])")}(e,n);break;case 6:!function(e,n){const{helper:t}=e;e.push(`${t("linked")}(`),T(e,n.key),n.modifier&&(e.push(", "),T(e,n.modifier)),e.push(")")}(e,n);break;case 8:case 7:e.push(JSON.stringify(n.value),n);break;case 5:e.push(`${t("interpolate")}(${t("list")}(${n.index}))`,n);break;case 4:e.push(`${t("interpolate")}(${t("named")}(${JSON.stringify(n.key)}))`,n);break;case 9:case 3:e.push(JSON.stringify(n.value),n)}}function v(t,r={}){const c=e({},r),o=x(c).parse(t);return L(o,c),((e,t={})=>{const r=n(t.mode)?t.mode:"normal",c=n(t.filename)?t.filename:"message.intl",o=t.needIndent?t.needIndent:"arrow"!==r,u=e.helpers||[],s=function(e,n){const{filename:t,breakLineCode:r,needIndent:c}=n,o={source:e.loc.source,filename:t,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:r,needIndent:c,indentLevel:0};function u(e,n){o.code+=e}function s(e,n=!0){const t=n?r:"";u(c?t+"  ".repeat(e):t)}return{context:()=>o,push:u,indent:function(e=!0){const n=++o.indentLevel;e&&s(n)},deindent:function(e=!0){const n=--o.indentLevel;e&&s(n)},newline:function(){s(o.indentLevel)},helper:e=>`_${e}`,needIndent:()=>o.needIndent}}(e,{mode:r,filename:c,sourceMap:!!t.sourceMap,breakLineCode:null!=t.breakLineCode?t.breakLineCode:"arrow"===r?";":"\n",needIndent:o});s.push("normal"===r?"function __msg__ (ctx) {":"(ctx) => {"),s.indent(o),u.length>0&&(s.push(`const { ${u.map((e=>`${e}: _${e}`)).join(", ")} } = ctx`),s.newline()),s.push("return "),T(s,e),s.deindent(o),s.push("}");const{code:i,map:a}=s.context();return{ast:e,code:i,map:a?a.toJSON():void 0}})(o,c)}export{p as ERROR_DOMAIN,o as LocationStub,v as baseCompile,r as createCompileError,s as createLocation,x as createParser,u as createPosition,c as defaultOnError,t as errorMessages};
