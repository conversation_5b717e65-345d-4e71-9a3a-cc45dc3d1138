/*!
  * @intlify/message-compiler v9.1.9
  * (c) 2021 ka<PERSON><PERSON>
  * Released under the MIT License.
  */
var IntlifyMessageCompiler=function(e){"use strict";const n=Object.assign,t=e=>"string"==typeof e,r={0:"Expected token: '{0}'",1:"Invalid token in placeholder: '{0}'",2:"Unterminated single quote in placeholder",3:"Unknown escape sequence: \\{0}",4:"Invalid unicode escape sequence: {0}",5:"Unbalanced closing brace",6:"Unterminated closing brace",7:"Empty placeholder",8:"Not allowed nest placeholder",9:"Invalid linked format",10:"Plural must have messages",11:"Unexpected empty linked modifier",12:"Unexpected empty linked key",13:"Unexpected lexical analysis in token: '{0}'"};function c(e,n,t={}){const{domain:r}=t,c=new SyntaxError(String(e));return c.code=e,n&&(c.location=n),c.domain=r,c}function o(e,n,t){return{line:e,column:n,offset:t}}function u(e,n,t){const r={start:e,end:n};return null!=t&&(r.source=t),r}const s=" ",i="\n",a=String.fromCharCode(8232),l=String.fromCharCode(8233);function f(e){const n=e;let t=0,r=1,c=1,o=0;const u=e=>"\r"===n[e]&&n[e+1]===i,s=e=>n[e]===l,f=e=>n[e]===a,d=e=>u(e)||(e=>n[e]===i)(e)||s(e)||f(e),p=e=>u(e)||s(e)||f(e)?i:n[e];function k(){return o=0,d(t)&&(r++,c=0),u(t)&&t++,t++,c++,n[t]}return{index:()=>t,line:()=>r,column:()=>c,peekOffset:()=>o,charAt:p,currentChar:()=>p(t),currentPeek:()=>p(t+o),next:k,peek:function(){return u(t+o)&&o++,o++,n[t+o]},reset:function(){t=0,r=1,c=1,o=0},resetPeek:function(e=0){o=e},skipToPeek:function(){const e=t+o;for(;e!==t;)k();o=0}}}const d=void 0;function p(e,n={}){const t=!1!==n.location,r=f(e),c=()=>r.index(),a=()=>o(r.line(),r.column(),r.index()),l=a(),p=c(),k={currentType:14,offset:p,startLoc:l,endLoc:l,lastType:14,lastOffset:p,lastStartLoc:l,lastEndLoc:l,braceNest:0,inLinked:!1,text:""},h=()=>k,{onError:x}=n;function y(e,n,r){e.endLoc=a(),e.currentType=n;const c={type:n};return t&&(c.loc=u(e.startLoc,e.endLoc)),null!=r&&(c.value=r),c}const m=e=>y(e,14);function b(e,n){return e.currentChar()===n?(e.next(),n):(a(),"")}function L(e){let n="";for(;e.currentPeek()===s||e.currentPeek()===i;)n+=e.currentPeek(),e.peek();return n}function P(e){const n=L(e);return e.skipToPeek(),n}function v(e){if(e===d)return!1;const n=e.charCodeAt(0);return n>=97&&n<=122||n>=65&&n<=90||95===n}function T(e,n){const{currentType:t}=n;if(2!==t)return!1;L(e);const r=function(e){if(e===d)return!1;const n=e.charCodeAt(0);return n>=48&&n<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function C(e){L(e);const n="|"===e.currentPeek();return e.resetPeek(),n}function O(e,n=!0){const t=(n=!1,r="",c=!1)=>{const o=e.currentPeek();return"{"===o?"%"!==r&&n:"@"!==o&&o?"%"===o?(e.peek(),t(n,"%",!0)):"|"===o?!("%"!==r&&!c)||!(r===s||r===i):o===s?(e.peek(),t(!0,s,c)):o!==i||(e.peek(),t(!0,i,c)):"%"===r||n},r=t();return n&&e.resetPeek(),r}function g(e,n){const t=e.currentChar();return t===d?d:n(t)?(e.next(),t):null}function N(e){return g(e,(e=>{const n=e.charCodeAt(0);return n>=97&&n<=122||n>=65&&n<=90||n>=48&&n<=57||95===n||36===n}))}function w(e){return g(e,(e=>{const n=e.charCodeAt(0);return n>=48&&n<=57}))}function S(e){return g(e,(e=>{const n=e.charCodeAt(0);return n>=48&&n<=57||n>=65&&n<=70||n>=97&&n<=102}))}function $(e){let n="",t="";for(;n=w(e);)t+=n;return t}function I(e){const n=e.currentChar();switch(n){case"\\":case"'":return e.next(),`\\${n}`;case"u":return E(e,n,4);case"U":return E(e,n,6);default:return a(),""}}function E(e,n,t){b(e,n);let r="";for(let n=0;n<t;n++){const n=S(e);if(!n){a(),e.currentChar();break}r+=n}return`\\${n}${r}`}function A(e){P(e);const n=b(e,"|");return P(e),n}function U(e,n){let t=null;switch(e.currentChar()){case"{":return n.braceNest>=1&&a(),e.next(),t=y(n,2,"{"),P(e),n.braceNest++,t;case"}":return n.braceNest>0&&2===n.currentType&&a(),e.next(),t=y(n,3,"}"),n.braceNest--,n.braceNest>0&&P(e),n.inLinked&&0===n.braceNest&&(n.inLinked=!1),t;case"@":return n.braceNest>0&&a(),t=_(e,n)||m(n),n.braceNest=0,t;default:let r=!0,c=!0,o=!0;if(C(e))return n.braceNest>0&&a(),t=y(n,1,A(e)),n.braceNest=0,n.inLinked=!1,t;if(n.braceNest>0&&(5===n.currentType||6===n.currentType||7===n.currentType))return a(),n.braceNest=0,M(e,n);if(r=function(e,n){const{currentType:t}=n;if(2!==t)return!1;L(e);const r=v(e.currentPeek());return e.resetPeek(),r}(e,n))return t=y(n,5,function(e){P(e);let n="",t="";for(;n=N(e);)t+=n;return e.currentChar()===d&&a(),t}(e)),P(e),t;if(c=T(e,n))return t=y(n,6,function(e){P(e);let n="";return"-"===e.currentChar()?(e.next(),n+=`-${$(e)}`):n+=$(e),e.currentChar()===d&&a(),n}(e)),P(e),t;if(o=function(e,n){const{currentType:t}=n;if(2!==t)return!1;L(e);const r="'"===e.currentPeek();return e.resetPeek(),r}(e,n))return t=y(n,7,function(e){P(e),b(e,"'");let n="",t="";const r=e=>"'"!==e&&e!==i;for(;n=g(e,r);)t+="\\"===n?I(e):n;const c=e.currentChar();return c===i||c===d?(a(),c===i&&(e.next(),b(e,"'")),t):(b(e,"'"),t)}(e)),P(e),t;if(!r&&!c&&!o)return t=y(n,13,function(e){P(e);let n="",t="";const r=e=>"{"!==e&&"}"!==e&&e!==s&&e!==i;for(;n=g(e,r);)t+=n;return t}(e)),a(),P(e),t}return t}function _(e,n){const{currentType:t}=n;let r=null;const c=e.currentChar();switch(8!==t&&9!==t&&12!==t&&10!==t||c!==i&&c!==s||a(),c){case"@":return e.next(),r=y(n,8,"@"),n.inLinked=!0,r;case".":return P(e),e.next(),y(n,9,".");case":":return P(e),e.next(),y(n,10,":");default:return C(e)?(r=y(n,1,A(e)),n.braceNest=0,n.inLinked=!1,r):function(e,n){const{currentType:t}=n;if(8!==t)return!1;L(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,n)||function(e,n){const{currentType:t}=n;if(8!==t&&12!==t)return!1;L(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,n)?(P(e),_(e,n)):function(e,n){const{currentType:t}=n;if(9!==t)return!1;L(e);const r=v(e.currentPeek());return e.resetPeek(),r}(e,n)?(P(e),y(n,12,function(e){let n="",t="";for(;n=N(e);)t+=n;return t}(e))):function(e,n){const{currentType:t}=n;if(10!==t)return!1;const r=()=>{const n=e.currentPeek();return"{"===n?v(e.peek()):!("@"===n||"%"===n||"|"===n||":"===n||"."===n||n===s||!n)&&(n===i?(e.peek(),r()):v(n))},c=r();return e.resetPeek(),c}(e,n)?(P(e),"{"===c?U(e,n)||r:y(n,11,function(e){const n=(t=!1,r)=>{const c=e.currentChar();return"{"!==c&&"%"!==c&&"@"!==c&&"|"!==c&&c?c===s?r:c===i?(r+=c,e.next(),n(t,r)):(r+=c,e.next(),n(!0,r)):r};return n(!1,"")}(e))):(8===t&&a(),n.braceNest=0,n.inLinked=!1,M(e,n))}}function M(e,n){let t={type:14};if(n.braceNest>0)return U(e,n)||m(n);if(n.inLinked)return _(e,n)||m(n);const r=e.currentChar();switch(r){case"{":return U(e,n)||m(n);case"}":return a(),e.next(),y(n,3,"}");case"@":return _(e,n)||m(n);default:if(C(e))return t=y(n,1,A(e)),n.braceNest=0,n.inLinked=!1,t;if(O(e))return y(n,0,function(e){let n="";for(;;){const t=e.currentChar();if("{"===t||"}"===t||"@"===t||"|"===t||!t)break;if("%"===t){if(!O(e))break;n+=t,e.next()}else if(t===s||t===i)if(O(e))n+=t,e.next();else{if(C(e))break;n+=t,e.next()}else n+=t,e.next()}return n}(e));if("%"===r)return e.next(),y(n,4,"%")}return t}return{nextToken:function(){const{currentType:e,offset:n,startLoc:t,endLoc:o}=k;return k.lastType=e,k.lastOffset=n,k.lastStartLoc=t,k.lastEndLoc=o,k.offset=c(),k.startLoc=a(),r.currentChar()===d?y(k,14):M(r,k)},currentOffset:c,currentPosition:a,context:h}}const k="parser",h=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function x(e,n,t){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(n||t,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function y(e={}){const t=!1!==e.location,{onError:r}=e;function c(e,n,r){const c={type:e,start:n,end:n};return t&&(c.loc={start:r,end:r}),c}function o(e,n,r,c){e.end=n,c&&(e.type=c),t&&e.loc&&(e.loc.end=r)}function u(e,n){const t=e.context(),r=c(3,t.offset,t.startLoc);return r.value=n,o(r,e.currentOffset(),e.currentPosition()),r}function s(e,n){const t=e.context(),{lastOffset:r,lastStartLoc:u}=t,s=c(5,r,u);return s.index=parseInt(n,10),e.nextToken(),o(s,e.currentOffset(),e.currentPosition()),s}function i(e,n){const t=e.context(),{lastOffset:r,lastStartLoc:u}=t,s=c(4,r,u);return s.key=n,e.nextToken(),o(s,e.currentOffset(),e.currentPosition()),s}function a(e,n){const t=e.context(),{lastOffset:r,lastStartLoc:u}=t,s=c(9,r,u);return s.value=n.replace(h,x),e.nextToken(),o(s,e.currentOffset(),e.currentPosition()),s}function l(e){const n=e.context(),t=c(6,n.offset,n.startLoc);let r=e.nextToken();if(9===r.type){const n=function(e){const n=e.nextToken(),t=e.context(),{lastOffset:r,lastStartLoc:u}=t,s=c(8,r,u);return 12!==n.type?(s.value="",o(s,r,u),{nextConsumeToken:n,node:s}):(null==n.value&&m(n),s.value=n.value||"",o(s,e.currentOffset(),e.currentPosition()),{node:s})}(e);t.modifier=n.node,r=n.nextConsumeToken||e.nextToken()}switch(10!==r.type&&m(r),r=e.nextToken(),2===r.type&&(r=e.nextToken()),r.type){case 11:null==r.value&&m(r),t.key=function(e,n){const t=e.context(),r=c(7,t.offset,t.startLoc);return r.value=n,o(r,e.currentOffset(),e.currentPosition()),r}(e,r.value||"");break;case 5:null==r.value&&m(r),t.key=i(e,r.value||"");break;case 6:null==r.value&&m(r),t.key=s(e,r.value||"");break;case 7:null==r.value&&m(r),t.key=a(e,r.value||"");break;default:const n=e.context(),u=c(7,n.offset,n.startLoc);return u.value="",o(u,n.offset,n.startLoc),t.key=u,o(t,n.offset,n.startLoc),{nextConsumeToken:r,node:t}}return o(t,e.currentOffset(),e.currentPosition()),{node:t}}function f(e){const n=e.context(),t=c(2,1===n.currentType?e.currentOffset():n.offset,1===n.currentType?n.endLoc:n.startLoc);t.items=[];let r=null;do{const n=r||e.nextToken();switch(r=null,n.type){case 0:null==n.value&&m(n),t.items.push(u(e,n.value||""));break;case 6:null==n.value&&m(n),t.items.push(s(e,n.value||""));break;case 5:null==n.value&&m(n),t.items.push(i(e,n.value||""));break;case 7:null==n.value&&m(n),t.items.push(a(e,n.value||""));break;case 8:const c=l(e);t.items.push(c.node),r=c.nextConsumeToken||null}}while(14!==n.currentType&&1!==n.currentType);return o(t,1===n.currentType?n.lastOffset:e.currentOffset(),1===n.currentType?n.lastEndLoc:e.currentPosition()),t}function d(e){const n=e.context(),{offset:t,startLoc:r}=n,u=f(e);return 14===n.currentType?u:function(e,n,t,r){const u=e.context();let s=0===r.items.length;const i=c(1,n,t);i.cases=[],i.cases.push(r);do{const n=f(e);s||(s=0===n.items.length),i.cases.push(n)}while(14!==u.currentType);return o(i,e.currentOffset(),e.currentPosition()),i}(e,t,r,u)}return{parse:function(r){const u=p(r,n({},e)),s=u.context(),i=c(0,s.offset,s.startLoc);return t&&i.loc&&(i.loc.source=r),i.body=d(u),o(i,u.currentOffset(),u.currentPosition()),i}}}function m(e){if(14===e.type)return"EOF";const n=(e.value||"").replace(/\r?\n/gu,"\\n");return n.length>10?n.slice(0,9)+"…":n}function b(e,n){for(let t=0;t<e.length;t++)L(e[t],n)}function L(e,n){switch(e.type){case 1:b(e.cases,n),n.helper("plural");break;case 2:b(e.items,n);break;case 6:L(e.key,n),n.helper("linked");break;case 5:n.helper("interpolate"),n.helper("list");break;case 4:n.helper("interpolate"),n.helper("named")}}function P(e,n={}){const t=function(e,n={}){const t={ast:e,helpers:new Set};return{context:()=>t,helper:e=>(t.helpers.add(e),e)}}(e);t.helper("normalize"),e.body&&L(e.body,t);const r=t.context();e.helpers=Array.from(r.helpers)}function v(e,n){const{helper:t}=e;switch(n.type){case 0:!function(e,n){n.body?v(e,n.body):e.push("null")}(e,n);break;case 1:!function(e,n){const{helper:t,needIndent:r}=e;if(n.cases.length>1){e.push(`${t("plural")}([`),e.indent(r());const c=n.cases.length;for(let t=0;t<c&&(v(e,n.cases[t]),t!==c-1);t++)e.push(", ");e.deindent(r()),e.push("])")}}(e,n);break;case 2:!function(e,n){const{helper:t,needIndent:r}=e;e.push(`${t("normalize")}([`),e.indent(r());const c=n.items.length;for(let t=0;t<c&&(v(e,n.items[t]),t!==c-1);t++)e.push(", ");e.deindent(r()),e.push("])")}(e,n);break;case 6:!function(e,n){const{helper:t}=e;e.push(`${t("linked")}(`),v(e,n.key),n.modifier&&(e.push(", "),v(e,n.modifier)),e.push(")")}(e,n);break;case 8:case 7:e.push(JSON.stringify(n.value),n);break;case 5:e.push(`${t("interpolate")}(${t("list")}(${n.index}))`,n);break;case 4:e.push(`${t("interpolate")}(${t("named")}(${JSON.stringify(n.key)}))`,n);break;case 9:case 3:e.push(JSON.stringify(n.value),n)}}return e.ERROR_DOMAIN=k,e.LocationStub={start:{line:1,column:1,offset:0},end:{line:1,column:1,offset:0}},e.baseCompile=function(e,r={}){const c=n({},r),o=y(c).parse(e);return P(o,c),((e,n={})=>{const r=t(n.mode)?n.mode:"normal",c=t(n.filename)?n.filename:"message.intl",o=n.needIndent?n.needIndent:"arrow"!==r,u=e.helpers||[],s=function(e,n){const{filename:t,breakLineCode:r,needIndent:c}=n,o={source:e.loc.source,filename:t,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:r,needIndent:c,indentLevel:0};function u(e,n){o.code+=e}function s(e,n=!0){const t=n?r:"";u(c?t+"  ".repeat(e):t)}return{context:()=>o,push:u,indent:function(e=!0){const n=++o.indentLevel;e&&s(n)},deindent:function(e=!0){const n=--o.indentLevel;e&&s(n)},newline:function(){s(o.indentLevel)},helper:e=>`_${e}`,needIndent:()=>o.needIndent}}(e,{mode:r,filename:c,sourceMap:!!n.sourceMap,breakLineCode:null!=n.breakLineCode?n.breakLineCode:"arrow"===r?";":"\n",needIndent:o});s.push("normal"===r?"function __msg__ (ctx) {":"(ctx) => {"),s.indent(o),u.length>0&&(s.push(`const { ${u.map((e=>`${e}: _${e}`)).join(", ")} } = ctx`),s.newline()),s.push("return "),v(s,e),s.deindent(o),s.push("}");const{code:i,map:a}=s.context();return{ast:e,code:i,map:a?a.toJSON():void 0}})(o,c)},e.createCompileError=c,e.createLocation=u,e.createParser=y,e.createPosition=o,e.defaultOnError=function(e){throw e},e.errorMessages=r,Object.defineProperty(e,"__esModule",{value:!0}),e}({});
