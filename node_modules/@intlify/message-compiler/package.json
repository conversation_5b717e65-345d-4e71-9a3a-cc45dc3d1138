{"name": "@intlify/message-compiler", "version": "9.1.9", "description": "@intlify/message-compiler", "keywords": ["compiler", "i18n", "internationalization", "intlify", "message-format"], "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/intlify/vue-i18n-next/tree/master/packages/message-compiler#readme", "repository": {"type": "git", "url": "git+https://github.com/intlify/vue-i18n-next.git", "directory": "packages/message-compiler"}, "bugs": {"url": "https://github.com/intlify/vue-i18n-next/issues"}, "files": ["index.js", "dist"], "main": "index.js", "module": "dist/message-compiler.esm-bundler.js", "unpkg": "dist/message-compiler.global.js", "jsdelivr": "dist/message-compiler.global.js", "types": "dist/message-compiler.d.ts", "dependencies": {"@intlify/message-resolver": "9.1.9", "@intlify/shared": "9.1.9", "source-map": "0.6.1"}, "engines": {"node": ">= 10"}, "buildOptions": {"name": "IntlifyMessageCompiler", "formats": ["esm-bundler", "esm-browser", "cjs", "global"]}, "publishConfig": {"access": "public"}, "sideEffects": false}