{"name": "image-generate", "version": "3.0.0", "private": true, "description": "基于gpt-4o的图片风格转换项目", "templateInfo": {"name": "mobx", "typescript": true, "css": "Less", "framework": "React"}, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "dev:weapp:sentry": "TARO_APP_SENTRY_UPLOAD_SOURCEMAPS=true npm run dev:weapp", "build:weapp:sentry": "npm run build:weapp && node scripts/upload-sourcemaps.js", "upload:sourcemaps": "node scripts/upload-sourcemaps.js", "format": "prettier --write ."}, "browserslist": ["defaults and fully supports es6-module", "maintained node versions"], "author": "", "dependencies": {"@babel/runtime": "^7.21.5", "@tarojs/components": "4.0.9", "@tarojs/helper": "4.0.9", "@tarojs/plugin-framework-react": "4.0.9", "@tarojs/plugin-platform-alipay": "4.0.9", "@tarojs/plugin-platform-h5": "4.0.9", "@tarojs/plugin-platform-jd": "4.0.9", "@tarojs/plugin-platform-qq": "4.0.9", "@tarojs/plugin-platform-swan": "4.0.9", "@tarojs/plugin-platform-tt": "4.0.9", "@tarojs/plugin-platform-weapp": "4.0.9", "@tarojs/react": "4.0.9", "@tarojs/runtime": "4.0.9", "@tarojs/shared": "4.0.9", "@tarojs/taro": "4.0.9", "@tarojs/webpack5-runner": "^4.0.10", "dayjs": "^1.11.13", "mobx": "6", "mobx-react": "7", "react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@babel/core": "^7.8.0", "@babel/plugin-proposal-class-properties": "7.14.5", "@babel/preset-react": "^7.24.1", "@sentry/cli": "^2.28.6", "@tarojs/cli": "4.0.9", "@tarojs/vite-runner": "4.0.9", "@types/react": "^18.0.0", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "@vitejs/plugin-react": "^4.1.0", "autoprefixer": "^10.4.21", "babel-preset-taro": "4.0.9", "dotenv": "^17.1.0", "eslint": "^8.12.0", "eslint-config-taro": "4.0.9", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "less": "^4.1.3", "postcss": "^8.5.3", "postcss-import": "^16.1.0", "prettier": "^3.5.3", "react-refresh": "^0.11.0", "stylelint": "^14.4.0", "terser": "^5.16.8", "typescript": "^5.1.0", "vite": "^4.2.0"}}