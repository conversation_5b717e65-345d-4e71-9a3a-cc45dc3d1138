/* 全局样式 */
page {
  background-color: #0a0e17;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans',
    'Helvetica Neue', sans-serif;
  color: #e6e6e6;
  font-size: 15px;
  box-sizing: border-box;
  line-height: 1.5;
  background-color: #0a0e17;
}

/* 主题颜色 */
:root {
  --primary-color: #0fe0ff;
  --secondary-color: #9059e7;
  --primary-gradient: linear-gradient(135deg, #0fe0ff 0%, #03a1ff 100%);
  --background-color: #0a0e17;
  --card-color: #171c26;
  --text-color: #e6e6e6;
  --text-color-light: #a3a3a3;
  --border-color: #2a2f3a;

  /* 间距变量 */
  --space-xs: 8px;
  --space-sm: 12px;
  --space-md: 20px;
  --space-lg: 28px;
  --space-xl: 32px;

  /* 圆角变量 */
  --radius-sm: 6px;
  --radius-md: 10px;
  --radius-lg: 16px;
  --radius-circle: 50%;

  /* 阴影变量 */
  --shadow-sm: 0 4px 12px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 8px 16px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 12px 24px rgba(0, 0, 0, 0.5);
  --neon-shadow: 0 0 10px rgba(15, 224, 255, 0.5), 0 0 20px rgba(15, 224, 255, 0.3);
}

/* 自定义按钮样式 */
button[type='primary'] {
  background: var(--primary-gradient) !important;
  border: none !important;
  font-size: 16px !important;
  height: 48px !important;
  line-height: 48px !important;
  border-radius: var(--radius-lg) !important;
  font-weight: 500 !important;
  box-shadow: var(--neon-shadow) !important;
}

button[type='primary'][disabled] {
  opacity: 0.6 !important;
  box-shadow: none !important;
}

button[type='default'] {
  color: var(--primary-color) !important;
  border: 1px solid var(--primary-color) !important;
  background-color: rgba(15, 224, 255, 0.1) !important;
  font-size: 16px !important;
  height: 48px !important;
  line-height: 48px !important;
  border-radius: var(--radius-lg) !important;
  font-weight: 500 !important;
}

/* 修改Taro自带组件的样式 */
.taro-text {
  display: inline;
}

/* 卡片通用样式 */
.card {
  background-color: var(--card-color);
  border-radius: var(--radius-md);
  padding: var(--space-md);
  margin-bottom: var(--space-md);
  box-shadow: var(--shadow-md);
}

/* 修复可能的兼容性问题 */
view,
text,
image,
button,
input {
  box-sizing: border-box;
}

/* 通用文本样式 */
.text-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: var(--space-sm);
}

.text-subtitle {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: var(--space-xs);
}

.text-body {
  font-size: 15px;
  color: var(--text-color);
}

.text-caption {
  font-size: 13px;
  color: var(--text-color-light);
}

/* 统一间距类 */
.margin-xs {
  margin: var(--space-xs);
}
.margin-sm {
  margin: var(--space-sm);
}
.margin-md {
  margin: var(--space-md);
}
.margin-lg {
  margin: var(--space-lg);
}
.margin-xl {
  margin: var(--space-xl);
}

.padding-xs {
  padding: var(--space-xs);
}
.padding-sm {
  padding: var(--space-sm);
}
.padding-md {
  padding: var(--space-md);
}
.padding-lg {
  padding: var(--space-lg);
}
.padding-xl {
  padding: var(--space-xl);
}

/* 统一圆角尺寸 */
.radius-sm {
  border-radius: var(--radius-sm);
}

.radius-md {
  border-radius: var(--radius-md);
}

.radius-lg {
  border-radius: var(--radius-lg);
}

.radius-circle {
  border-radius: var(--radius-circle);
}

/* 阴影效果 */
.shadow-sm {
  box-shadow: var(--shadow-sm);
}

.shadow-md {
  box-shadow: var(--shadow-md);
}

.shadow-lg {
  box-shadow: var(--shadow-lg);
}

/* 加载动画 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-float {
  animation: float 20s ease-in-out infinite;
}

.animate-float-reverse {
  animation: float 15s ease-in-out infinite reverse;
}

.animate-float-delay {
  animation: float 18s ease-in-out 2s infinite;
}

.text-shadow-lg {
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.35);
}
