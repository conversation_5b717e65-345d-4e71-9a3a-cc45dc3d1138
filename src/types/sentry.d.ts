// Sentry 相关的类型声明

declare module '@sentry/browser' {
  export interface SentryUser {
    id?: string
    email?: string
    username?: string
    [key: string]: any
  }

  export interface SentryContext {
    [key: string]: any
  }

  export interface SentryBreadcrumb {
    message: string
    category?: string
    level?: 'info' | 'warning' | 'error'
    data?: any
    timestamp?: number
  }
}

// 扩展全局类型，支持小程序环境
declare global {
  const getCurrentPages: () => any[]

  interface WxMiniProgram {
    onError?: (callback: (error: string) => void) => void
    onUnhandledRejection?: (
      callback: (res: { reason: string; promise: Promise<any> }) => void
    ) => void
  }

  const wx: WxMiniProgram
}

export {}
