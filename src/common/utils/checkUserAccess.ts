import type { UserInfo } from '@/common/apis/user/types'

/**
 * 用户访问权限操作
 * 1. 刷新
 * 2. 购买积分
 */
export enum AccessAction {
  REFRESH = 'refresh',
  PURCHASE = 'purchase',
}

/**
 * 检查用户是否可以使用服务
 * @param userInfo 用户信息
 * @returns { canUse: boolean, reason?: string } 是否可以使用及原因
 */
export const checkUserAccess = (userInfo: UserInfo | null) => {
  if (!userInfo) {
    return {
      canUse: false,
      reason: '用户信息获取失败',
      action: AccessAction.REFRESH,
    }
  }

  // 如果是活跃VIP用户，直接允许使用
  if (userInfo.isActiveVip) {
    return {
      canUse: true,
    }
  }

  // 非VIP用户需要检查积分
  if (!userInfo.points || userInfo.points < 3) {
    return {
      canUse: false,
      reason: '积分不足',
      action: AccessAction.PURCHASE,
    }
  }

  return {
    canUse: true,
  }
}
