import Taro from '@tarojs/taro'
import type { UserInfo } from '@/common/apis/user/types'

/**
 * 购买权限检查参数
 */
export interface PurchasePermissionParams {
  userInfo: UserInfo | null
  showVipCard: boolean
  allowedUserIds: number[] | null
}

/**
 * 购买权限检查结果
 */
export interface PurchasePermissionResult {
  canPurchase: boolean
  reason?: string
}

/**
 * 判断是否为安卓平台
 */
const isAndroidPlatform = (): boolean => {
  try {
    const systemInfo = Taro.getDeviceInfo()

    return systemInfo.platform === 'android'
  } catch (error) {
    console.error('获取设备信息失败:', error)
    return false
  }
}

/**
 * 判断是否为体验版
 * 自动判断当前应用是否为体验版
 * @returns boolean 是否为体验版
 */
export const getIsTrialVersion = (): boolean => {
  try {
    // 1. 通过小程序版本信息判断
    const accountInfo = Taro.getAccountInfoSync()
    const miniProgram = accountInfo.miniProgram

    console.log('miniProgram', miniProgram)

    // 如果是开发版或体验版，则认为是体验版
    if (miniProgram.envVersion === 'develop' || miniProgram.envVersion === 'trial') {
      return true
    }
  } catch (error) {
    console.error('判断体验版失败:', error)
  }

  return false // 默认不是体验版
}

/**
 * 检查用户是否有权限通过积分卡按钮进入购买页面
 *
 * 判断规则（按优先级顺序）：
 * 1. 体验版都不允许
 * 2. 安卓平台都允许
 * 3. 如果没有用户ID，允许（原有逻辑）
 * 4. 如果allowedUserIds为null，则根据showVipCard判断
 * 5. 检查用户ID是否在允许列表中，并且showVipCard为true
 *
 * @param params 检查参数
 * @returns 检查结果
 */
export const checkPurchasePermission = (
  params: PurchasePermissionParams
): PurchasePermissionResult => {
  const { userInfo, showVipCard, allowedUserIds } = params

  // 1. 安卓平台都允许
  if (isAndroidPlatform()) {
    return {
      canPurchase: true,
      reason: '安卓平台允许购买',
    }
  }

  // 2. 体验版都不允许
  if (getIsTrialVersion()) {
    return {
      canPurchase: false,
      reason: '体验版不支持购买功能',
    }
  }

  // 3. 如果没有用户ID，则允许
  if (!userInfo?.id) {
    return {
      canPurchase: true,
      reason: '未获取到用户ID，默认允许',
    }
  }

  // 4. allowedUserIds为null表示不限制，则根据showVipCard判断
  if (allowedUserIds === null) {
    return {
      canPurchase: showVipCard,
      reason: showVipCard ? 'VIP卡片显示，允许购买' : 'VIP卡片不显示，不允许购买',
    }
  }

  // 5. 检查用户ID是否在允许列表中，并且showVipCard为true
  const isUserAllowed = allowedUserIds.includes(userInfo.id)
  const canPurchase = isUserAllowed && showVipCard

  return {
    canPurchase,
    reason: !isUserAllowed
      ? '用户不在允许购买的名单中'
      : !showVipCard
        ? 'VIP卡片不显示，不允许购买'
        : '用户有购买权限',
  }
}
