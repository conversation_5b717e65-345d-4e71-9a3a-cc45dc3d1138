import Taro from '@tarojs/taro'

import { BASE_URL, TOKEN_KEY } from '@/common/constants'
import { captureException, addBreadcrumb } from './sentry'

// 判断 HTTP 错误是否需要上报到 Sentry
const shouldReportHttpError = (statusCode: number): boolean => {
  // 以下状态码不上报到 Sentry（正常业务逻辑）
  const ignoredStatusCodes = [401, 403] // 401: 未授权, 403: 禁止访问
  return !ignoredStatusCodes.includes(statusCode)
}

// 获取错误类型
const getErrorType = (statusCode: number): string => {
  if (statusCode >= 400 && statusCode < 500) {
    return 'api_client_error'
  } else if (statusCode >= 500) {
    return 'api_server_error'
  } else {
    return 'api_network_error'
  }
}

interface RequestConfig {
  baseURL: string
  timeout: number
  header: Record<string, string>
  executeError?: boolean // 是否执行错误处理（显示错误提示等）
}

interface RequestOptions extends Partial<RequestConfig> {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  header?: Record<string, string>
}

// 创建请求实例
const request = {
  // 基础配置
  config: {
    baseURL: `${BASE_URL}/ai/api`,
    timeout: 10000,
    header: {
      'Content-Type': 'application/json',
    },
    executeError: true, // 默认执行错误处理
  } as RequestConfig,

  // 请求拦截器
  interceptors: {
    request: (config: RequestOptions) => {
      // 从本地存储获取 token
      const token = Taro.getStorageSync('token')

      // 确保 header 对象存在
      config.header = config.header || {}

      if (token) {
        config.header.Authorization = `Bearer ${token}`
      }
      // 确保 URL 是完整的
      if (!config.url.startsWith('http')) {
        config.url = `${request.config.baseURL}${config.url}`
      }
      return config
    },
    response: <T>(response: any, config: RequestOptions): Promise<T> => {
      const { data } = response
      // 使用 success 字段判断请求是否成功
      if (data.success) {
        // 成功请求不需要向 Sentry 发送任何信息，避免产生噪音和浪费配额
        return Promise.resolve(data.data)
      } else {
        // 创建业务错误对象
        const error = new Error(data.message || '请求失败')

        // 上报业务错误到 Sentry
        captureException(error, {
          type: 'api_business_error',
          url: config.url,
          method: config.method || 'GET',
          requestData: config.data,
          responseData: data,
          statusCode: response.statusCode,
          errorCode: data.code,
          errorMessage: data.message,
        })

        // 添加面包屑
        addBreadcrumb({
          message: `API 业务错误: ${config.url}`,
          category: 'api_error',
          level: 'error',
          data: {
            url: config.url,
            method: config.method || 'GET',
            errorCode: data.code,
            errorMessage: data.message,
          },
        })

        // 根据配置决定是否执行错误处理
        if (config.executeError !== false) {
          // 处理业务错误
          Taro.showToast({
            title: data.message || '请求失败',
            icon: 'none',
          })
        }
        return Promise.reject(error)
      }
    },
  },

  // 请求方法
  request: <T>(options: RequestOptions): Promise<T> => {
    const config = {
      ...request.config,
      ...options,
    }

    // 应用请求拦截器
    const requestConfig = request.interceptors.request(config)

    // 请求开始不再发送面包屑，避免产生大量噪音
    // 只有在发生错误时，错误上下文中已包含足够的请求信息

    return new Promise((resolve, reject) => {
      Taro.request({
        ...requestConfig,
        success: (res) => {
          try {
            request.interceptors.response<T>(res, config).then(resolve).catch(reject)
          } catch (error) {
            reject(error)
          }
        },
        fail: (error: any) => {
          // 安全地获取错误信息
          const getErrorMessage = (err: any): string => {
            if (typeof err === 'string') return err
            if (err && typeof err.errMsg === 'string') return err.errMsg
            if (err && err.message) return String(err.message)
            if (err && err.statusCode) return `HTTP ${err.statusCode} 错误`

            return 'HTTP 请求失败'
          }

          // 创建 HTTP 错误对象
          const errorMessage = getErrorMessage(error)
          const httpError = new Error(errorMessage)

          // 判断是否需要上报到 Sentry
          const shouldReport = error.statusCode ? shouldReportHttpError(error.statusCode) : true

          if (shouldReport) {
            // 上报 HTTP 错误到 Sentry
            captureException(httpError, {
              type: error.statusCode ? getErrorType(error.statusCode) : 'api_network_error',
              url: config.url,
              method: config.method || 'GET',
              requestData: config.data,
              statusCode: error.statusCode,
              errorMsg: error.errMsg,
              originalError: error,
              // 添加网络错误的详细信息
              networkError: {
                name: error.name,
                message: error.message,
                code: error.code,
                errno: error.errno,
              },
            })
          }

          // 添加面包屑
          addBreadcrumb({
            message: `API HTTP 错误: ${config.url}`,
            category: 'api_error',
            level: shouldReport ? 'error' : 'info',
            data: {
              url: config.url,
              method: config.method || 'GET',
              statusCode: error.statusCode,
              errorMsg: errorMessage,
              originalErrorMsg: error.errMsg,
            },
          })

          // 根据配置决定是否执行错误处理
          if (config.executeError !== false) {
            // 处理 HTTP 错误
            if (error.statusCode) {
              switch (error.statusCode) {
                case 401:
                  // token 过期或未登录
                  Taro.removeStorageSync(TOKEN_KEY)
                  Taro.reLaunch({
                    url: '/pages/index/index',
                  })
                  break
                case 403:
                  Taro.showToast({
                    title: '拒绝访问',
                    icon: 'none',
                  })
                  break
                case 404:
                  Taro.showToast({
                    title: '请求错误，未找到该资源',
                    icon: 'none',
                  })
                  break
                case 500:
                  Taro.showToast({
                    title: '服务器错误',
                    icon: 'none',
                  })
                  break
                default:
                  Taro.showToast({
                    title: `连接错误${error.statusCode}`,
                    icon: 'none',
                  })
              }
            } else {
              Taro.showToast({
                title: `网络连接异常，${JSON.stringify(error)}`,
                icon: 'none',
              })
            }
          }
          reject(httpError)
        },
      })
    })
  },

  // 封装 GET 请求
  get: <T>(url: string, params?: any, config?: Partial<RequestConfig>): Promise<T> => {
    return request.request<T>({
      method: 'GET',
      url,
      data: params,
      ...config,
    })
  },

  // 封装 POST 请求
  post: <T>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T> => {
    return request.request<T>({
      method: 'POST',
      url,
      data,
      ...config,
    })
  },

  // 封装 PUT 请求
  put: <T>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T> => {
    return request.request<T>({
      method: 'PUT',
      url,
      data,
      ...config,
    })
  },

  // 封装 DELETE 请求
  delete: <T>(url: string, params?: any, config?: Partial<RequestConfig>): Promise<T> => {
    return request.request<T>({
      method: 'DELETE',
      url,
      data: params,
      ...config,
    })
  },
}

export const { get, post, put, delete: del } = request
export default request
