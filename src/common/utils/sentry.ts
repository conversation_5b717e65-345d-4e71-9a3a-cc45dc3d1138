import Taro from '@tarojs/taro'
import React from 'react'

// 微信小程序全局对象的类型声明已在 src/types/sentry.d.ts 中定义

// 日志级别类型
type LogLevel = 'debug' | 'info' | 'warning' | 'error' | 'fatal'

// 确保在小程序环境中有基本的 process.env 对象
if (typeof process === 'undefined') {
  ;(globalThis as any).process = { env: {} }
}

// 用户信息接口
interface SentryUser {
  id?: string
  email?: string
  username?: string
  [key: string]: any
}

// 面包屑接口
interface Breadcrumb {
  message: string
  category?: string
  level?: LogLevel
  data?: any
  timestamp?: number
}

// 错误事件接口
interface ErrorEvent {
  event_id: string
  timestamp: number
  level: LogLevel
  message?: string
  exception?: {
    values: Array<{
      type: string
      value: string
      stacktrace?: {
        frames: Array<{
          filename: string
          function: string
          lineno: number
          colno: number
        }>
      }
    }>
  }
  breadcrumbs?: Breadcrumb[]
  user?: SentryUser | null
  tags?: Record<string, string>
  extra?: Record<string, any>
  contexts?: Record<string, any>
  environment?: string
  release?: string
  platform?: string
}

// Sentry 配置
interface SentryConfig {
  dsn: string
  environment: string
  release: string
  debug: boolean
  maxBreadcrumbs: number
  sampleRate: number
}

// 获取当前环境的配置
const getCurrentEnvironmentConfig = () => {
  const isProduction = process.env.NODE_ENV === 'production'
  const isDevelopment = process.env.NODE_ENV === 'development'

  return {
    environment:
      process.env.TARO_APP_SENTRY_ENVIRONMENT || (isProduction ? 'production' : 'development'),
    release: process.env.TARO_APP_SENTRY_RELEASE || (isProduction ? '1.0.0' : '1.0.0-dev'),
    sampleRate: parseFloat(
      process.env.TARO_APP_SENTRY_SAMPLE_RATE || (isProduction ? '0.8' : '1.0')
    ),
    debug: process.env.TARO_APP_SENTRY_DEBUG === 'true' || isDevelopment,
  }
}

// 全局状态
class SentryClient {
  private config: SentryConfig
  private user: SentryUser | null = null
  private breadcrumbs: Breadcrumb[] = []
  private tags: Record<string, string> = {}
  private contexts: Record<string, any> = {}
  private enabled: boolean = false

  constructor(config: SentryConfig) {
    this.config = config
    this.setupMiniprogramContext()
  }

  // 设置小程序上下文
  private setupMiniprogramContext() {
    try {
      if (typeof wx !== 'undefined') {
        const systemInfo = (wx as any).getSystemInfoSync()
        const accountInfo = (wx as any).getAccountInfoSync()

        this.contexts['miniprogram'] = {
          platform: 'miniprogram',
          system: systemInfo.system,
          version: systemInfo.version,
          SDKVersion: systemInfo.SDKVersion,
          brand: systemInfo.brand,
          model: systemInfo.model,
          appId: accountInfo.miniProgram.appId,
          envVersion: accountInfo.miniProgram.envVersion,
          screenWidth: systemInfo.screenWidth,
          screenHeight: systemInfo.screenHeight,
          pixelRatio: systemInfo.pixelRatio,
        }

        this.contexts['runtime'] = {
          name: 'taro',
          version: '4.0.9',
        }
      }
    } catch (error) {
      console.error('获取小程序信息失败:', error)
    }
  }

  // 初始化
  init() {
    try {
      this.enabled = true

      // 设置全局错误处理
      this.setupGlobalErrorHandlers()

      console.log('✅ Sentry 客户端初始化成功')
      console.log('📊 配置信息:', {
        dsn: this.config.dsn.substring(0, 50) + '...',
        environment: this.config.environment,
        release: this.config.release,
        debug: this.config.debug,
        authToken: process.env.TARO_APP_SENTRY_AUTH_TOKEN ? '已配置' : '未配置（构建时功能不可用）',
      })

      return true
    } catch (error) {
      console.error('❌ Sentry 初始化失败:', error)
      return false
    }
  }

  // 设置全局错误处理
  private setupGlobalErrorHandlers() {
    if (typeof wx !== 'undefined') {
      // 监听小程序错误
      if ((wx as any).onError) {
        ;(wx as any).onError((error: string) => {
          console.error('微信小程序错误:', error)
          this.captureMessage(`小程序错误: ${error}`, 'error')
        })
      }

      // 监听未处理的 Promise 错误
      if ((wx as any).onUnhandledRejection) {
        ;(wx as any).onUnhandledRejection((res: any) => {
          console.error('未处理的 Promise 错误:', res)
          this.captureException(res.reason || new Error('未处理的 Promise 错误'))
        })
      }
    }
  }

  // 生成事件ID
  private generateEventId(): string {
    return 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = (Math.random() * 16) | 0
      const v = c === 'x' ? r : (r & 0x3) | 0x8
      return v.toString(16)
    })
  }

  // 解析DSN
  private parseDsn(dsn: string) {
    const match = dsn.match(/^https?:\/\/([^@]+)@([^/]+)\/(.+)$/)
    if (!match) {
      throw new Error('Invalid DSN format')
    }

    const [, publicKey, host, projectId] = match
    return {
      publicKey,
      host,
      projectId,
      url: `https://${host}/api/${projectId}/store/`,
    }
  }

  // 发送事件到 Sentry
  private async sendEvent(event: ErrorEvent) {
    if (!this.enabled) return

    // 采样率检查
    if (Math.random() > this.config.sampleRate) {
      console.log('事件被采样过滤')
      return
    }

    try {
      const { publicKey, url } = this.parseDsn(this.config.dsn)

      const authHeader = [
        `Sentry sentry_version=7`,
        `sentry_client=sentry-taro/1.0.0`,
        `sentry_timestamp=${Math.floor(Date.now() / 1000)}`,
        `sentry_key=${publicKey}`,
      ].join(', ')

      const response = await Taro.request({
        url,
        method: 'POST',
        data: event,
        header: {
          'Content-Type': 'application/json',
          'User-Agent': 'sentry-taro/1.0.0',
          'X-Sentry-Auth': authHeader,
        },
      })

      if (response.statusCode >= 200 && response.statusCode < 300) {
        console.log('✅ 事件发送成功:', event.event_id)
      } else {
        console.error('❌ 事件发送失败:', response.statusCode, response.data)
      }
    } catch (error) {
      console.error('❌ 发送事件时出错:', error)
    }
  }

  // 设置用户信息
  setUser(user: SentryUser) {
    this.user = user
    console.log('👤 用户信息已设置:', user)
  }

  // 设置标签
  setTag(key: string, value: string) {
    this.tags[key] = value
  }

  // 设置上下文
  setContext(key: string, context: any) {
    this.contexts[key] = context
  }

  // 添加面包屑
  addBreadcrumb(breadcrumb: Breadcrumb) {
    const crumb: Breadcrumb = {
      ...breadcrumb,
      timestamp: breadcrumb.timestamp || Date.now() / 1000,
    }

    this.breadcrumbs.push(crumb)

    // 限制面包屑数量
    const maxBreadcrumbs = parseInt(process.env.TARO_APP_SENTRY_MAX_BREADCRUMBS || '50')

    if (this.breadcrumbs.length > maxBreadcrumbs) {
      this.breadcrumbs.shift()
    }

    if (this.config.debug) {
      console.log('🍞 面包屑已添加:', crumb)
    }
  }

  // 上报消息
  captureMessage(message: string, level: LogLevel = 'info', extra?: any) {
    console.log(`📝 Sentry 消息上报 [${level}]:`, message)

    const event: ErrorEvent = {
      event_id: this.generateEventId(),
      timestamp: Date.now() / 1000,
      level,
      message,
      breadcrumbs: [...this.breadcrumbs],
      user: this.user,
      tags: { ...this.tags },
      extra: extra || {},
      contexts: { ...this.contexts },
      environment: this.config.environment,
      release: this.config.release,
      platform: 'javascript',
    }

    this.sendEvent(event)
  }

  // 序列化对象为字符串（处理循环引用）
  private serializeForSentry(obj: any, depth = 0, maxDepth = 3): any {
    if (depth > maxDepth) return '[Object too deep]'

    if (obj === null || obj === undefined) return obj
    if (typeof obj === 'string' || typeof obj === 'number' || typeof obj === 'boolean') return obj

    if (obj instanceof Error) {
      return {
        name: obj.name,
        message: obj.message,
        stack: obj.stack,
      }
    }

    if (Array.isArray(obj)) {
      return obj.slice(0, 10).map((item) => this.serializeForSentry(item, depth + 1, maxDepth))
    }

    if (typeof obj === 'object') {
      const result: any = {}
      let count = 0
      for (const key in obj) {
        if (count >= 20) break // 限制键数量
        if (obj.hasOwnProperty(key)) {
          try {
            result[key] = this.serializeForSentry(obj[key], depth + 1, maxDepth)
          } catch (e) {
            result[key] = '[Unserializable]'
          }
        }
        count++
      }
      return result
    }

    return String(obj)
  }

  // 上报异常
  captureException(error: Error | string, extra?: any) {
    const actualError = typeof error === 'string' ? new Error(error) : error

    console.error('🔥 Sentry 异常上报:', actualError)

    // 解析堆栈信息
    const stackTrace = this.parseStackTrace(actualError.stack || '')

    // 序列化 extra 数据
    const serializedExtra = extra ? this.serializeForSentry(extra) : {}

    const event: ErrorEvent = {
      event_id: this.generateEventId(),
      timestamp: Date.now() / 1000,
      level: 'error',
      exception: {
        values: [
          {
            type: actualError.name || 'Error',
            value: actualError.message || String(error),
            stacktrace: stackTrace.length > 0 ? { frames: stackTrace } : undefined,
          },
        ],
      },
      breadcrumbs: [...this.breadcrumbs],
      user: this.user,
      tags: { ...this.tags },
      extra: serializedExtra,
      contexts: { ...this.contexts },
      environment: this.config.environment,
      release: this.config.release,
      platform: 'javascript',
    }

    this.sendEvent(event)
  }

  // 检查是否已启用
  isEnabled(): boolean {
    return this.enabled
  }

  // 解析堆栈信息
  private parseStackTrace(stack: string) {
    const frames: any[] = []
    const lines = stack.split('\n')

    for (const line of lines) {
      const match = line.match(/at\s+(.+?)\s+\((.+?):(\d+):(\d+)\)/)
      if (match) {
        const [, functionName, filename, lineno, colno] = match
        frames.push({
          filename: filename || 'unknown',
          function: functionName || 'unknown',
          lineno: parseInt(lineno, 10) || 0,
          colno: parseInt(colno, 10) || 0,
        })
      }
    }

    return frames.reverse() // Sentry 期望从最深的调用开始
  }
}

// 全局实例
let sentryClient: SentryClient | null = null

// 初始化 Sentry
export const initSentry = (config: Partial<SentryConfig> = {}) => {
  // 防止重复初始化
  if (sentryClient) {
    console.log('ℹ️ Sentry 已经初始化过，跳过重复初始化')
    return sentryClient.isEnabled()
  }

  console.log('process.env', process.env)

  // 检查必需的环境变量
  if (!process.env.TARO_APP_SENTRY_DSN) {
    console.warn('⚠️ SENTRY_DSN 环境变量未设置，Sentry 将不会初始化')
    return false
  }

  if (!process.env.TARO_APP_SENTRY_ENVIRONMENT) {
    console.warn('⚠️ SENTRY_ENVIRONMENT 环境变量未设置')
  }

  if (!process.env.TARO_APP_SENTRY_RELEASE) {
    console.warn('⚠️ SENTRY_RELEASE 环境变量未设置')
  }

  if (!process.env.TARO_APP_SENTRY_AUTH_TOKEN) {
    console.info('ℹ️ SENTRY_AUTH_TOKEN 环境变量未设置，构建时功能（如 source maps 上传）将不可用')
  }

  const envConfig = getCurrentEnvironmentConfig()

  const finalConfig: SentryConfig = {
    dsn: process.env.TARO_APP_SENTRY_DSN,
    environment: envConfig.environment,
    release: envConfig.release,
    debug: envConfig.debug,
    maxBreadcrumbs: parseInt(process.env.TARO_APP_SENTRY_MAX_BREADCRUMBS || '50'),
    sampleRate: envConfig.sampleRate,
    ...config,
  }

  sentryClient = new SentryClient(finalConfig)
  return sentryClient.init()
}

// 导出函数
export const setSentryUser = (user: SentryUser) => {
  sentryClient?.setUser(user)
}

export const setSentryTag = (key: string, value: string) => {
  sentryClient?.setTag(key, value)
}

export const setSentryContext = (key: string, context: any) => {
  sentryClient?.setContext(key, context)
}

export const captureMessage = (message: string, level: LogLevel = 'info', extra?: any) => {
  sentryClient?.captureMessage(message, level, extra)
}

export const captureException = (error: Error | string, extra?: any) => {
  sentryClient?.captureException(error, extra)
}

export const addBreadcrumb = (breadcrumb: Breadcrumb) => {
  sentryClient?.addBreadcrumb(breadcrumb)
}

// 错误边界组件
export class SentryErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('React 错误边界捕获:', error)

    addBreadcrumb({
      message: 'React 错误边界捕获',
      category: 'error',
      level: 'error',
      data: { componentStack: errorInfo.componentStack },
    })

    captureException(error, {
      source: 'errorBoundary',
      componentStack: errorInfo.componentStack,
    })
  }

  render() {
    if (this.state.hasError) {
      return null
    }
    return this.props.children
  }
}

// 包装组件
export const withSentry = <T extends object>(
  Component: React.ComponentType<T>
): React.ComponentType<T> => {
  return (props: T) => {
    return React.createElement(SentryErrorBoundary, {
      children: React.createElement(Component, props),
    })
  }
}

// 获取当前页面信息
export const getCurrentPageInfo = () => {
  try {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    return {
      route: currentPage?.route,
      options: currentPage?.options,
      stackLength: pages.length,
    }
  } catch (error) {
    return null
  }
}

// 监听页面路由变化（仅在调试模式下启用）
export const setupRouteTracking = () => {
  // 只在开发环境或调试模式下启用路由追踪，避免生产环境产生大量噪音
  const envConfig = getCurrentEnvironmentConfig()
  if (!envConfig.debug && process.env.NODE_ENV === 'production') {
    console.log('🔕 生产环境下跳过路由追踪，避免产生过多面包屑')
    return
  }

  console.log('🔍 启用路由追踪（调试模式）')

  const originalNavigateTo = Taro.navigateTo
  const originalRedirectTo = Taro.redirectTo
  const originalSwitchTab = Taro.switchTab

  // 包装导航方法，仅在调试模式下添加面包屑记录
  Taro.navigateTo = (options: any) => {
    addBreadcrumb({
      message: `导航到: ${options.url}`,
      category: 'navigation',
      level: 'info',
      data: { type: 'navigateTo', url: options.url },
    })
    return originalNavigateTo(options)
  }

  Taro.redirectTo = (options: any) => {
    addBreadcrumb({
      message: `重定向到: ${options.url}`,
      category: 'navigation',
      level: 'info',
      data: { type: 'redirectTo', url: options.url },
    })
    return originalRedirectTo(options)
  }

  Taro.switchTab = (options: any) => {
    addBreadcrumb({
      message: `切换标签页: ${options.url}`,
      category: 'navigation',
      level: 'info',
      data: { type: 'switchTab', url: options.url },
    })
    return originalSwitchTab(options)
  }
}

export default {
  initSentry,
  captureException,
  captureMessage,
  addBreadcrumb,
  setSentryUser,
  setSentryTag,
  setSentryContext,
  SentryErrorBoundary,
  withSentry,
  getCurrentPageInfo,
  setupRouteTracking,
}

// 导出类型定义
export type { LogLevel, SentryUser, Breadcrumb }
