import Taro from '@tarojs/taro'

// 当前应用版本号
export const CURRENT_VERSION = '1.1.0'

// 本地存储的版本号key
const VERSION_STORAGE_KEY = 'app_version'

// 功能更新弹窗显示状态key
const UPDATE_MODAL_SHOWN_KEY = 'update_modal_shown'

/**
 * 比较版本号
 * @param version1 版本号1
 * @param version2 版本号2
 * @returns 1: version1 > version2, 0: version1 = version2, -1: version1 < version2
 */
export const compareVersion = (version1: string, version2: string): number => {
  const v1Parts = version1.split('.').map(Number)
  const v2Parts = version2.split('.').map(Number)

  const maxLength = Math.max(v1Parts.length, v2Parts.length)

  for (let i = 0; i < maxLength; i++) {
    const v1Part = v1Parts[i] || 0
    const v2Part = v2Parts[i] || 0

    if (v1Part > v2Part) return 1
    if (v1Part < v2Part) return -1
  }

  return 0
}

/**
 * 获取本地存储的版本号
 */
export const getStoredVersion = (): string => {
  try {
    return Taro.getStorageSync(VERSION_STORAGE_KEY) || '0.0.0'
  } catch (error) {
    console.error('获取本地版本号失败:', error)

    return '0.0.0'
  }
}

/**
 * 保存版本号到本地存储
 */
export const setStoredVersion = (version: string): void => {
  try {
    Taro.setStorageSync(VERSION_STORAGE_KEY, version)
  } catch (error) {
    console.error('保存版本号失败:', error)
  }
}

/**
 * 检查是否需要显示更新弹窗
 */
export const shouldShowUpdateModal = (): boolean => {
  const storedVersion = getStoredVersion()
  const currentVersion = CURRENT_VERSION

  // 如果本地版本低于当前版本，需要显示更新弹窗
  return compareVersion(currentVersion, storedVersion) > 0
}

/**
 * 获取当前版本的更新弹窗是否已显示过
 */
export const isUpdateModalShown = (): boolean => {
  try {
    const shownVersion = Taro.getStorageSync(`${UPDATE_MODAL_SHOWN_KEY}_${CURRENT_VERSION}`)

    return !!shownVersion
  } catch (error) {
    console.error('获取弹窗显示状态失败:', error)

    return false
  }
}

/**
 * 标记当前版本的更新弹窗已显示
 */
export const markUpdateModalShown = (): void => {
  try {
    Taro.setStorageSync(`${UPDATE_MODAL_SHOWN_KEY}_${CURRENT_VERSION}`, true)
  } catch (error) {
    console.error('标记弹窗显示状态失败:', error)
  }
}

/**
 * 更新本地版本号为当前版本
 */
export const updateToCurrentVersion = (): void => {
  setStoredVersion(CURRENT_VERSION)
}
