import React from 'react'
import { View, Text } from '@tarojs/components'
import styles from './index.module.less'

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg'
  className?: string
  text?: string
}

const Loading: React.FC<LoadingProps> = ({ size = 'md', className = '', text }) => {
  return (
    <View className={`${styles.container} ${className}`}>
      <View className={`${styles.spinner} ${styles[size]}`} />
      {text && <Text className={styles.text}>{text}</Text>}
    </View>
  )
}

export default Loading
