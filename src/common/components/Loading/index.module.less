@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  gap: 10px;
  width: 100%;
}

.spinner {
  border-radius: 50%;
  animation: spin 1s linear infinite;
  border-style: solid;
  box-sizing: border-box;
}

.sm {
  width: 32px;
  height: 32px;
  border-width: 2px;
  border-color: #2a2f3a;
  border-top-color: var(--primary-color, #0fe0ff);
}

.md {
  width: 48px;
  height: 48px;
  border-width: 3px;
  border-color: #2a2f3a;
  border-top-color: var(--primary-color, #0fe0ff);
}

.lg {
  width: 64px;
  height: 64px;
  border-width: 4px;
  border-color: #2a2f3a;
  border-top-color: var(--primary-color, #0fe0ff);
}

.text {
  margin-top: 16px;
  font-size: 28px;
  color: #e6e6e6;
  text-align: center;
}
