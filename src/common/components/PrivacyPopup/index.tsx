import { View, Text, Button } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { memo, useEffect, useState } from 'react'

import styles from './index.module.less'

declare const wx: {
  onNeedPrivacyAuthorization?: (callback: (resolve: PrivacyResolve) => void) => void
  openPrivacyContract: (options: { success?: () => void; fail?: (res: any) => void }) => void
}

type PrivacyResolve = (result: { event: string; buttonId?: string }) => void

let privacyHandler: ((resolve: PrivacyResolve) => void) | undefined
let privacyResolves = new Set<PrivacyResolve>()

if (typeof wx !== 'undefined' && wx.onNeedPrivacyAuthorization) {
  wx.onNeedPrivacyAuthorization((resolve: PrivacyResolve) => {
    console.log('show')
    privacyHandler?.(resolve)
  })
}

const PrivacyPopup: React.FC = () => {
  const [visible, setVisible] = useState<boolean>(false)

  const handleDisagree = () => {
    setVisible(false)
    privacyResolves.forEach((resolve) => {
      resolve({
        event: 'disagree',
      })
    })
    privacyResolves.clear()
    Taro.exitMiniProgram()
  }

  const handleAgree = () => {
    setVisible(false)
    privacyResolves.forEach((resolve) => {
      resolve({
        event: 'agree',
        buttonId: 'agree-btn',
      })
    })
    privacyResolves.clear()
  }

  useEffect(() => {
    privacyHandler = (resolve: PrivacyResolve) => {
      privacyResolves.add(resolve)
      setVisible(true)
    }
  }, [])

  const openPrivacyContract = () => {
    if (typeof wx !== 'undefined') {
      wx.openPrivacyContract({
        success: () => {
          console.log('openPrivacyContract success')
        },
        fail: (res: any) => {
          console.error('openPrivacyContract fail', res)
        },
      })
    }
  }

  return (
    <View className={styles.container}>
      {visible && <View className={styles.mask} />}
      {visible && (
        <View className={styles.wrap}>
          <View className={styles.header}>
            <View className={styles.title}>用户隐私保护提示</View>
          </View>
          <View className={styles.content}>
            感谢您使用我们的服务，您在使用该服务前，请仔细阅读
            <Text className={styles.link} onClick={openPrivacyContract}>
              {' '}
              用户隐私保护指引{' '}
            </Text>
            。当您点击同意并开始使用该服务时，即表示您已理解并同意该条款内容。
          </View>
          <View className={styles.footer}>
            <Button
              id="disagree-btn"
              className={`${styles.btn} ${styles.btn_refuse}`}
              onClick={handleDisagree}
            >
              拒绝
            </Button>
            <Button
              id="agree-btn"
              className={styles.btn}
              openType="agreePrivacyAuthorization"
              onAgreePrivacyAuthorization={handleAgree}
            >
              同意
            </Button>
          </View>
          <View className={styles.safearea} />
        </View>
      )}
    </View>
  )
}

export default memo(PrivacyPopup)
