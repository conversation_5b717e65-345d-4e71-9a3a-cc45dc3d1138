.container {
  .mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 1000;
  }

  .wrap {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
    z-index: 1001;
  }

  .header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid #eee;
  }

  .title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }

  .content {
    padding: 20px;
    font-size: 16px;
    color: #666;
    line-height: 1.5;
  }

  .link {
    color: #07c160;
    text-decoration: underline;
  }

  .footer {
    display: flex;
    padding: 20px;
    border-top: 1px solid #eee;
  }

  .btn {
    flex: 1;
    margin: 0 10px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 20px;
    font-size: 16px;
    background-color: #07c160;
    color: #fff;

    &_refuse {
      background-color: #f5f5f5;
      color: #666;
    }
  }

  .safearea {
    height: env(safe-area-inset-bottom);
  }
}
