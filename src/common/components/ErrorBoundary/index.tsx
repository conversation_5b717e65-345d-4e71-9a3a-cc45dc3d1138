import React from 'react'
import { View, Text, Button } from '@tarojs/components'
import { SentryErrorBoundary } from '@/common/utils'

import styles from './index.module.less'

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>
}

interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false, error: null }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('错误边界捕获到错误:', error, errorInfo)
  }

  resetError = () => {
    this.setState({ hasError: false, error: null })
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback
        return <FallbackComponent error={this.state.error!} resetError={this.resetError} />
      }

      return (
        <View className={styles.errorContainer}>
          <Text className={styles.errorTitle}>出错了</Text>
          <Text className={styles.errorMessage}>
            {this.state.error?.message || '应用发生未知错误'}
          </Text>
          <Button className={styles.retryButton} onClick={this.resetError}>
            重试
          </Button>
        </View>
      )
    }

    return this.props.children
  }
}

// 默认错误页面组件
export const DefaultErrorFallback: React.FC<{ error: Error; resetError: () => void }> = ({
  error,
  resetError,
}) => {
  return (
    <View className={styles.errorContainer}>
      <Text className={styles.errorTitle}>页面出错了</Text>
      <Text className={styles.errorMessage}>{error.message}</Text>
      <Button className={styles.retryButton} onClick={resetError}>
        重试
      </Button>
    </View>
  )
}

// 包装 Sentry 错误边界的组件
export const SentryErrorBoundaryWrapper: React.FC<ErrorBoundaryProps> = (props) => {
  return (
    <SentryErrorBoundary>
      <ErrorBoundary {...props} />
    </SentryErrorBoundary>
  )
}

export default ErrorBoundary 