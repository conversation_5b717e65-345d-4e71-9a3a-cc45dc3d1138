.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 80px;
  box-sizing: border-box;
}

.modal {
  background: #fff;
  border-radius: 32px;
  width: 100%;
  max-width: 1200px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 40px 80px rgba(0, 0, 0, 0.15);
  animation: modalSlideIn 0.3s ease-out;
  box-sizing: border-box;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-40px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.header {
  padding: 48px 64px 24px;
  text-align: center;
  background: linear-gradient(135deg, #0a0e17 0%, #1a1f2e 100%);
  color: white;
  position: relative;
}

.importantHeader {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.title {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 12px;
  display: block;
  line-height: 1.2;
}

.version {
  font-size: 22px;
  opacity: 0.9;
  display: block;
  line-height: 1.2;
}

.importantBadge {
  position: absolute;
  top: 24px;
  right: 24px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 6px 20px;
  border-radius: 20px;
  font-size: 18px;
  font-weight: 600;
  border: 2px solid rgba(255, 255, 255, 0.3);
  line-height: 1.2;
}

.content {
  flex: 1;
  padding: 32px;
  max-height: 800px;
  box-sizing: border-box;
  overflow-y: auto;
}

.subtitle {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin-bottom: 32px;
  display: block;
  line-height: 1.4;
}

.featureList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.featureItem {
  display: flex;
  align-items: flex-start;
  padding: 0;
  background: none;
  border-radius: 0;
  border-left: none;
  gap: 16px;
}

.featureEmoji {
  font-size: 28px;
  line-height: 1.6;
  flex-shrink: 0;
}

.featureText {
  font-size: 28px;
  line-height: 1.6;
  color: #555;
  display: block;
  word-wrap: break-word;
  word-break: break-all;
  flex: 1;
}

.footer {
  padding: 32px;
  display: flex;
  gap: 32px;
  justify-content: center;
}

.laterBtn {
  flex: 1;
  height: 96px;
  border-radius: 16px;
  background: #f5f5f5;
  color: #666;
  font-size: 28px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;

  &:active {
    background: #e8e8e8;
  }
}

.confirmBtn {
  flex: 1;
  height: 80px;
  border-radius: 16px;
  background: linear-gradient(135deg, #0a0e17 0%, #1a1f2e 100%);
  color: white;
  font-size: 28px;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;

  &:active {
    opacity: 0.9;
  }

  &[disabled] {
    opacity: 0.7;
  }
}
