import { useState } from 'react'
import { View, Text, <PERSON><PERSON>, <PERSON><PERSON>View } from '@tarojs/components'
import {
  CURRENT_VERSION,
  markUpdateModalShown,
  updateToCurrentVersion,
} from '@/common/utils/version'
import { getVersionUpdateContent } from '@/common/constants/version'

import styles from './index.module.less'

interface UpdateModalProps {
  visible: boolean
  onClose: () => void
}

const UpdateModal = ({ visible, onClose }: UpdateModalProps) => {
  const [isClosing, setIsClosing] = useState(false)

  // 获取当前版本的更新内容
  const updateContent = getVersionUpdateContent(CURRENT_VERSION)

  const handleConfirm = () => {
    setIsClosing(true)

    // 标记弹窗已显示
    markUpdateModalShown()

    // 更新本地版本号
    updateToCurrentVersion()

    // 延迟关闭，给用户一个视觉反馈
    setTimeout(() => {
      onClose()
      setIsClosing(false)
    }, 300)
  }

  if (!visible) return null

  return (
    <View className={styles.overlay}>
      <View className={styles.modal}>
        {/* 头部 */}
        <View
          className={`${styles.header} ${updateContent.isImportant ? styles.importantHeader : ''}`}
        >
          <Text className={styles.title}>{updateContent.title}</Text>
          <Text className={styles.version}>v{updateContent.version}</Text>
          {updateContent.isImportant && <Text className={styles.importantBadge}>重要更新</Text>}
        </View>

        {/* 更新内容 */}
        <ScrollView className={styles.content} scrollY>
          <Text className={styles.subtitle}>本次更新内容：</Text>
          <View className={styles.featureList}>
            {updateContent.features.map((feature, index) => {
              // 检查第一个字符是否为emoji（简单判断：Unicode码点大于127的字符）
              const firstChar = feature.charAt(0)
              const isEmoji = firstChar.charCodeAt(0) > 127

              if (isEmoji) {
                // 找到第一个空格的位置来分离emoji和文字
                const spaceIndex = feature.indexOf(' ')
                const emoji = spaceIndex > 0 ? feature.substring(0, spaceIndex) : firstChar
                const text =
                  spaceIndex > 0 ? feature.substring(spaceIndex + 1) : feature.substring(1).trim()

                return (
                  <View key={index} className={styles.featureItem}>
                    <Text className={styles.featureEmoji}>{emoji}</Text>
                    <Text className={styles.featureText}>{text}</Text>
                  </View>
                )
              } else {
                return (
                  <View key={index} className={styles.featureItem}>
                    <Text className={styles.featureText}>{feature}</Text>
                  </View>
                )
              }
            })}
          </View>
        </ScrollView>

        {/* 底部按钮 */}
        <View className={styles.footer}>
          <Button
            className={styles.confirmBtn}
            onClick={handleConfirm}
            loading={isClosing}
            disabled={isClosing}
          >
            {isClosing ? '已了解' : '知道了'}
          </Button>
        </View>
      </View>
    </View>
  )
}

export default UpdateModal
