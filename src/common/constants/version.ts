// 版本更新内容配置
export interface VersionUpdateContent {
  version: string
  title: string
  features: string[]
  isImportant?: boolean // 是否为重要更新
}

// 版本更新历史记录
export const VERSION_UPDATES: Record<string, VersionUpdateContent> = {
  '1.1.0': {
    version: '1.1.0',
    title: '功能更新',
    features: [
      '🎨 新增失败重试功能',
      '📱 优化模型显示效果',
      '✨ 优化图片生成算法，提升生成质量和速度',
      '🔧 修复已知问题，提升应用稳定性',
    ],
    isImportant: true,
  },
}

// 获取指定版本的更新内容
export const getVersionUpdateContent = (version: string): VersionUpdateContent => {
  return VERSION_UPDATES[version] || getDefaultUpdateContent(version)
}

// 获取默认更新内容（当找不到指定版本时使用）
export const getDefaultUpdateContent = (version: string): VersionUpdateContent => {
  return {
    version,
    title: '版本更新',
    features: ['✨ 新功能和改进', '🔧 修复已知问题', '🚀 性能优化'],
    isImportant: false,
  }
}
