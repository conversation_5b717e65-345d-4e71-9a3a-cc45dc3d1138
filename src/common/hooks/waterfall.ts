// hooks/useWaterfall.js
import { useEffect, useState, useMemo } from 'react'
import Taro from '@tarojs/taro'

import type { ImageItem, ImageStatus, Style } from '@/common/apis/types'

export interface ImageWithHeight {
  id: number
  url: string
  height: number
  name: string
  originalIndex: number
  preview: string
  status: ImageStatus
}

interface Props {
  imageList: (ImageItem | Style)[]
  imgKey: 'preview' | 'resultUrl'
  columnCount?: number
  gutter?: number
}

/**
 * set 3:2 --> 3/2
 *
 * @param param0
 * @returns
 */
const getRatio = (ratio?: string) => {
  if (!ratio) return 3 / 2

  const [width, height] = ratio.split(':').map(Number)

  return width / height
}

export function useWaterfall({ imageList, imgKey, columnCount = 2, gutter = 12 }: Props) {
  const [columns, setColumns] = useState<ImageWithHeight[][]>(
    Array(columnCount)
      .fill(0)
      .map(() => [])
  )
  const [_, setHeights] = useState(Array(columnCount).fill(0))

  // 使用 useMemo 缓存处理后的图片列表
  const processedImageList = useMemo(() => {
    return imageList.map((img, index) => ({
      id: img.id,
      url: imgKey === 'preview' ? (img as Style).preview || '' : (img as ImageItem).resultUrl || '',
      status: (img as ImageItem).status || 'completed',
      preview:
        imgKey === 'preview'
          ? (img as Style).preview || ''
          : (img as ImageItem).resultUrlPreview || '',
      name: (img as Style).name || '',
      originalIndex: (img as Style).sortOrder || index,
      ratio: getRatio((img as Style).ratio) || 2 / 3,
    }))
  }, [imageList, imgKey])

  useEffect(() => {
    if (!processedImageList.length) {
      setColumns(
        Array(columnCount)
          .fill(0)
          .map(() => [])
      )
      setHeights(Array(columnCount).fill(0))
      return
    }

    // 重置状态
    const initialColumns = Array(columnCount)
      .fill(0)
      .map(() => [])
    setColumns(initialColumns)
    setHeights(Array(columnCount).fill(0))

    const screenWidth = Taro.getWindowInfo().windowWidth
    const displayWidth = screenWidth / columnCount - gutter * 2

    // 处理每张图片
    processedImageList.forEach((img) => {
      const displayHeight = displayWidth * img.ratio

      setHeights((prevHeights) => {
        const minIndex = prevHeights.indexOf(Math.min(...prevHeights))
        const newHeights = [...prevHeights]
        newHeights[minIndex] += displayHeight + gutter

        setColumns((prevColumns) => {
          const newColumns = [...prevColumns]
          // 确保 newColumns[minIndex] 是一个数组
          if (!Array.isArray(newColumns[minIndex])) {
            newColumns[minIndex] = []
          }
          newColumns[minIndex] = [
            ...newColumns[minIndex],
            {
              id: img.id,
              url: img.url,
              height: displayHeight,
              name: img.name,
              preview: img.preview,
              status: img.status,
              originalIndex: img.originalIndex,
            },
          ]
          return newColumns
        })

        return newHeights
      })
    })
  }, [processedImageList, columnCount, gutter])

  // 对每列进行排序，确保按照原始顺序显示
  const sortedColumns = useMemo(() => {
    return columns.map((column) => [...column].sort((a, b) => a.originalIndex - b.originalIndex))
  }, [columns])

  return { columns: sortedColumns }
}
