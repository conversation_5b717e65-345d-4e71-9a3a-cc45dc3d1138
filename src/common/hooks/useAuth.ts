import React, { useEffect } from 'react'
import { observer } from 'mobx-react-lite'

import authStore, { AuthStatus } from '@/common/store/auth'
import { setSentryUser } from '@/common/utils'

export const useAuth = () => {
  const {
    state: { status, userInfo, error },
    isAuthenticated,
    isLoading,
    currentUser,
    currentToken,
    login,
    logout,
    checkLogin,
    getUserInfo,
    refreshUserInfo,
  } = authStore

  // 当用户信息变化时，更新 Sentry 用户信息
  useEffect(() => {
    if (userInfo && status === AuthStatus.AUTHENTICATED) {
      setSentryUser({
        id: userInfo.id.toString(),
        username: userInfo.nickName || '未知用户',
        isActiveVip: userInfo.isActiveVip,
        inviteCode: userInfo.inviteCode,
      })
    }
  }, [userInfo, status])

  return {
    // 状态
    status,
    isAuthenticated,
    isLoading,
    error,

    // 数据
    userInfo: currentUser,
    token: currentToken,

    // 方法
    login,
    logout,
    checkLogin,
    getUserInfo,
    refreshUserInfo,
  }
}

// 高阶组件：需要认证的页面
export const withAuth = <P extends object>(
  Component: React.ComponentType<P>,
  options: {
    requireAuth?: boolean
    redirectTo?: string
  } = {}
) => {
  const { requireAuth = true, redirectTo = '/pages/index/index' } = options

  return observer((props: P) => {
    const { isAuthenticated, status } = useAuth()

    // 如果需要认证但未认证，重定向到首页
    if (requireAuth && !isAuthenticated && status !== AuthStatus.LOADING) {
      // 这里可以添加重定向逻辑
      return null
    }

    return React.createElement(Component, props)
  })
}

export default useAuth
