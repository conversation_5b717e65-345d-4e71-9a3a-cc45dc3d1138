import React, { useMemo } from 'react'
import { observer } from 'mobx-react-lite'

import { useAuth } from './useAuth'
import { checkUserAccess, AccessAction } from '@/common/utils/checkUserAccess'
import { checkPurchasePermission } from '@/common/utils/purchasePermission'

export interface PermissionState {
  canUseService: boolean
  canPurchase: boolean
  reason?: string
  action?: AccessAction
}

export const usePermission = (showVipCard: boolean, allowedUserIds: number[] | null) => {
  const { userInfo } = useAuth()

  const permissionState = useMemo((): PermissionState => {
    // 检查服务使用权限
    const accessCheck = checkUserAccess(userInfo)

    // 检查购买权限
    const purchaseCheck = checkPurchasePermission({
      userInfo,
      showVipCard,
      allowedUserIds,
    })

    return {
      canUseService: accessCheck.canUse,
      canPurchase: purchaseCheck.canPurchase,
      reason: accessCheck.reason || purchaseCheck.reason,
      action: accessCheck.action,
    }
  }, [userInfo, showVipCard, allowedUserIds])

  return permissionState
}

// 高阶组件：需要特定权限的页面
export const withPermission = <P extends object>(
  Component: React.ComponentType<P>,
  requiredPermissions: {
    canUseService?: boolean
    canPurchase?: boolean
  } = {}
) => {
  return observer((props: P) => {
    const { canUseService, canPurchase } = usePermission(false, null)

    // 检查所需权限
    if (requiredPermissions.canUseService && !canUseService) {
      return null // 或者显示权限不足的提示
    }

    if (requiredPermissions.canPurchase && !canPurchase) {
      return null // 或者显示购买权限不足的提示
    }

    return React.createElement(Component, props)
  })
}

export default usePermission
