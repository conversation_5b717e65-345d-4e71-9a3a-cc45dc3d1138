export interface LoginParams {
  code: string
  encryptedData?: string
  iv?: string
  inviteCode?: string
}

export interface LoginResponse {
  token: string
  user: UserInfo
}

export interface UserInfo {
  id: number
  phone?: string
  nickName?: string
  avatarUrl?: string
  points?: number
  isVip?: boolean
  vipExpireAt?: string
  inviteCode?: string
  isActiveVip?: boolean
}

export enum SubscribeStatus {
  UNDEFINED = 'undefined',
  ACCEPT = 'accept',
  REJECT = 'reject',
  BAN = 'ban',
}
