import http from '@/common/utils/request'

import type { LoginParams, LoginResponse, SubscribeStatus, UserInfo } from './types'

export default {
  login: (data: LoginParams) => http.post<LoginResponse>('/user/login', data),
  getUserInfo: () => http.get<UserInfo>('/user/info', {}, { executeError: false }),
  showVipCard: () => http.get<{ showPurchase: number }>('/setting/purchase'),
  syncSubscribeStatus: (data: { subscribeStatus: SubscribeStatus }) =>
    http.post<void>('/user/subscribe-status', data),
  getPaymentAccessList: () =>
    http.get<{ allowedUserIds: number[] }>('/setting/payment/access-list'),
  bindCoupon: (couponCode: string) => http.post<any>('/coupon/bind', { couponCode }),
}
