import type { Layer } from '../types'

export interface ImageListParams {
  excludeStatuses?: string
  page?: number
  limit?: number
}

// 图片状态枚举
export enum ImageStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

export type ImageList = Layer<ImageItem>

// 图片属性接口
export interface ImageItem {
  id: number
  userId: number
  originalUrls: string[] // 多张原始图片URL
  resultUrl: string | null // 处理后的图片URL
  resultUrlPreview: string | null // 处理后的图片预览URL
  styleName: string // 风格名称
  status: ImageStatus
  errorMessage: string | null
  taskId: string | null // 第三方API任务ID
  createdAt?: Date
  updatedAt?: Date
}

// 二次编辑响应接口
export interface EditImageResponse {
  id: number
  resultUrl?: string
  status: ImageStatus
  taskId?: string
}

export interface RetryImageResponse {
  imageId: number
  taskId: string
  status: ImageStatus
  pointsLeft: number
  isVip: boolean
}
