import http from '@/common/utils/request'

import type {
  ImageList,
  ImageListParams,
  EditImageResponse,
  ImageItem,
  RetryImageResponse,
} from './types'

export default {
  uploadImage: (image: string) => http.post('/image/upload', { image }),
  generateImage: (imageId: string, styleId: number) =>
    http.post('/image/generate', { imageId, styleId }),
  getImageList: (params?: ImageListParams) => http.get<ImageList>('/image/list', { params }),
  getImageDetail: (id: number) => http.get<ImageItem>(`/image/${id}`),
  deleteImage: (id: number) => http.delete(`/image/${id}`),
  editImage: (imageId: number, prompt: string) =>
    http.post<EditImageResponse>('/image/edit', { imageId, prompt }),
  retryImage: (imageId: number) => http.post<RetryImageResponse>('/image/retry', { imageId }),
}
