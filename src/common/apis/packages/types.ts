export interface PackageItem {
  id: number
  name: string // 套餐名称
  description: string // 套餐描述
  points: number // 积分数量
  price: number // 价格（单位：分）
  priceCNY: number // 价格（单位：元）
  originalPrice: number // 原价（单位：分），用于显示折扣
  originalPriceCNY: number // 原价（单位：元），用于显示折扣
  imageUrl: string | null // 套餐图片
  isPopular: boolean // 是否热门套餐
  isActive: boolean // 是否激活
  vipDuration?: number // 会员时长（单位：天）
  sortOrder: number // 排序顺序
  createdAt?: Date
  updatedAt?: Date
}
