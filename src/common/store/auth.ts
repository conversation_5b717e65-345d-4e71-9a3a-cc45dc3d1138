import Taro from '@tarojs/taro'
import { makeAutoObservable, runInAction } from 'mobx'

import apis from '@/common/apis'
import type { UserInfo } from '@/common/apis/user/types'
import { USER_INFO_KEY, TOKEN_KEY } from '@/common/constants'
import { captureException } from '@/common/utils'

export enum AuthStatus {
  IDLE = 'idle',
  LOADING = 'loading',
  AUTHENTICATED = 'authenticated',
  UNAUTHENTICATED = 'unauthenticated',
  ERROR = 'error',
}

export interface AuthState {
  status: AuthStatus
  token: string | null
  userInfo: UserInfo | null
  lastCheckTime: number
  error: string | null
}

class AuthStore {
  state: AuthState = {
    status: AuthStatus.IDLE,
    token: null,
    userInfo: null,
    lastCheckTime: 0,
    error: null,
  }

  // 检查间隔时间（60分钟）
  private readonly CHECK_INTERVAL = 60 * 60 * 1000

  constructor() {
    makeAutoObservable(this)
    this.initializeAuth()
  }

  // 初始化认证状态
  private initializeAuth = () => {
    const token = Taro.getStorageSync(TOKEN_KEY)
    const userInfo = Taro.getStorageSync(USER_INFO_KEY)

    if (token && userInfo) {
      runInAction(() => {
        this.state.token = token
        this.state.userInfo = userInfo
        this.state.status = AuthStatus.AUTHENTICATED
      })
    } else {
      runInAction(() => {
        this.state.status = AuthStatus.UNAUTHENTICATED
      })
    }
  }

  // 设置用户信息
  setUserInfo = (userInfo: UserInfo) => {
    runInAction(() => {
      this.state.userInfo = userInfo
      Taro.setStorageSync(USER_INFO_KEY, userInfo)
    })
  }

  // 设置认证状态
  setAuthStatus = (status: AuthStatus, error?: string) => {
    runInAction(() => {
      this.state.status = status
      if (error) {
        this.state.error = error
      }
    })
  }

  // 获取用户信息
  getUserInfo = async (): Promise<UserInfo | null> => {
    try {
      this.setAuthStatus(AuthStatus.LOADING)

      const userInfo = await apis.getUserInfo()

      runInAction(() => {
        this.setUserInfo(userInfo)
        this.state.lastCheckTime = Date.now()
        this.setAuthStatus(AuthStatus.AUTHENTICATED)
      })

      return userInfo
    } catch (error) {
      console.error('获取用户信息失败:', error)

      runInAction(() => {
        this.setAuthStatus(
          AuthStatus.ERROR,
          error instanceof Error ? error.message : '获取用户信息失败'
        )
      })

      // 上报错误到 Sentry
      captureException(error as Error, {
        type: 'get_user_info_error',
      })

      return null
    }
  }

  // 检查登录状态
  checkLogin = async (forceCheck = false): Promise<boolean> => {
    try {
      const now = Date.now()
      const timeSinceLastCheck = now - this.state.lastCheckTime

      // 如果不是强制检查，且距离上次检查时间少于间隔时间，则跳过
      if (!forceCheck && timeSinceLastCheck < this.CHECK_INTERVAL) {
        return this.state.status === AuthStatus.AUTHENTICATED
      }

      // 检查本地是否有 token
      const token = Taro.getStorageSync(TOKEN_KEY)
      if (!token) {
        runInAction(() => {
          this.setAuthStatus(AuthStatus.UNAUTHENTICATED)
        })
        return false
      }

      // 验证 token 是否有效
      const userInfo = await this.getUserInfo()

      return userInfo !== null
    } catch (error) {
      console.error('检查登录状态失败:', error)

      runInAction(() => {
        this.setAuthStatus(
          AuthStatus.ERROR,
          error instanceof Error ? error.message : '检查登录状态失败'
        )
      })

      return false
    }
  }

  // 登录
  login = async (inviteCode?: string): Promise<boolean> => {
    try {
      this.setAuthStatus(AuthStatus.LOADING)

      // 获取微信登录 code
      const { code } = await Taro.login()

      // 调用后端登录接口
      const { token, user } = await apis.login({ code, inviteCode })

      // 保存认证信息
      runInAction(() => {
        this.state.token = token
        this.setUserInfo(user)
        this.state.lastCheckTime = Date.now()
        this.setAuthStatus(AuthStatus.AUTHENTICATED)
        Taro.setStorageSync(TOKEN_KEY, token)
      })

      return true
    } catch (error) {
      console.error('登录失败:', error)

      runInAction(() => {
        this.setAuthStatus(AuthStatus.ERROR, error instanceof Error ? error.message : '登录失败')
      })

      // 上报错误到 Sentry
      captureException(error as Error, {
        type: 'login_error',
        inviteCode,
      })

      return false
    }
  }

  // 登出
  logout = () => {
    runInAction(() => {
      this.state.token = null
      this.state.userInfo = null
      this.state.status = AuthStatus.UNAUTHENTICATED
      this.state.error = null
    })

    // 清除本地存储
    Taro.removeStorageSync(TOKEN_KEY)
    Taro.removeStorageSync(USER_INFO_KEY)
  }

  // 刷新用户信息
  refreshUserInfo = async (): Promise<void> => {
    await this.getUserInfo()
  }

  // 获取当前认证状态
  get isAuthenticated(): boolean {
    return this.state.status === AuthStatus.AUTHENTICATED && !!this.state.userInfo
  }

  // 获取当前用户信息
  get currentUser(): UserInfo | null {
    return this.state.userInfo
  }

  // 获取当前 token
  get currentToken(): string | null {
    return this.state.token
  }

  // 获取加载状态
  get isLoading(): boolean {
    return this.state.status === AuthStatus.LOADING
  }

  // 获取错误信息
  get error(): string | null {
    return this.state.error
  }
}

export default new AuthStore()
