import { useEffect, useState } from 'react'
import { View, Image, ScrollView } from '@tarojs/components'
import { observer, useLocalObservable } from 'mobx-react'
import Taro, { useDidShow, useShareAppMessage, useShareTimeline } from '@tarojs/taro'

import { Loading, UpdateModal } from '@/common/components'

import { useWaterfall, useAuth, usePermission } from '@/common/hooks'
import { shouldShowUpdateModal, isUpdateModalShown, AccessAction, withSentry } from '@/common/utils'

import Store from './Store'
import apis from '@/common/apis'
import authStore from '@/common/store/auth'
import { SubscribeStatus } from '@/common/apis/types'
import {
  BASE_URL,
  COVER_URL,
  SUBSCRIBE_MODAL_SHOWN_KEY,
  SUBSCRIBE_TEMPLATE_ID,
} from '@/common/constants'
import { hasStorageKey } from '@/common/utils'

import styles from './index.module.less'

const Index = () => {
  const { styleList, getStyleList, loading, setIsFirst } = useLocalObservable(() => new Store())
  const [showUpdateModal, setShowUpdateModal] = useState(false)
  const { userInfo, refreshUserInfo } = useAuth()
  // 检查用户是否可以使用服务
  const { reason, action } = usePermission(false, null)

  const { columns } = useWaterfall({
    imageList: styleList,
    imgKey: 'preview',
  })

  useEffect(() => {
    // 设置导航栏颜色
    Taro.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#0a0e17',
    })
  }, [])

  useDidShow(() => {
    getStyleList()
    refreshUserInfo()

    // 在首页显示时检查版本更新
    checkVersionUpdate()
  })

  useEffect(() => {
    setIsFirst(false)
  }, [])

  // 检查版本更新
  const checkVersionUpdate = () => {
    try {
      // 检查是否需要显示更新弹窗
      const needUpdate = shouldShowUpdateModal()

      // 检查当前版本的弹窗是否已经显示过
      const alreadyShown = isUpdateModalShown()

      console.log('版本检查结果:', {
        needUpdate,
        alreadyShown,
        willShow: needUpdate && !alreadyShown,
      })

      // 如果需要更新且当前版本的弹窗未显示过，则显示弹窗
      if (needUpdate && !alreadyShown) {
        console.log('准备显示版本更新弹窗')
        // 延迟显示弹窗，确保页面完全加载
        setTimeout(() => {
          console.log('显示版本更新弹窗')
          setShowUpdateModal(true)
        }, 1000)
      } else {
        console.log('不显示版本更新弹窗')
      }
    } catch (error) {
      console.error('版本检查失败:', error)
    }
  }

  // 关闭更新弹窗
  const handleCloseUpdateModal = () => {
    setShowUpdateModal(false)
  }

  // 获取系统信息，用于设置顶部安全区域
  const systemInfo = Taro.getWindowInfo()
  const statusBarHeight = (systemInfo.statusBarHeight || 20) + 40

  // 处理选择图片
  const handleChooseImage = async (styleId: number) => {
    const hasSubscribeKey = hasStorageKey(SUBSCRIBE_MODAL_SHOWN_KEY)

    console.log('hasSubscribeKey', hasSubscribeKey)

    if (action === AccessAction.REFRESH) {
      Taro.showToast({
        title: reason || '请刷新或重进小程序',
        icon: 'none',
      })
      return
    }

    if (action === AccessAction.PURCHASE) {
      Taro.showModal({
        title: '积分不足',
        content: '积分不足，是否购买积分？',
        confirmText: '去购买',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 跳转到购买积分页面
            Taro.navigateTo({
              url: '/pages/membership/index',
            })
          }
        },
      })
      return
    }

    Taro.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        try {
          Taro.showLoading({
            title: '创建中...',
          })

          // 上传图片
          const imageId = await uploadImage(res.tempFilePaths[0])

          // 生成图片
          if (imageId) {
            const result = await apis.generateImage(imageId, styleId)

            Taro.hideLoading()

            if (result) {
              // 生成后需要重新获取积分信息
              refreshUserInfo()

              if (!hasSubscribeKey) {
                Taro.showModal({
                  title: '通知提醒',
                  showCancel: false,
                  content: '可能需要几分钟时间完成绘制，完成后会通知您',
                  confirmText: '知道了',
                  success: () => handleSubscribe(),
                })

                Taro.setStorageSync(SUBSCRIBE_MODAL_SHOWN_KEY, true)
              } else {
                // 弹窗提示创建成功
                Taro.showToast({
                  title: '创建成功',
                  icon: 'success',
                })
              }
            }
          }
        } catch (error) {
          console.error('处理图片失败', error)

          Taro.showToast({
            title: '处理图片失败',
            icon: 'none',
          })
        } finally {
          Taro.hideLoading()
        }
      },
    })

    // key存在说明已经触发过首次状态不会再触发微信自带订阅推送确认弹窗，可以直接默认获取权限
    if (hasSubscribeKey) {
      handleSubscribe()
    }
  }

  // 上传图片
  const uploadImage = async (tempFilePath: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      Taro.uploadFile({
        url: `${BASE_URL}/ai/api/image/upload`,
        filePath: tempFilePath,
        name: 'file',
        header: {
          Authorization: `Bearer ${authStore.currentToken}`,
        },
        success: (res) => {
          const data = JSON.parse(res.data)

          if (data.success) {
            resolve(data.data)
          } else {
            reject(new Error(data.message || '上传失败'))
          }
        },
        fail: (err) => {
          reject(err)
        },
      })
    })
  }

  // 分享给好友
  useShareAppMessage(() => {
    return {
      title: '吉卜力风格AI - 让你的照片更有创意',
      path: `/pages/index/index?inviteCode=${userInfo?.inviteCode}`,
      imageUrl: COVER_URL,
    }
  })

  // 分享到朋友圈
  useShareTimeline(() => {
    return {
      title: '吉卜力风格AI - 让你的照片更有创意',
      path: `/pages/index/index?inviteCode=${userInfo?.inviteCode}`,
      imageUrl: COVER_URL,
    }
  })

  // 同步订阅状态到服务端
  const syncSubscribeStatus = () => {
    Taro.getSetting({
      withSubscriptions: true,
      success: (res) => {
        const mainSwitch = res.subscriptionsSetting?.mainSwitch
        const itemStatus = res.subscriptionsSetting?.itemSettings?.[SUBSCRIBE_TEMPLATE_ID]

        // 如果用户已关闭订阅消息主开关，则不发送订阅消息，同步状态给服务端
        if (!mainSwitch) {
          console.log('用户已关闭订阅消息主开关')
          // 可以提示用户去设置里开启，或者不发送消息
          apis.syncSubscribeStatus({ subscribeStatus: SubscribeStatus.UNDEFINED })
        } else {
          // 进一步判断该模板的状态
          if (itemStatus === 'accept') {
            console.log('用户已接受订阅')
            apis.syncSubscribeStatus({ subscribeStatus: SubscribeStatus.ACCEPT })
          } else if (itemStatus === 'reject' || itemStatus === 'ban') {
            console.log('用户拒绝或屏蔽了订阅')
            apis.syncSubscribeStatus({ subscribeStatus: SubscribeStatus.REJECT })
          } else {
            console.log('用户未设置该订阅项')
            // 可视情况重新请求订阅授权
          }
        }
      },
    })
  }

  const handleSubscribe = () => {
    Taro.requestSubscribeMessage({
      tmplIds: [SUBSCRIBE_TEMPLATE_ID],
      entityIds: [SUBSCRIBE_TEMPLATE_ID],
      success: () => {
        syncSubscribeStatus()
      },
      fail: () => {
        syncSubscribeStatus()
      },
    })
  }

  if (loading) {
    return (
      <View className={styles.loadingContainer}>
        <Loading text="加载中..." />
      </View>
    )
  }

  return (
    <View className={styles.container}>
      {/* 状态栏占位 */}
      <View style={{ height: `${statusBarHeight}px` }} />

      {/* 瀑布流内容 */}
      <ScrollView scrollY className={styles.waterfall}>
        <View className={styles.columns}>
          {columns.map((column, columnIndex) => (
            <View key={columnIndex} className={styles.column}>
              {column.map((item) => (
                <View
                  key={item.id}
                  className={styles.imageCard}
                  onClick={() => handleChooseImage(item.id)}
                >
                  <Image
                    lazyLoad
                    src={item.url}
                    mode="widthFix"
                    className={styles.image}
                    style={{ height: item.height }}
                  />
                  <View className={styles.styleName}>{item.name}</View>
                </View>
              ))}
            </View>
          ))}
        </View>
      </ScrollView>

      {/* 版本更新弹窗 */}
      <UpdateModal visible={showUpdateModal} onClose={handleCloseUpdateModal} />
    </View>
  )
}

export default withSentry(observer(Index))
