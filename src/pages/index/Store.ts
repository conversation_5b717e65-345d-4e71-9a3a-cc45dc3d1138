import { makeAutoObservable, runInAction } from 'mobx'

import apis from '@/common/apis'
import type { Style } from '@/common/apis/types'
class Store {
  loading = false
  isFirst = true
  styleList: Style[] = []

  constructor() {
    makeAutoObservable(this)
  }

  setLoading = (loading: boolean) => {
    this.loading = loading
  }

  setIsFirst = (isFirst: boolean) => {
    this.isFirst = isFirst
  }

  getStyleList = async () => {
    if (this.isFirst) {
      this.setLoading(true)
    }

    try {
      const data = await apis.getStyles()

      runInAction(() => {
        // 根据sortOrder字段对数据重新进行排序
        this.styleList = data.sort((a, b) => a.sortOrder - b.sortOrder)
      })
    } catch (error) {
      console.error('获取风格列表失败', error)
    } finally {
      this.setLoading(false)
    }
  }
}

export default Store
