<template>
  <view class="index-page">
    <!-- 头部区域 -->
    <view class="header">
      <view class="title">吉卜力风格AI</view>
      <view class="subtitle">让你的照片拥有宫崎骏的魔法</view>
    </view>

    <!-- 风格选择区域 -->
    <view class="style-section">
      <view class="section-title">选择风格</view>
      <scroll-view class="style-list" scroll-x>
        <view 
          v-for="style in styleList" 
          :key="style.id"
          class="style-item"
          :class="{ active: selectedStyleId === style.id }"
          @click="selectStyle(style)"
        >
          <image :src="style.preview" class="style-image" mode="aspectFill" />
          <text class="style-name">{{ style.name }}</text>
        </view>
      </scroll-view>
    </view>

    <!-- 上传区域 -->
    <view class="upload-section">
      <view class="section-title">上传照片</view>
      <view class="upload-area" @click="chooseImage">
        <view v-if="!selectedImage" class="upload-placeholder">
          <text class="upload-icon">📷</text>
          <text class="upload-text">点击上传照片</text>
          <text class="upload-tip">支持JPG、PNG格式</text>
        </view>
        <image v-else :src="selectedImage" class="preview-image" mode="aspectFit" />
      </view>
    </view>

    <!-- 生成按钮 -->
    <view class="generate-section">
      <button 
        class="generate-btn"
        :class="{ disabled: !canGenerate }"
        :disabled="!canGenerate"
        @click="generateImage"
      >
        <text v-if="!isGenerating">{{ generateButtonText }}</text>
        <view v-else class="loading-container">
          <view class="loading-spinner"></view>
          <text>生成中...</text>
        </view>
      </button>
    </view>

    <!-- 用户信息提示 -->
    <view v-if="userInfo" class="user-info">
      <text class="points">剩余积分: {{ userInfo.points || 0 }}</text>
      <text v-if="userInfo.isActiveVip" class="vip-badge">VIP</text>
    </view>

    <!-- 更新弹窗 -->
    <UpdateModal 
      :visible="showUpdateModal"
      :version="VERSION_INFO.current"
      :features="VERSION_INFO.features"
      @close="handleUpdateModalClose"
      @update="handleUpdate"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { styleApi } from '@/api/style'
import { imageApi } from '@/api/image'
import { UpdateModal } from '@/components'
import type { StyleItem } from '@/types/style'
import { usePermission, VERSION_INFO, shouldShowUpdateModal, markUpdateModalShown } from '@/utils'

// 状态管理
const authStore = useAuthStore()

// 响应式数据
const styleList = ref<StyleItem[]>([])
const selectedStyleId = ref<number | null>(null)
const selectedImage = ref<string>('')
const isGenerating = ref(false)
const showUpdateModal = ref(false)

// 计算属性
const userInfo = computed(() => authStore.userInfo)
const canGenerate = computed(() => selectedStyleId.value && selectedImage.value && !isGenerating.value)
const generateButtonText = computed(() => {
  if (!selectedStyleId.value) return '请选择风格'
  if (!selectedImage.value) return '请上传照片'
  return '开始生成'
})

// 权限检查
const permission = computed(() => {
  return usePermission(true, null, userInfo.value)
})

// 方法
const loadStyles = async () => {
  try {
    const styles = await styleApi.getStyleList()
    styleList.value = styles
    if (styles.length > 0) {
      selectedStyleId.value = styles[0].id
    }
  } catch (error) {
    console.error('加载风格列表失败:', error)
    uni.showToast({
      title: '加载风格失败',
      icon: 'none'
    })
  }
}

const selectStyle = (style: StyleItem) => {
  selectedStyleId.value = style.id
}

const chooseImage = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      selectedImage.value = res.tempFilePaths[0]
    },
    fail: (error) => {
      console.error('选择图片失败:', error)
      uni.showToast({
        title: '选择图片失败',
        icon: 'none'
      })
    }
  })
}

const generateImage = async () => {
  if (!canGenerate.value) return

  // 检查权限
  if (!permission.value.canUse) {
    if (permission.value.action === 'refresh') {
      // 需要登录
      const success = await authStore.checkLogin()
      if (!success) {
        uni.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        })
        return
      }
    } else if (permission.value.action === 'purchase') {
      // 需要购买
      uni.navigateTo({
        url: '/pages/membership/index'
      })
      return
    }
  }

  try {
    isGenerating.value = true

    // 上传图片
    const imageUrl = await imageApi.uploadFile(selectedImage.value)
    
    // 生成图片
    const result = await imageApi.generateImage(imageUrl, selectedStyleId.value!)
    
    // 跳转到结果页面
    uni.navigateTo({
      url: `/pages/task/edit/index?id=${result.id}`
    })
    
  } catch (error) {
    console.error('生成图片失败:', error)
    uni.showToast({
      title: '生成失败，请重试',
      icon: 'none'
    })
  } finally {
    isGenerating.value = false
  }
}

const checkForUpdates = () => {
  if (shouldShowUpdateModal()) {
    showUpdateModal.value = true
  }
}

const handleUpdateModalClose = () => {
  showUpdateModal.value = false
  markUpdateModalShown()
}

const handleUpdate = () => {
  showUpdateModal.value = false
  markUpdateModalShown()
}

// 生命周期
onMounted(async () => {
  // 检查登录状态
  await authStore.checkLogin()
  
  // 加载风格列表
  await loadStyles()
  
  // 检查更新
  checkForUpdates()
})
</script>

<style lang="scss" scoped>
.index-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0e17 0%, #1a1a2e 100%);
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  
  .title {
    font-size: 28px;
    font-weight: bold;
    color: #0fe0ff;
    margin-bottom: 8px;
  }
  
  .subtitle {
    font-size: 14px;
    color: #a3a3a3;
  }
}

.style-section {
  margin-bottom: 30px;
  
  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 16px;
  }
  
  .style-list {
    white-space: nowrap;
    
    .style-item {
      display: inline-block;
      width: 80px;
      margin-right: 12px;
      text-align: center;
      
      .style-image {
        width: 80px;
        height: 80px;
        border-radius: 8px;
        border: 2px solid transparent;
        transition: all 0.3s ease;
      }
      
      .style-name {
        display: block;
        font-size: 12px;
        color: #a3a3a3;
        margin-top: 8px;
      }
      
      &.active .style-image {
        border-color: #0fe0ff;
        box-shadow: 0 0 12px rgba(15, 224, 255, 0.3);
      }
      
      &.active .style-name {
        color: #0fe0ff;
      }
    }
  }
}

.upload-section {
  margin-bottom: 30px;
  
  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 16px;
  }
  
  .upload-area {
    width: 100%;
    height: 200px;
    border: 2px dashed #333;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.02);
    transition: all 0.3s ease;
    
    &:active {
      border-color: #0fe0ff;
      background: rgba(15, 224, 255, 0.05);
    }
    
    .upload-placeholder {
      text-align: center;
      
      .upload-icon {
        font-size: 48px;
        display: block;
        margin-bottom: 12px;
      }
      
      .upload-text {
        font-size: 16px;
        color: #ffffff;
        display: block;
        margin-bottom: 8px;
      }
      
      .upload-tip {
        font-size: 12px;
        color: #a3a3a3;
        display: block;
      }
    }
    
    .preview-image {
      width: 100%;
      height: 100%;
      border-radius: 8px;
    }
  }
}

.generate-section {
  margin-bottom: 20px;
  
  .generate-btn {
    width: 100%;
    height: 50px;
    background: linear-gradient(135deg, #0fe0ff 0%, #00b4d8 100%);
    border: none;
    border-radius: 25px;
    color: #000;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    
    &:not(.disabled):active {
      transform: scale(0.98);
      opacity: 0.8;
    }
    
    &.disabled {
      background: #333;
      color: #666;
    }
    
    .loading-container {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .loading-spinner {
        width: 20px;
        height: 20px;
        border: 2px solid rgba(0, 0, 0, 0.3);
        border-top: 2px solid #000;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }
}

.user-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  
  .points {
    font-size: 14px;
    color: #a3a3a3;
  }
  
  .vip-badge {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #000;
    font-size: 12px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
