.container {
  min-height: 100vh;
  background: #0a0e17;
  background-image:
    radial-gradient(circle at 10% 10%, rgba(15, 224, 255, 0.1) 0%, transparent 25%),
    radial-gradient(circle at 90% 90%, rgba(144, 89, 231, 0.1) 0%, transparent 25%);
  position: relative;
}

.waterfall {
  height: calc(100vh - 200px);
  padding: 0 10px;
  box-sizing: border-box;
}

.columns {
  display: flex;
  justify-content: space-between;
  padding-bottom: 40px;
}

.column {
  width: calc(50% - 10px);
}

.imageCard {
  margin-bottom: 20px;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  background: #171c26;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
  border: 1px solid #2a2f3a;
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
}

.imageCard:active {
  transform: translateY(-2px);
  box-shadow:
    0 10px 20px rgba(0, 0, 0, 0.5),
    0 0 10px rgba(15, 224, 255, 0.3);
}

.image {
  width: 100%;
  height: auto;
  display: block;
}

.likeCount {
  position: absolute;
  bottom: 16px;
  right: 16px;
  background: rgba(15, 224, 255, 0.2);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 24px;
  color: #0fe0ff;
  font-weight: 500;
  backdrop-filter: blur(4px);
  box-shadow: 0 0 10px rgba(15, 224, 255, 0.3);
  text-shadow: 0 0 5px rgba(15, 224, 255, 0.5);
}

.styleName {
  position: absolute;
  bottom: 16px;
  left: 16px;
  background: rgba(23, 28, 38, 0.8);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 24px;
  color: #e6e6e6;
  font-weight: 500;
  backdrop-filter: blur(4px);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(15, 224, 255, 0.3);
}

.card {
  background-color: var(--card-color);
  border-radius: var(--radius-md);
  padding: var(--space-md);
  margin-bottom: var(--space-md);
  box-shadow: var(--shadow-md);
}

.button {
  width: 100%;
  height: 48px;
  line-height: 48px;
  text-align: center;
  border-radius: var(--radius-lg);
  font-size: 16px;
  font-weight: 500;
  margin-top: var(--space-md);
}

.primaryButton {
  composes: button;
  background: var(--primary-gradient);
  color: #0a0e17;
  border: none;
  box-shadow: var(--neon-shadow);
  font-weight: bold;
}

.defaultButton {
  composes: button;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  background-color: rgba(15, 224, 255, 0.1);
}

.imageContainer {
  width: 100%;
  height: 200px;
  border-radius: var(--radius-md);
  overflow: hidden;
  margin-bottom: var(--space-md);
  background-color: #171c26;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #2a2f3a;
}

.placeholder {
  color: var(--text-color-light);
  font-size: 14px;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.error {
  color: #ff4d4f;
  font-size: 14px;
  margin-top: var(--space-xs);
  text-align: center;
}

/* 新增样式 */
.mainPreview {
  width: 100%;
  height: 300px;
  border-radius: var(--radius-md);
  overflow: hidden;
  margin-bottom: var(--space-md);
  background-color: #171c26;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #2a2f3a;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
}

.previewImage {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.previewPlaceholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-md);
}

.placeholderIcon {
  width: 64px;
  height: 64px;
  margin-bottom: var(--space-md);
}

.placeholderText {
  font-size: 16px;
  color: #e6e6e6;
  margin-bottom: var(--space-xs);
}

.placeholderSub {
  font-size: 14px;
  color: #a3a3a3;
}

.section {
  margin-bottom: var(--space-lg);
}

.sectionTitle {
  font-size: 18px;
  font-weight: 500;
  color: #e6e6e6;
  margin-bottom: var(--space-md);
  text-shadow: 0 0 5px rgba(15, 224, 255, 0.3);
  position: relative;
  display: inline-block;
}

.sectionTitle::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 2px;
  background: linear-gradient(to right, #0fe0ff, #03a1ff);
  border-radius: 2px;
}

.styleSelector {
  background-color: #171c26;
  border-radius: var(--radius-md);
  padding: var(--space-md);
  margin-bottom: var(--space-md);
  border: 1px solid #2a2f3a;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
}

.pickerView {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.arrow {
  color: var(--text-color-light);
}

.stylePreview {
  background-color: var(--card-color);
  border-radius: var(--radius-md);
  padding: var(--space-md);
  margin-bottom: var(--space-md);
}

.styleImage {
  width: 100%;
  height: 200px;
  border-radius: var(--radius-sm);
  margin-bottom: var(--space-sm);
}

.styleName {
  font-size: 16px;
  color: var(--text-color);
  text-align: center;
}

.promptInput {
  width: 100%;
  height: 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: 0 var(--space-sm);
  font-size: 14px;
  color: var(--text-color);
}

.resultContainer {
  background-color: var(--card-color);
  border-radius: var(--radius-md);
  padding: var(--space-md);
}

.resultImage {
  width: 100%;
  height: 300px;
  border-radius: var(--radius-sm);
  margin-bottom: var(--space-md);
}

.resultActions {
  display: flex;
  gap: var(--space-sm);
}

.saveBtn {
  composes: primaryButton;
  flex: 1;
}

.saveAllBtn {
  composes: defaultButton;
  flex: 1;
}

.loadingMask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid white;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

.loadingText {
  color: white;
  margin-top: var(--space-md);
  font-size: 16px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 瀑布流布局样式 */
.styleGrid {
  width: 100%;
  height: calc(100vh - 140px);
}

.styleItem {
  width: 100%;
  position: relative;
  border-radius: var(--radius-md);
  overflow: hidden;
  margin-bottom: 16px;
  box-shadow: var(--shadow-md);
  background-color: var(--card-color);
}

.styleImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.styleName {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 8px;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  backdrop-filter: blur(2px);
}

.loadingText {
  color: white;
  margin-top: 12px;
  font-size: 14px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  min-height: 100vh;
  background-color: #0a0e17;
  background-image:
    radial-gradient(circle at 10% 10%, rgba(15, 224, 255, 0.1) 0%, transparent 25%),
    radial-gradient(circle at 90% 90%, rgba(144, 89, 231, 0.1) 0%, transparent 25%);
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid white;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}
