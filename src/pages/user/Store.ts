import { makeAutoObservable, runInAction } from 'mobx'
import Taro from '@tarojs/taro'

import apis from '@/common/apis'

class Store {
  showVipCard = false
  isAndroid = false
  allowedUserIds: number[] | null = null

  constructor() {
    makeAutoObservable(this)
    // 初始化时判断平台
    const systemInfo = Taro.getDeviceInfo()

    this.isAndroid = systemInfo.platform === 'android'
  }

  getVipCardVisible = async () => {
    try {
      // 如果是安卓平台，直接显示
      if (this.isAndroid) {
        runInAction(() => {
          this.showVipCard = true
        })
        return
      }

      const data = await apis.showVipCard()

      runInAction(() => {
        this.showVipCard = !!data.showPurchase
      })
    } catch (error) {
      console.error('获取VIP卡片显示状态失败:', error)
    }
  }

  getPaymentAccessList = async () => {
    try {
      const data = await apis.getPaymentAccessList()

      runInAction(() => {
        this.allowedUserIds = data.allowedUserIds
      })
    } catch (error) {
      console.error('获取支付权限列表失败:', error)
    }
  }
}

export default Store
