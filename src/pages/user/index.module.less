.container {
  min-height: 100vh;
  background: #0a0e17;
  background-image:
    radial-gradient(circle at 10% 10%, rgba(15, 224, 255, 0.1) 0%, transparent 25%),
    radial-gradient(circle at 90% 90%, rgba(144, 89, 231, 0.1) 0%, transparent 25%);
  position: relative;
}

.userHeader {
  padding: 40px 40px 60px;
}

.userBasic {
  display: flex;
  align-items: center;
  margin-bottom: 56px;
}

.avatarWrapper {
  position: relative;
  width: 110px;
  height: 110px;
  margin-right: 32px;
  border-radius: 55px;
  background: linear-gradient(135deg, rgba(15, 224, 255, 0.2), rgba(3, 161, 255, 0.2));
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 0 20px rgba(15, 224, 255, 0.3);

  &::before {
    content: '';
    position: absolute;
    inset: -3px;
    border-radius: 60px;
    background: linear-gradient(45deg, transparent, rgba(15, 224, 255, 0.5), transparent);
    z-index: -1;
  }
}

.avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 2px solid #0fe0ff;
  box-shadow: 0 0 15px rgba(15, 224, 255, 0.5);
  filter: brightness(0.9) contrast(1.1) saturate(0.8) hue-rotate(35deg);
  object-fit: cover;
  position: relative;
  z-index: 1;
}

.avatarOverlay {
  position: absolute;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(15, 224, 255, 0.2), rgba(3, 161, 255, 0.2));
  mix-blend-mode: color-dodge;
  z-index: 2;
  pointer-events: none;
}

.nameContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.nickname,
.point {
  font-size: 32px;
  font-weight: 600;
  color: #e6e6e6;
  display: flex;
  align-items: center;
  letter-spacing: 1px;
  text-shadow: 0 0 10px rgba(15, 224, 255, 0.5);
}

.vipExpire {
  font-size: 32px;
  color: #e6e6e6;
  display: flex;
  align-items: center;
  letter-spacing: 1px;
  text-shadow: 0 0 10px rgba(230, 230, 230, 0.3);
}

.vipTag {
  background: linear-gradient(135deg, #0fe0ff, #03a1ff);
  color: #0a0e17;
  font-size: 24px;
  padding: 6px 16px;
  border-radius: 8px;
  margin-left: 24px;
  letter-spacing: 1px;
  font-weight: bold;
  box-shadow: 0 0 10px rgba(15, 224, 255, 0.5);
}

.signature {
  font-size: 28px;
  color: #a3a3a3;
  letter-spacing: 0.5px;
  line-height: 1.5;
}

// 会员卡片样式
.vipCard {
  background: linear-gradient(135deg, #0a1a2a, #172335);
  border-radius: 24px;
  padding: 40px;
  margin-bottom: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  border: 1px solid #2a2f3a;
  position: relative;
  overflow: hidden;
}

.vipCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(to right, #0fe0ff, #03a1ff);
}

.vipInfo {
  flex: 1;
}

.vipTitle {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.vipIcon {
  width: 48px;
  height: 48px;
  line-height: 48px;
  text-align: center;
  background: linear-gradient(135deg, #0fe0ff, #03a1ff);
  border-radius: 24px;
  color: #0a0e17;
  font-size: 32px;
  font-weight: bold;
  margin-right: 16px;
  box-shadow: 0 0 10px rgba(15, 224, 255, 0.5);
}

.vipText {
  color: #0fe0ff;
  font-size: 36px;
  font-weight: 600;
  letter-spacing: 1px;
  text-shadow: 0 0 10px rgba(15, 224, 255, 0.5);
}

.vipDesc {
  color: #a3a3a3;
  font-size: 28px;
  letter-spacing: 0.5px;
}

.vipButton {
  background: linear-gradient(135deg, #0fe0ff, #03a1ff);
  border-radius: 20px;
  padding: 16px 40px;
  text-align: center;
  box-shadow: 0 0 15px rgba(15, 224, 255, 0.5);
}

.vipPrice {
  font-size: 36px;
  font-weight: 600;
  color: #0a0e17;
  margin-right: 16px;
}

.vipAction {
  font-size: 32px;
  color: #0a0e17;
  font-weight: 500;
}

.functionList {
  padding: 0 40px;
}

.sectionTitle {
  font-size: 34px;
  font-weight: 600;
  color: #e6e6e6;
  margin-bottom: 32px;
  letter-spacing: 1px;
  text-shadow: 0 0 10px rgba(15, 224, 255, 0.3);
  position: relative;
  display: inline-block;
}

.sectionTitle::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(to right, #0fe0ff, #03a1ff);
  border-radius: 3px;
}

.menuList {
  background: #171c26;
  border-radius: 24px;
  overflow: hidden;
  border: 1px solid #2a2f3a;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(to right, #0fe0ff, #03a1ff);
  }
}

.menuItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 36px;
  background: #171c26;
  border-radius: 0;
  margin-bottom: 0;
  position: relative;
  border-bottom: 1px solid #2a2f3a !important;

  &:last-child {
    border-bottom: none !important;
  }

  &::after {
    display: none;
  }

  /* 统一按钮和普通菜单项的样式 */
  &.button {
    background: #171c26;
    margin: 0;
    padding: 36px;
    height: auto;
    line-height: normal;
    text-align: left;
    border-radius: 0;
    width: 100%;
    box-sizing: border-box;
    box-shadow: none;
    border: none;
  }

  .menuLeft {
    display: flex;
    align-items: center;
  }

  .menuIcon {
    margin-right: 24px;
    font-size: 40px;
  }

  .menuText {
    font-size: 32px;
    color: #e6e6e6;
    font-weight: 500;
  }

  .menuTip {
    font-size: 32px;
    color: #e6e6e6;
  }

  .menuArrow {
    color: #a3a3a3;
    font-size: 40px;
  }

  /* 菜单项悬停效果 */
  &:active {
    background: rgba(15, 224, 255, 0.05);
  }
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(10, 14, 23, 0.85);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal {
  background: linear-gradient(135deg, #171c26 60%, #232b3a 100%);
  border-radius: 20px;
  width: 85vw;
  padding: 60px;
  box-shadow: 0 8px 20px rgba(15, 224, 255, 0.18);
  display: flex;
  flex-direction: column;
}

.header {
  font-size: 34px;
  font-weight: bold;
  margin-bottom: 20px;
  text-align: center;
  color: #0fe0ff;
  letter-spacing: 2px;
}

.title {
  font-size: 34px;
  font-weight: bold;
  color: #0fe0ff;
  text-shadow: 0 0 12px rgba(15, 224, 255, 0.3);
}

.content {
  margin-bottom: 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.input {
  width: calc(100% - 40px);
  border: 2px solid #0fe0ff;
  border-radius: 16px;
  padding: 16px 20px;
  font-size: 30px;
  margin-top: 8px;
  margin-bottom: 8px;
  background: #232b3a;
  color: #e6e6e6;
}

.footer {
  display: flex;
  justify-content: space-between;
  gap: 48px;
}

.confirmBtn {
  flex: 1;
  background: linear-gradient(135deg, #0fe0ff, #03a1ff);
  color: #0a0e17;
  border-radius: 16px;
  font-size: 32px;
  font-weight: 600;
  margin-right: 0;
  box-shadow: 0 0 10px rgba(15, 224, 255, 0.3);
  padding: 6px 0;
}

.cancelBtn {
  flex: 1;
  background: #232b3a;
  color: #a3a3a3;
  border-radius: 16px;
  font-size: 32px;
  font-weight: 600;
  margin-left: 0;
  border: 2px solid #2a2f3a;
  padding: 6px 0;
}
