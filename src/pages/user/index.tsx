import dayjs from 'dayjs'
import { observer, useLocalObservable } from 'mobx-react-lite'
import { View, Text, Image, Button, Input } from '@tarojs/components'
import Taro, { useDidShow, useShareAppMessage, useShareTimeline } from '@tarojs/taro'

import Store from './Store'
import Avatar from '@/assets/avatar.jpg'
import { useAuth, usePermission } from '@/common/hooks'
import { COVER_URL } from '@/common/constants'

import styles from './index.module.less'
import { useMemo, useState } from 'react'

import userApi from '@/common/apis/user'

export enum MenuType {
  HISTORY_ORDER = 'historyOrder',
  GUIDE = 'guide',
  CUSTOMER_SERVICE = 'customerService',
  SHARE = 'share',
  DISCOUNT = 'discount',
}

const User = () => {
  const { showVipCard, getVipCardVisible, allowedUserIds, getPaymentAccessList } =
    useLocalObservable(() => new Store())
  const { userInfo, refreshUserInfo } = useAuth()

  // 获取系统信息，用于设置顶部安全区域
  const systemInfo = Taro.getWindowInfo()
  const statusBarHeight = (systemInfo.statusBarHeight || 20) + 40

  const handleOpenVip = () => {
    Taro.navigateTo({ url: '/pages/membership/index' })
  }

  const handleMenu = (type: MenuType) => {
    switch (type) {
      case MenuType.GUIDE:
        Taro.navigateTo({ url: '/pages/guide/index' })
        break
      case MenuType.DISCOUNT:
        Taro.navigateTo({ url: '/pages/discount/index' })
        break

      default:
        break
    }
  }

  useDidShow(() => {
    getVipCardVisible()
    getPaymentAccessList()
    // 每次进入都获取最新的用户信息
    refreshUserInfo()
  })

  // 分享给好友
  useShareAppMessage(() => {
    return {
      title: '吉卜力风格AI - 让你的照片更有创意',
      path: `/pages/index/index?inviteCode=${userInfo?.inviteCode}`,
      imageUrl: COVER_URL,
    }
  })

  // 分享到朋友圈
  useShareTimeline(() => {
    return {
      title: '吉卜力风格AI - 让你的照片更有创意',
      path: `/pages/index/index?inviteCode=${userInfo?.inviteCode}`,
      imageUrl: COVER_URL,
    }
  })

  const { canPurchase: hasPermission } = usePermission(showVipCard, allowedUserIds)

  const [couponModalVisible, setCouponModalVisible] = useState(false)
  const [couponCode, setCouponCode] = useState('')
  const [submitting, setSubmitting] = useState(false)

  const handleCouponSubmit = async () => {
    if (!couponCode) {
      Taro.showToast({ title: '请输入优惠码', icon: 'none' })
      return
    }
    setSubmitting(true)
    try {
      await userApi.bindCoupon(couponCode)
      Taro.showToast({ title: '兑换成功', icon: 'success' })
      setCouponModalVisible(false)
      setCouponCode('')
    } catch (e: any) {
      // 错误提示已由request封装
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <View className={styles.container}>
      {/* 状态栏占位 */}
      <View style={{ height: `${statusBarHeight}px` }} />

      {/* 用户基本信息 */}
      <View className={styles.userHeader}>
        <View className={styles.userBasic}>
          <View className={styles.avatarWrapper}>
            <Image
              className={styles.avatar}
              src={userInfo?.avatarUrl || Avatar}
              mode="aspectFill"
            />
            <View className={styles.avatarOverlay}></View>
          </View>
          <View className={styles.nameContainer}>
            <View className={styles.nickname}>{userInfo?.nickName || '未登录'}</View>
            {userInfo?.isActiveVip ? (
              <View className={styles.vipExpire}>
                VIP到期时间:{' '}
                {userInfo?.vipExpireAt ? dayjs(userInfo.vipExpireAt).format('YYYY-MM-DD') : '永久'}
              </View>
            ) : (
              <View className={styles.point}>剩余积分: {userInfo?.points || 0}</View>
            )}
          </View>
        </View>

        {/* 会员卡片 */}
        <View className={styles.vipCard}>
          <View className={styles.vipInfo}>
            <View className={styles.vipTitle}>
              <Text className={styles.vipIcon}>V</Text>
              <Text className={styles.vipText}>积分卡</Text>
            </View>
            <Text className={styles.vipDesc}>获取积分，生成更多照片</Text>
          </View>
          {hasPermission ? (
            <View className={styles.vipButton} onClick={handleOpenVip}>
              <Text className={styles.vipAction}>获取</Text>
            </View>
          ) : (
            <Button
              openType="contact"
              style={{
                backgroundColor: 'transparent',
                outline: 0,
                lineHeight: 1.5,
                padding: 0,
              }}
            >
              <View className={styles.vipButton}>
                <Text className={styles.vipAction}>获取</Text>
              </View>
            </Button>
          )}
        </View>
      </View>

      {/* 功能列表 */}
      <View className={styles.functionList}>
        <View className={styles.sectionTitle}>我的功能</View>
        <View className={styles.menuList}>
          <View className={styles.menuItem} onClick={() => handleMenu(MenuType.GUIDE)}>
            <View className={styles.menuLeft}>
              <Text className={styles.menuIcon}>📖</Text>
              <Text className={styles.menuText}>使用指南</Text>
            </View>
            <Text className={styles.menuArrow}>›</Text>
          </View>

          <View className={styles.menuItem} onClick={() => handleMenu(MenuType.DISCOUNT)}>
            <View className={styles.menuLeft}>
              <Text className={styles.menuIcon}>🎁</Text>
              <Text className={styles.menuText}>获取优惠码</Text>
            </View>
            <Text className={styles.menuArrow}>›</Text>
          </View>

          <View className={styles.menuItem} onClick={() => setCouponModalVisible(true)}>
            <View className={styles.menuLeft}>
              <Text className={styles.menuIcon}>🎫</Text>
              <Text className={styles.menuText}>填写优惠码</Text>
            </View>
          </View>

          <Button className={`${styles.menuItem} ${styles.button}`} openType="contact">
            <View className={styles.menuLeft}>
              <Text className={styles.menuIcon}>💬</Text>
              <Text className={styles.menuText}>联系客服</Text>
            </View>
            <Text className={styles.menuArrow}>›</Text>
          </Button>

          <Button className={`${styles.menuItem} ${styles.button}`} openType="share">
            <View className={styles.menuLeft}>
              <Text className={styles.menuIcon}>📤</Text>
              <Text className={styles.menuText}>分享好友</Text>
            </View>
            <Text className={styles.menuTip}>+1 / 人</Text>
          </Button>
        </View>
      </View>

      {/* 优惠码弹窗 */}
      {couponModalVisible && (
        <View className={styles.overlay}>
          <View className={styles.modal}>
            <View className={styles.header}>
              <Text className={styles.title}>填写优惠码</Text>
            </View>
            <View className={styles.content}>
              <Input
                className={styles.input}
                placeholder="请输入优惠码"
                value={couponCode}
                onInput={(e) => setCouponCode(e.detail.value)}
                disabled={submitting}
                focus
              />
            </View>
            <View className={styles.footer}>
              <Button
                className={styles.cancelBtn}
                onClick={() => setCouponModalVisible(false)}
                disabled={submitting}
              >
                取消
              </Button>
              <Button
                className={styles.confirmBtn}
                onClick={handleCouponSubmit}
                loading={submitting}
                disabled={submitting}
              >
                兑换
              </Button>
            </View>
          </View>
        </View>
      )}
    </View>
  )
}

export default observer(User)
