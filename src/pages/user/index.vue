<template>
  <view class="user-page">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-avatar">
        <image 
          :src="userInfo?.avatarUrl || '/static/default-avatar.png'" 
          class="avatar-image"
          mode="aspectFill"
        />
      </view>
      
      <view class="user-info">
        <text class="nickname">{{ userInfo?.nickName || '未设置昵称' }}</text>
        <view class="user-stats">
          <view class="stat-item">
            <text class="stat-value">{{ userInfo?.points || 0 }}</text>
            <text class="stat-label">积分</text>
          </view>
          <view v-if="userInfo?.isActiveVip" class="vip-badge">
            <text>VIP</text>
          </view>
        </view>
      </view>
      
      <view class="user-actions">
        <button class="recharge-btn" @click="goToMembership">
          {{ userInfo?.isActiveVip ? '续费' : '充值' }}
        </button>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-item" @click="goToGuide">
        <view class="menu-icon">📖</view>
        <text class="menu-title">使用指南</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" @click="goToDiscount">
        <view class="menu-icon">🎁</view>
        <text class="menu-title">优惠活动</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" @click="showCouponInput">
        <view class="menu-icon">🎫</view>
        <text class="menu-title">兑换优惠券</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" @click="contactService">
        <view class="menu-icon">💬</view>
        <text class="menu-title">联系客服</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" @click="showAbout">
        <view class="menu-icon">ℹ️</view>
        <text class="menu-title">关于我们</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <!-- 优惠券输入弹窗 -->
    <uni-popup ref="couponPopup" type="center">
      <view class="coupon-modal">
        <view class="modal-header">
          <text class="modal-title">兑换优惠券</text>
          <text class="modal-close" @click="closeCouponInput">×</text>
        </view>
        
        <view class="modal-content">
          <input 
            v-model="couponCode"
            class="coupon-input"
            placeholder="请输入优惠券代码"
            maxlength="20"
          />
        </view>
        
        <view class="modal-footer">
          <button class="btn btn-secondary" @click="closeCouponInput">取消</button>
          <button 
            class="btn btn-primary" 
            :disabled="!couponCode.trim() || isBinding"
            @click="bindCoupon"
          >
            <text v-if="!isBinding">兑换</text>
            <view v-else class="loading-container">
              <view class="loading-spinner"></view>
              <text>兑换中...</text>
            </view>
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { userApi } from '@/api/user'

// 状态管理
const authStore = useAuthStore()

// 响应式数据
const couponCode = ref('')
const isBinding = ref(false)
const couponPopup = ref()

// 计算属性
const userInfo = computed(() => authStore.userInfo)

// 方法
const goToMembership = () => {
  uni.navigateTo({
    url: '/pages/membership/index'
  })
}

const goToGuide = () => {
  uni.navigateTo({
    url: '/pages/guide/index'
  })
}

const goToDiscount = () => {
  uni.navigateTo({
    url: '/pages/discount/index'
  })
}

const showCouponInput = () => {
  couponCode.value = ''
  couponPopup.value?.open()
}

const closeCouponInput = () => {
  couponPopup.value?.close()
}

const bindCoupon = async () => {
  if (!couponCode.value.trim()) return
  
  try {
    isBinding.value = true
    
    await userApi.bindCoupon(couponCode.value.trim())
    
    uni.showToast({
      title: '兑换成功',
      icon: 'success'
    })
    
    // 刷新用户信息
    await authStore.refreshUserInfo()
    
    closeCouponInput()
    
  } catch (error) {
    console.error('兑换优惠券失败:', error)
    uni.showToast({
      title: '兑换失败',
      icon: 'none'
    })
  } finally {
    isBinding.value = false
  }
}

const contactService = () => {
  uni.showActionSheet({
    itemList: ['微信客服', '在线客服', '意见反馈'],
    success: (res) => {
      switch (res.tapIndex) {
        case 0:
          // 微信客服
          uni.showModal({
            title: '微信客服',
            content: '请添加微信号：service123',
            showCancel: false
          })
          break
        case 1:
          // 在线客服
          uni.showToast({
            title: '功能开发中',
            icon: 'none'
          })
          break
        case 2:
          // 意见反馈
          uni.showToast({
            title: '功能开发中',
            icon: 'none'
          })
          break
      }
    }
  })
}

const showAbout = () => {
  uni.showModal({
    title: '关于我们',
    content: '吉卜力风格AI v1.0.0\n\n让你的照片拥有宫崎骏的魔法\n\n© 2024 All Rights Reserved',
    showCancel: false
  })
}

// 生命周期
onMounted(async () => {
  // 检查登录状态并刷新用户信息
  await authStore.checkLogin()
})
</script>

<style lang="scss" scoped>
.user-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0e17 0%, #1a1a2e 100%);
  padding: 20px;
}

.user-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  
  .user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 30px;
    overflow: hidden;
    margin-right: 16px;
    
    .avatar-image {
      width: 100%;
      height: 100%;
    }
  }
  
  .user-info {
    flex: 1;
    
    .nickname {
      font-size: 18px;
      font-weight: 600;
      color: #ffffff;
      display: block;
      margin-bottom: 8px;
    }
    
    .user-stats {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        
        .stat-value {
          font-size: 16px;
          font-weight: 600;
          color: #0fe0ff;
        }
        
        .stat-label {
          font-size: 12px;
          color: #a3a3a3;
        }
      }
      
      .vip-badge {
        background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
        color: #000;
        font-size: 12px;
        font-weight: 600;
        padding: 4px 8px;
        border-radius: 12px;
      }
    }
  }
  
  .user-actions {
    .recharge-btn {
      background: linear-gradient(135deg, #0fe0ff 0%, #00b4d8 100%);
      color: #000;
      border: none;
      border-radius: 20px;
      padding: 8px 16px;
      font-size: 14px;
      font-weight: 600;
    }
  }
}

.menu-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  
  .menu-item {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    
    &:last-child {
      border-bottom: none;
    }
    
    &:active {
      background: rgba(255, 255, 255, 0.05);
    }
    
    .menu-icon {
      font-size: 20px;
      margin-right: 12px;
    }
    
    .menu-title {
      flex: 1;
      font-size: 16px;
      color: #ffffff;
    }
    
    .menu-arrow {
      font-size: 16px;
      color: #a3a3a3;
    }
  }
}

.coupon-modal {
  width: 300px;
  background: #1a1a1a;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  
  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    
    .modal-title {
      font-size: 18px;
      font-weight: 600;
      color: #ffffff;
    }
    
    .modal-close {
      font-size: 24px;
      color: #a3a3a3;
      cursor: pointer;
    }
  }
  
  .modal-content {
    padding: 20px;
    
    .coupon-input {
      width: 100%;
      height: 44px;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid #333;
      border-radius: 8px;
      padding: 0 12px;
      color: #ffffff;
      font-size: 14px;
      
      &::placeholder {
        color: #666;
      }
    }
  }
  
  .modal-footer {
    display: flex;
    padding: 20px;
    gap: 12px;
    
    .btn {
      flex: 1;
      height: 44px;
      border-radius: 22px;
      font-size: 14px;
      font-weight: 600;
      border: none;
      
      &.btn-primary {
        background: linear-gradient(135deg, #0fe0ff 0%, #00b4d8 100%);
        color: #000;
        
        &:disabled {
          background: #333;
          color: #666;
        }
      }
      
      &.btn-secondary {
        background: transparent;
        color: #a3a3a3;
        border: 1px solid #333;
      }
      
      .loading-container {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        
        .loading-spinner {
          width: 16px;
          height: 16px;
          border: 2px solid rgba(0, 0, 0, 0.3);
          border-top: 2px solid #000;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
