<template>
  <view class="task-page">
    <!-- 头部 -->
    <view class="header">
      <text class="title">我的画廊</text>
    </view>

    <!-- 加载状态 -->
    <Loading v-if="loading" text="加载中..." />

    <!-- 空状态 -->
    <view v-else-if="imageList.length === 0" class="empty-state">
      <text class="empty-icon">🎨</text>
      <text class="empty-text">还没有作品</text>
      <text class="empty-tip">去首页生成你的第一张吉卜力风格图片吧</text>
      <button class="empty-btn" @click="goToHome">开始创作</button>
    </view>

    <!-- 瀑布流布局 -->
    <view v-else class="waterfall-container">
      <view
        v-for="(column, columnIndex) in columns"
        :key="columnIndex"
        class="waterfall-column"
      >
        <view
          v-for="item in column"
          :key="item.id"
          class="image-item"
          @click="viewImage(item)"
        >
          <image
            :src="item.resultUrlPreview || item.resultUrl"
            class="image"
            mode="widthFix"
            :style="{ height: item.height + 'px' }"
            @load="onImageLoad"
            @error="onImageError"
          />

          <!-- 状态遮罩 -->
          <view v-if="item.status !== 'completed'" class="status-overlay">
            <view v-if="item.status === 'processing'" class="processing">
              <view class="loading-spinner"></view>
              <text>生成中...</text>
            </view>
            <view v-else-if="item.status === 'failed'" class="failed">
              <text class="error-icon">❌</text>
              <text>生成失败</text>
              <button class="retry-btn" @click.stop="retryImage(item)">重试</button>
            </view>
          </view>

          <!-- 风格标签 -->
          <view class="style-tag">{{ item.styleName }}</view>

          <!-- 时间标签 -->
          <view class="time-tag">{{ formatTime(item.createdAt) }}</view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view v-if="hasMore && !loading" class="load-more" @click="loadMore">
      <text>加载更多</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onReachBottom } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { imageApi } from '@/api/image'
import { Loading } from '@/components'
import type { ImageItem } from '@/types/image'
import { useWaterfall, formatTime, getImageInfo, calculateImageHeight } from '@/utils'

// 状态管理
const authStore = useAuthStore()

// 响应式数据
const imageList = ref<ImageItem[]>([])
const loading = ref(false)
const currentPage = ref(1)
const hasMore = ref(true)
const pageSize = 20

// 瀑布流配置
const waterfallConfig = computed(() => ({
  imageList: imageList.value,
  columnCount: 2,
  gap: 10
}))

const { columns } = useWaterfall(waterfallConfig.value)

// 方法
const loadImageList = async (page = 1, append = false) => {
  try {
    loading.value = true
    
    const result = await imageApi.getImageList({
      page,
      limit: pageSize,
      excludeStatuses: 'pending'
    })
    
    // 为每个图片计算高度
    const processedImages = await Promise.all(
      result.data.map(async (item) => {
        if (item.resultUrl || item.resultUrlPreview) {
          try {
            const imageUrl = item.resultUrlPreview || item.resultUrl!
            const { width, height } = await getImageInfo(imageUrl)
            const calculatedHeight = calculateImageHeight(width, height, 160) // 假设列宽为160px
            return { ...item, height: calculatedHeight }
          } catch (error) {
            console.error('获取图片尺寸失败:', error)
            return { ...item, height: 200 } // 默认高度
          }
        }
        return { ...item, height: 200 }
      })
    )
    
    if (append) {
      imageList.value = [...imageList.value, ...processedImages]
    } else {
      imageList.value = processedImages
    }
    
    hasMore.value = result.data.length === pageSize
    currentPage.value = page
    
  } catch (error) {
    console.error('加载图片列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

const loadMore = () => {
  if (hasMore.value && !loading.value) {
    loadImageList(currentPage.value + 1, true)
  }
}

const viewImage = (item: ImageItem) => {
  if (item.status === 'completed') {
    uni.navigateTo({
      url: `/pages/task/edit/index?id=${item.id}`
    })
  }
}

const retryImage = async (item: ImageItem) => {
  try {
    uni.showLoading({ title: '重试中...' })
    
    await imageApi.retryImage(item.id)
    
    // 更新本地状态
    const index = imageList.value.findIndex(img => img.id === item.id)
    if (index !== -1) {
      imageList.value[index].status = 'processing'
      imageList.value[index].errorMessage = null
    }
    
    uni.showToast({
      title: '已重新开始生成',
      icon: 'success'
    })
    
  } catch (error) {
    console.error('重试失败:', error)
    uni.showToast({
      title: '重试失败',
      icon: 'none'
    })
  } finally {
    uni.hideLoading()
  }
}

const goToHome = () => {
  uni.switchTab({
    url: '/pages/index/index'
  })
}

const onImageLoad = () => {
  // 图片加载完成，可以做一些处理
}

const onImageError = () => {
  console.error('图片加载失败')
}

// 生命周期
onMounted(async () => {
  // 检查登录状态
  const isLoggedIn = await authStore.checkLogin()
  if (isLoggedIn) {
    await loadImageList()
  }
})

// 触底加载更多
onReachBottom(() => {
  loadMore()
})
</script>

<style lang="scss" scoped>
.task-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0e17 0%, #1a1a2e 100%);
}

.header {
  padding: 20px;
  text-align: center;
  
  .title {
    font-size: 24px;
    font-weight: bold;
    color: #0fe0ff;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
  
  .empty-icon {
    font-size: 64px;
    margin-bottom: 20px;
  }
  
  .empty-text {
    font-size: 18px;
    color: #ffffff;
    margin-bottom: 8px;
  }
  
  .empty-tip {
    font-size: 14px;
    color: #a3a3a3;
    margin-bottom: 30px;
  }
  
  .empty-btn {
    background: linear-gradient(135deg, #0fe0ff 0%, #00b4d8 100%);
    color: #000;
    border: none;
    border-radius: 25px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
  }
}

.waterfall-container {
  display: flex;
  padding: 0 10px 20px;
  gap: 10px;
  
  .waterfall-column {
    flex: 1;
    
    .image-item {
      position: relative;
      margin-bottom: 10px;
      border-radius: 8px;
      overflow: hidden;
      background: rgba(255, 255, 255, 0.05);
      
      .image {
        width: 100%;
        display: block;
      }
      
      .status-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        
        .processing {
          display: flex;
          flex-direction: column;
          align-items: center;
          color: #0fe0ff;
          
          .loading-spinner {
            width: 30px;
            height: 30px;
            border: 3px solid rgba(15, 224, 255, 0.3);
            border-top: 3px solid #0fe0ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 8px;
          }
        }
        
        .failed {
          display: flex;
          flex-direction: column;
          align-items: center;
          color: #ff6b6b;
          
          .error-icon {
            font-size: 24px;
            margin-bottom: 8px;
          }
          
          .retry-btn {
            background: #0fe0ff;
            color: #000;
            border: none;
            border-radius: 16px;
            padding: 6px 12px;
            font-size: 12px;
            margin-top: 8px;
          }
        }
      }
      
      .style-tag {
        position: absolute;
        top: 8px;
        left: 8px;
        background: rgba(15, 224, 255, 0.9);
        color: #000;
        font-size: 10px;
        padding: 4px 8px;
        border-radius: 12px;
        font-weight: 600;
      }
      
      .time-tag {
        position: absolute;
        bottom: 8px;
        right: 8px;
        background: rgba(0, 0, 0, 0.7);
        color: #fff;
        font-size: 10px;
        padding: 4px 8px;
        border-radius: 12px;
      }
    }
  }
}

.load-more {
  text-align: center;
  padding: 20px;
  color: #a3a3a3;
  font-size: 14px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
