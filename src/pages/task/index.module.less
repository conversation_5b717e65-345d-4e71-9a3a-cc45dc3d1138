.container {
  min-height: 100vh;
  background: #0a0e17;
  background-image:
    radial-gradient(circle at 10% 10%, rgba(15, 224, 255, 0.1) 0%, transparent 25%),
    radial-gradient(circle at 90% 90%, rgba(144, 89, 231, 0.1) 0%, transparent 25%);
  position: relative;
}

.waterfall {
  height: calc(100vh - 200px);
  padding: 0 10px;
  box-sizing: border-box;
}

.columns {
  display: flex;
  justify-content: space-between;
  padding-bottom: 40px;
}

.column {
  width: calc(50% - 10px);
}

.imageCard {
  margin-bottom: 20px;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  background: #171c26;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
  border: 1px solid #2a2f3a;
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
}

.imageCard:active {
  transform: translateY(-2px);
  box-shadow:
    0 10px 20px rgba(0, 0, 0, 0.5),
    0 0 10px rgba(15, 224, 255, 0.3);
}

.image {
  width: 100%;
  height: auto;
  display: block;
}

.saveText {
  text-align: center;
  color: #e6e6e6;
  font-size: 24px;
  margin: 20px 0;
  padding: 0 20px;
}

.emptyTip {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 200px);
  color: #e6e6e6;
  font-size: 28px;
  text-align: center;
  padding: 0 40px;
}

.failedContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 0;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 0;
  background: #171c26;
  border-radius: 16px;
}

.loadingIcon {
  width: 40px;
  height: 40px;
  border: 3px solid #0fe0ff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
  margin-bottom: 32px;
}

.loadingText {
  color: #e6e6e6;
  font-size: 24px;
}

.failedText {
  color: #e6e6e6;
  font-size: 24px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
