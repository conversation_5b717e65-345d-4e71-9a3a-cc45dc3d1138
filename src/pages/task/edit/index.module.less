.container {
  min-height: 100vh;
  background: #0a0e17;
  background-image:
    radial-gradient(circle at 10% 10%, rgba(15, 224, 255, 0.1) 0%, transparent 25%),
    radial-gradient(circle at 90% 90%, rgba(144, 89, 231, 0.1) 0%, transparent 25%);
  padding: 20px;
}

.loading,
.error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #e6e6e6;
  font-size: 32px;
}


.imageContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #0a0e17;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 10px;
  border: 1px solid #2a2f3a;
}

.originalImage,
.resultImage {
  max-width: 100%;
  max-height: 300px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.styleInfo {
  color: #9ca3af;
  font-size: 28px;
  text-align: center;
  display: block;
}

.section {
  margin-bottom: 30px;
  background: #171c26;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
  border: 1px solid #2a2f3a;
}

.sectionTitle {
  font-size: 28px;
  font-weight: 600;
  color: #e6e6e6;
  margin-bottom: 15px;
  display: block;
}


.promptInput {
  width: 100%;
  min-height: 120px;
  background: #0a0e17;
  border-radius: 12px;
  padding: 15px;
  font-size: 32px;
  line-height: 1.5;
  border: 1px solid #2a2f3a;
  box-sizing: border-box;
  color: #e6e6e6;
}

.promptInput::placeholder {
  color: #6b7280;
}

.charCount {
  color: #6b7280;
  font-size: 24px;
  text-align: right;
  margin-top: 8px;
  display: block;
}

.generateBtn {
  width: 100%;
  height: 80px;
  background: linear-gradient(135deg, #0fe0ff 0%, #9059e7 100%);
  color: #fff;
  border: none;
  border-radius: 25px;
  font-size: 32px;
  font-weight: 600;
  margin: 0;
  box-shadow: 0 4px 12px rgba(15, 224, 255, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  text-align: center;
}

.generateBtn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(15, 224, 255, 0.4);
}

.generateBtn:disabled {
  background: #374151;
  color: #6b7280;
  box-shadow: none;
  transform: none;
}

.saveHint {
  color: #9ca3af;
  font-size: 28px;
  text-align: center;
  margin-top: 10px;
  display: block;
} 