<template>
  <view class="edit-page">
    <!-- 加载状态 -->
    <Loading v-if="loading" text="加载中..." />
    
    <!-- 图片详情 -->
    <view v-else-if="imageDetail" class="image-detail">
      <!-- 图片展示 -->
      <view class="image-container">
        <image 
          :src="imageDetail.resultUrl || imageDetail.resultUrlPreview" 
          class="main-image"
          mode="aspectFit"
          @click="previewImage"
        />
        
        <!-- 状态遮罩 -->
        <view v-if="imageDetail.status !== 'completed'" class="status-overlay">
          <view v-if="imageDetail.status === 'processing'" class="processing">
            <view class="loading-spinner"></view>
            <text>生成中...</text>
          </view>
          <view v-else-if="imageDetail.status === 'failed'" class="failed">
            <text class="error-icon">❌</text>
            <text>生成失败</text>
            <text class="error-message">{{ imageDetail.errorMessage }}</text>
            <button class="retry-btn" @click="retryImage">重试</button>
          </view>
        </view>
      </view>
      
      <!-- 图片信息 -->
      <view class="image-info">
        <view class="info-item">
          <text class="label">风格:</text>
          <text class="value">{{ imageDetail.styleName }}</text>
        </view>
        <view class="info-item">
          <text class="label">创建时间:</text>
          <text class="value">{{ formatTime(imageDetail.createdAt) }}</text>
        </view>
        <view v-if="imageDetail.prompt" class="info-item">
          <text class="label">提示词:</text>
          <text class="value">{{ imageDetail.prompt }}</text>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button 
          v-if="imageDetail.status === 'completed'"
          class="btn btn-primary"
          @click="saveToAlbum"
        >
          保存到相册
        </button>
        
        <button 
          v-if="imageDetail.status === 'completed'"
          class="btn btn-secondary"
          @click="shareImage"
        >
          分享
        </button>
        
        <button 
          class="btn btn-secondary"
          @click="deleteImage"
        >
          删除
        </button>
      </view>
      
      <!-- 编辑功能 -->
      <view v-if="imageDetail.status === 'completed'" class="edit-section">
        <view class="section-title">重新编辑</view>
        <textarea 
          v-model="editPrompt"
          class="edit-input"
          placeholder="输入新的提示词来重新生成图片..."
          maxlength="200"
        />
        <button 
          class="btn btn-primary"
          :disabled="!editPrompt.trim() || isEditing"
          @click="editImage"
        >
          <text v-if="!isEditing">重新生成</text>
          <view v-else class="loading-container">
            <view class="loading-spinner"></view>
            <text>生成中...</text>
          </view>
        </button>
      </view>
    </view>
    
    <!-- 错误状态 -->
    <view v-else class="error-state">
      <text class="error-icon">😞</text>
      <text class="error-text">图片不存在或已被删除</text>
      <button class="btn btn-primary" @click="goBack">返回</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { imageApi } from '@/api/image'
import { Loading } from '@/components'
import type { ImageItem } from '@/types/image'
import { formatTime, saveImageToPhotosAlbum, previewImage as previewImageUtil } from '@/utils'

// 获取页面参数
const pages = getCurrentPages()
const currentPage = pages[pages.length - 1]
const imageId = currentPage.options?.id

// 响应式数据
const loading = ref(false)
const imageDetail = ref<ImageItem | null>(null)
const editPrompt = ref('')
const isEditing = ref(false)

// 方法
const loadImageDetail = async () => {
  if (!imageId) return
  
  try {
    loading.value = true
    const detail = await imageApi.getImageDetail(Number(imageId))
    imageDetail.value = detail
    editPrompt.value = detail.prompt || ''
  } catch (error) {
    console.error('加载图片详情失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

const previewImage = () => {
  if (imageDetail.value?.resultUrl) {
    previewImageUtil([imageDetail.value.resultUrl])
  }
}

const saveToAlbum = async () => {
  if (!imageDetail.value?.resultUrl) return
  
  try {
    await saveImageToPhotosAlbum(imageDetail.value.resultUrl)
  } catch (error) {
    console.error('保存图片失败:', error)
  }
}

const shareImage = () => {
  if (!imageDetail.value?.resultUrl) return
  
  // 这里可以实现分享功能
  uni.showActionSheet({
    itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
    success: (res) => {
      switch (res.tapIndex) {
        case 0:
          // 分享到微信
          break
        case 1:
          // 分享到朋友圈
          break
        case 2:
          // 复制链接
          uni.setClipboardData({
            data: imageDetail.value!.resultUrl!,
            success: () => {
              uni.showToast({
                title: '链接已复制',
                icon: 'success'
              })
            }
          })
          break
      }
    }
  })
}

const deleteImage = () => {
  uni.showModal({
    title: '确认删除',
    content: '删除后无法恢复，确定要删除这张图片吗？',
    success: async (res) => {
      if (res.confirm && imageDetail.value) {
        try {
          await imageApi.deleteImage(imageDetail.value.id)
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        } catch (error) {
          console.error('删除图片失败:', error)
          uni.showToast({
            title: '删除失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

const editImage = async () => {
  if (!imageDetail.value || !editPrompt.value.trim()) return
  
  try {
    isEditing.value = true
    
    const result = await imageApi.editImage(imageDetail.value.id, editPrompt.value.trim())
    
    if (result.success && result.data) {
      imageDetail.value = result.data
      uni.showToast({
        title: '重新生成中...',
        icon: 'success'
      })
    }
    
  } catch (error) {
    console.error('编辑图片失败:', error)
    uni.showToast({
      title: '编辑失败',
      icon: 'none'
    })
  } finally {
    isEditing.value = false
  }
}

const retryImage = async () => {
  if (!imageDetail.value) return
  
  try {
    uni.showLoading({ title: '重试中...' })
    
    const result = await imageApi.retryImage(imageDetail.value.id)
    
    if (result.success && result.data) {
      imageDetail.value = result.data
      uni.showToast({
        title: '已重新开始生成',
        icon: 'success'
      })
    }
    
  } catch (error) {
    console.error('重试失败:', error)
    uni.showToast({
      title: '重试失败',
      icon: 'none'
    })
  } finally {
    uni.hideLoading()
  }
}

const goBack = () => {
  uni.navigateBack()
}

// 生命周期
onMounted(() => {
  loadImageDetail()
})
</script>

<style lang="scss" scoped>
.edit-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0e17 0%, #1a1a2e 100%);
}

.image-detail {
  padding: 20px;
}

.image-container {
  position: relative;
  width: 100%;
  height: 400px;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.05);
  
  .main-image {
    width: 100%;
    height: 100%;
  }
  
  .status-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    
    .processing {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #0fe0ff;
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(15, 224, 255, 0.3);
        border-top: 3px solid #0fe0ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 12px;
      }
    }
    
    .failed {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #ff6b6b;
      text-align: center;
      
      .error-icon {
        font-size: 48px;
        margin-bottom: 12px;
      }
      
      .error-message {
        font-size: 12px;
        margin: 8px 0;
        opacity: 0.8;
      }
      
      .retry-btn {
        background: #0fe0ff;
        color: #000;
        border: none;
        border-radius: 20px;
        padding: 8px 16px;
        font-size: 14px;
        margin-top: 12px;
      }
    }
  }
}

.image-info {
  margin-bottom: 20px;
  
  .info-item {
    display: flex;
    margin-bottom: 12px;
    
    .label {
      color: #a3a3a3;
      font-size: 14px;
      width: 80px;
      flex-shrink: 0;
    }
    
    .value {
      color: #ffffff;
      font-size: 14px;
      flex: 1;
    }
  }
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 30px;
  
  .btn {
    flex: 1;
    height: 44px;
    border-radius: 22px;
    font-size: 14px;
    font-weight: 600;
    border: none;
    
    &.btn-primary {
      background: linear-gradient(135deg, #0fe0ff 0%, #00b4d8 100%);
      color: #000;
    }
    
    &.btn-secondary {
      background: transparent;
      color: #a3a3a3;
      border: 1px solid #333;
    }
  }
}

.edit-section {
  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 16px;
  }
  
  .edit-input {
    width: 100%;
    min-height: 80px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid #333;
    border-radius: 8px;
    padding: 12px;
    color: #ffffff;
    font-size: 14px;
    margin-bottom: 16px;
    
    &::placeholder {
      color: #666;
    }
  }
  
  .btn {
    width: 100%;
    height: 44px;
    border-radius: 22px;
    font-size: 16px;
    font-weight: 600;
    border: none;
    background: linear-gradient(135deg, #0fe0ff 0%, #00b4d8 100%);
    color: #000;
    
    &:disabled {
      background: #333;
      color: #666;
    }
    
    .loading-container {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      
      .loading-spinner {
        width: 20px;
        height: 20px;
        border: 2px solid rgba(0, 0, 0, 0.3);
        border-top: 2px solid #000;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
  
  .error-icon {
    font-size: 64px;
    margin-bottom: 20px;
  }
  
  .error-text {
    font-size: 16px;
    color: #a3a3a3;
    margin-bottom: 30px;
  }
  
  .btn {
    background: linear-gradient(135deg, #0fe0ff 0%, #00b4d8 100%);
    color: #000;
    border: none;
    border-radius: 25px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
