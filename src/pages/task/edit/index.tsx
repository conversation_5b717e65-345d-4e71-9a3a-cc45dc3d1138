import { useState, useEffect } from 'react'
import { View, Text, Image, Textarea, Button } from '@tarojs/components'
import Taro, { useRouter } from '@tarojs/taro'

import apis from '@/common/apis'
import type { ImageItem } from '@/common/apis/types'

import styles from './index.module.less'

const EditImage = () => {
  const router = useRouter()
  const { imageId } = router.params

  const [originalImage, setOriginalImage] = useState<ImageItem | null>(null)
  const [prompt, setPrompt] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [loading, setLoading] = useState(true)

  // 获取原图信息
  useEffect(() => {
    const getImageInfo = async () => {
      try {
        setLoading(true)

        const data = await apis.getImageDetail(Number(imageId))

        setOriginalImage(data)
      } catch (error) {
        console.error('获取图片信息失败:', error)
        Taro.showToast({
          title: '获取图片失败',
          icon: 'error',
        })

        Taro.navigateBack()
      } finally {
        setLoading(false)
      }
    }

    if (imageId) {
      getImageInfo()
    }
  }, [imageId])

  // 预览图片
  const handlePreviewImage = (imageUrl: string) => {
    Taro.previewImage({
      current: imageUrl,
      urls: [imageUrl],
    })
  }

  // 处理二次编辑
  const handleEdit = async () => {
    if (!prompt.trim()) {
      Taro.showToast({
        title: '请输入编辑描述',
        icon: 'none',
      })
      return
    }

    if (!originalImage) {
      Taro.showToast({
        title: '原图信息不存在',
        icon: 'error',
      })
      return
    }

    try {
      setIsGenerating(true)

      Taro.showLoading({
        title: '生成中...',
      })

      await apis.editImage(originalImage.id, prompt.trim())

      Taro.showToast({
        title: '提交成功',
        icon: 'success',
      })

      // 延迟返回上一页

      Taro.navigateBack()
    } catch (error) {
      console.error('二次编辑失败:', error)
      Taro.showToast({
        title: '生成失败',
        icon: 'error',
      })
    } finally {
      setIsGenerating(false)
      Taro.hideLoading()
    }
  }

  if (loading) {
    return (
      <View className={styles.container}>
        <View className={styles.loading}>
          <Text>加载中...</Text>
        </View>
      </View>
    )
  }

  if (!originalImage) {
    return (
      <View className={styles.container}>
        <View className={styles.error}>
          <Text>图片不存在</Text>
        </View>
      </View>
    )
  }

  return (
    <View className={styles.container}>
      {/* 原图展示 */}
      <View className={styles.section}>
        <Text className={styles.sectionTitle}>图片(点击查看大图)</Text>
        <View className={styles.imageContainer}>
          <Image
            src={originalImage.resultUrlPreview || ''}
            mode="aspectFit"
            className={styles.originalImage}
            onClick={() => handlePreviewImage(originalImage.resultUrlPreview || '')}
          />
        </View>
        <Text className={styles.styleInfo}>风格：{originalImage.styleName}</Text>
      </View>

      {/* 编辑描述输入 */}
      <View className={styles.section}>
        <Text className={styles.sectionTitle}>修改描述</Text>
        <Textarea
          className={styles.promptInput}
          placeholder="请描述你想要的修改效果，例如：改变背景颜色、添加元素、调整光线等..."
          value={prompt}
          onInput={(e) => setPrompt(e.detail.value)}
          maxlength={200}
          showConfirmBar={false}
        />
        <Text className={styles.charCount}>{prompt.length}/200</Text>
      </View>

      {/* 生成按钮 */}
      <View style={{ marginTop: '40px' }}>
        <Button
          className={styles.generateBtn}
          onClick={handleEdit}
          loading={isGenerating}
          disabled={isGenerating || !prompt.trim()}
        >
          {isGenerating ? '生成中...' : '开始生成'}
        </Button>
      </View>
    </View>
  )
}

export default EditImage
