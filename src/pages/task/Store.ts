import { makeAutoObservable, runInAction } from 'mobx'

import apis from '@/common/apis'
import type { ImageItem } from '@/common/apis/types'

class Store {
  imageList: ImageItem[] = []

  constructor() {
    makeAutoObservable(this)
  }

  getImageList = async () => {
    try {
      const data = await apis.getImageList({
        excludeStatuses: 'failed',
      })

      runInAction(() => {
        this.imageList = data.data
      })
    } catch (error) {
      console.error('获取图片列表失败:', error)
    }
  }

  deleteImage = async (id: number) => {
    await apis.deleteImage(id)

    runInAction(() => {
      this.imageList = this.imageList.filter((item) => item.id !== id)
    })
  }
}

export default Store
