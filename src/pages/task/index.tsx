import { useRef } from 'react'
import cls from 'classnames'
import { observer, useLocalObservable } from 'mobx-react'
import { View, Text, Image, ScrollView } from '@tarojs/components'
import Taro, { useDidHide, useDidShow, useShareAppMessage, useShareTimeline } from '@tarojs/taro'

import Store from './Store'
import commonApis from '@/common/apis'
import { COVER_URL } from '@/common/constants'
import { ImageStatus } from '@/common/apis/types'
import { ImageWithHeight, useAuth, useWaterfall } from '@/common/hooks'

import styles from './index.module.less'

const Task = () => {
  // 获取系统信息，用于设置顶部安全区域
  const systemInfo = Taro.getWindowInfo()
  const statusBarHeight = (systemInfo.statusBarHeight || 20) + 40

  const timer = useRef<NodeJS.Timeout | null>(null)
  const { imageList, getImageList, deleteImage } = useLocalObservable(() => new Store())
  const { userInfo } = useAuth()

  const { columns } = useWaterfall({
    imageList,
    imgKey: 'resultUrl',
  })

  useDidShow(() => {
    getImageList()

    timer.current = setInterval(() => {
      getImageList()
    }, 10000)
  })

  useDidHide(() => {
    if (timer.current) {
      clearInterval(timer.current)
    }
  })

  // 分享给好友
  useShareAppMessage(() => {
    return {
      title: '吉卜力风格AI - 让你的照片更有创意',
      path: `/pages/index/index?inviteCode=${userInfo?.inviteCode}`,
      imageUrl: COVER_URL,
    }
  })

  // 分享到朋友圈
  useShareTimeline(() => {
    return {
      title: '吉卜力风格AI - 让你的照片更有创意',
      path: `/pages/index/index?inviteCode=${userInfo?.inviteCode}`,
      imageUrl: COVER_URL,
    }
  })

  // 检查并申请权限
  const checkPermission = async (): Promise<boolean> => {
    try {
      const { authSetting } = await Taro.getSetting()
      // 如果未授权
      if (!authSetting['scope.writePhotosAlbum']) {
        // 发起授权请求
        const { confirm } = await Taro.showModal({
          title: '提示',
          content: '需要您授权保存图片到相册',
          confirmText: '去授权',
          cancelText: '取消',
        })

        if (confirm) {
          try {
            await Taro.authorize({ scope: 'scope.writePhotosAlbum' })
            return true
          } catch (err) {
            // 如果用户拒绝授权，引导用户去设置页面手动授权
            const { confirm: openSetting } = await Taro.showModal({
              title: '提示',
              content: '您需要授权才能保存图片，是否去设置页面授权？',
              confirmText: '去设置',
              cancelText: '取消',
            })

            if (openSetting) {
              const res = await Taro.openSetting()
              return !!res.authSetting['scope.writePhotosAlbum']
            }
            return false
          }
        }
        return false
      }
      return true
    } catch (err) {
      console.error('获取授权设置失败：', err)
      return false
    }
  }

  // 保存图片到本地
  const handleSaveImage = async (imageUrl: string) => {
    if (!imageUrl) {
      Taro.showToast({
        title: '图片地址为空',
        icon: 'none',
      })
      return
    }

    // 检查权限
    const hasPermission = await checkPermission()
    if (!hasPermission) {
      return
    }

    Taro.showLoading({
      title: '保存中...',
    })

    // 先下载图片到本地临时文件
    Taro.downloadFile({
      url: imageUrl,
      success: (res) => {
        // 保存临时文件到相册
        Taro.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            Taro.hideLoading()
            Taro.showToast({
              title: '保存成功',
              icon: 'success',
            })
          },
          fail: (err) => {
            Taro.hideLoading()
            Taro.showToast({
              title: '保存失败',
              icon: 'none',
            })
            console.error('保存图片失败：', err)
          },
        })
      },
      fail: (err) => {
        Taro.hideLoading()
        Taro.showToast({
          title: '下载图片失败',
          icon: 'none',
        })
        console.error('下载图片失败：', err)
      },
    })
  }

  // 处理长按删除
  const handleLongPress = (id: number, item: any) => {
    const itemList = item.url ? ['编辑', '删除'] : ['删除']

    console.log('item', item, itemList)

    Taro.showActionSheet({
      itemList,
      success: async (res) => {
        if (item.url && res.tapIndex === 0) {
          // 二次编辑
          Taro.navigateTo({
            url: `/pages/task/edit/index?imageId=${id}`,
          })
        } else if ((item.url && res.tapIndex === 1) || (!item.url && res.tapIndex === 0)) {
          // 删除
          const { confirm } = await Taro.showModal({
            title: '确认删除',
            content: '确定要删除这张图片吗？',
            confirmText: '删除',
            confirmColor: '#ff4d4f',
            cancelText: '取消',
          })

          if (confirm) {
            try {
              await deleteImage(id)
              Taro.showToast({
                title: '删除成功',
                icon: 'success',
              })
              getImageList() // 刷新列表
            } catch (error) {
              Taro.showToast({
                title: '删除失败',
                icon: 'error',
              })
            }
          }
        }
      },
    })
  }

  const handleRetry = async (id: number) => {
    const { status } = await commonApis.retryImage(id)

    if (status === ImageStatus.PROCESSING) {
      getImageList()

      Taro.showToast({
        title: '重试成功',
        icon: 'success',
      })
    }
  }

  const renderCard = (item: ImageWithHeight) => {
    switch (item.status) {
      case ImageStatus.COMPLETED:
        return (
          <View
            key={item.id}
            className={styles.imageCard}
            onClick={() => handleSaveImage(item.url)}
            onLongPress={() => handleLongPress(item.id, item)}
          >
            <Image
              lazyLoad
              src={item.preview || item.url}
              mode="widthFix"
              className={styles.image}
              style={{ height: item.height }}
            />
          </View>
        )
      case ImageStatus.FAILED:
        return (
          <View
            key={item.id}
            className={cls(styles.imageCard, styles.failedContainer)}
            onClick={() => handleRetry(item.id)}
          >
            <svg viewBox="0 0 1024 1024" width="32" height="32" fill="#d81e06">
              <path
                d="M520 672L256 413.44l-109.76 93.76V246.72h261.12a41.6 41.6 0 1 0 0-82.88H104.64A41.6 41.6 0 0 0 64 205.44V800a41.6 41.6 0 0 0 41.6 41.6h267.84a40.96 40.96 0 0 0 32-67.52h21.76z"
                p-id="7836"
                fill="#d81e06"
              />
              <path
                d="M952 211.52a41.92 41.92 0 0 0-28.48-15.68l-310.08-32a41.6 41.6 0 0 0-8.32 82.88l267.2 27.52-55.04 411.84-113.28-160-99.2 17.92 42.56 96-123.84 123.52-32-1.6a41.6 41.6 0 1 0-4.16 82.88l352 17.92h1.92a41.28 41.28 0 0 0 41.28-35.84L960 242.88a42.56 42.56 0 0 0-8-31.36z"
                p-id="7837"
                fill="#d81e06"
              />
              <path
                d="M695.36 397.44m-64 0a64 64 0 1 0 128 0 64 64 0 1 0-128 0Z"
                p-id="7838"
                fill="#d81e06"
              />
            </svg>
            <Text className={styles.failedText}>生成失败, 点击重试</Text>
          </View>
        )
      case ImageStatus.PROCESSING:
        return (
          <View key={item.id} className={cls(styles.imageCard, styles.loadingContainer)}>
            <View className={styles.loadingIcon}></View>
            <Text className={styles.loadingText}>生成中，预计1-2分钟</Text>
          </View>
        )
      default:
    }
  }

  return (
    <View className={styles.container}>
      {/* 状态栏占位 */}
      <View style={{ height: `${statusBarHeight}px` }} />

      {/* 保存提示 */}
      {imageList.length > 0 && (
        <View className={styles.saveText}>
          <Text>点击图片可保存到相册，长按可进行编辑或删除</Text>
        </View>
      )}

      {imageList.length > 0 ? (
        <ScrollView scrollY className={styles.waterfall}>
          <View className={styles.columns}>
            {columns.map((column, columnIndex) => (
              <View key={columnIndex} className={styles.column}>
                {column.map((item) => renderCard(item))}
              </View>
            ))}
          </View>
        </ScrollView>
      ) : (
        <View className={styles.emptyTip}>
          <Text>您还没未创建过图片，快去创建吧</Text>
        </View>
      )}
    </View>
  )
}

export default observer(Task)
