import React from 'react'
import { <PERSON>, Button, View, Text } from '@tarojs/components'
import Taro from '@tarojs/taro'

import styles from './index.module.less'

const SERVICE_WECHAT_IMAGE_URL = 'https://cdn-img.convert.pub/style/service-wechat.png'

const Discount: React.FC = () => {
  const handleDownload = async () => {
    try {
      Taro.showLoading({
        title: '正在保存...',
      })
      const res = await Taro.downloadFile({
        url: SERVICE_WECHAT_IMAGE_URL,
      })

      if (res.statusCode === 200) {
        await Taro.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
        })
        Taro.showToast({
          title: '保存成功',
          icon: 'success',
        })
      }
    } catch (error: unknown) {
      console.log(error)
    } finally {
      Taro.hideLoading()
    }
  }

  const handleCopy = () => {
    Taro.setClipboardData({
      data: 'SHM_9804',
    })
  }

  return (
    <View className={styles.container}>
      <View className={styles.qrCodeWrapper}>
        <Image src={SERVICE_WECHAT_IMAGE_URL} mode="widthFix" />
      </View>

      <View className={styles.descriptionWrapper}>
        <View className={styles.description} onClick={handleCopy}>
          点击复制客服微信名: <Text className={styles.descriptionText}>SHM_9804</Text>
        </View>
        <View className={styles.description}>或</View>
        <View className={styles.description}>扫码添加客服微信，获取专享福利</View>
      </View>

      <Button className={styles.downloadBtn} onClick={handleDownload}>
        保存图片到相册
      </Button>
    </View>
  )
}

export default Discount
