.container {
  min-height: 100vh;
  background: #0a0e17;
  background-image:
    radial-gradient(circle at 10% 10%, rgba(15, 224, 255, 0.1) 0%, transparent 25%),
    radial-gradient(circle at 90% 90%, rgba(144, 89, 231, 0.1) 0%, transparent 25%);
  padding: 40px 32px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.title::after {
  content: '';
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(to right, #0fe0ff, #03a1ff);
  border-radius: 2px;
}

.qrCodeWrapper {
  background: rgba(15, 224, 255, 0.05);
  padding: 32px;
  border-radius: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  margin: 40px auto;
  width: fit-content;
  border: 1px solid rgba(15, 224, 255, 0.2);
  position: relative;
}

.qrCodeWrapper image {
  width: 280px;
  height: 280px;
  border-radius: 12px;
}

.descriptionWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin: 40px 0 48px;
}

.description {
  text-align: center;
  color: #a3a3a3;
  font-size: 32px;
  line-height: 1.6;
}

.descriptionText {
  color: #fff;
}

.downloadBtn {
  width: 90%;
  height: 88px;
  line-height: 88px;
  background: linear-gradient(135deg, #0fe0ff, #03a1ff);
  color: #0a0e17;
  border-radius: 44px;
  font-size: 32px;
  font-weight: 500;
  margin: 0 auto;
  display: block;
  box-shadow: 0 4px 12px rgba(15, 224, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.downloadBtn:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(15, 224, 255, 0.2);
}

.downloadBtn::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: rgba(255, 255, 255, 0.1);
  transform: rotate(45deg);
  transition: all 0.3s ease;
  opacity: 0;
}

.downloadBtn:active::after {
  opacity: 1;
}

.loadingText {
  color: #a0aec0;
  font-size: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.saveSuccess {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(15, 224, 255, 0.1);
  border: 1px solid rgba(15, 224, 255, 0.3);
  padding: 24px 48px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  animation: fadeIn 0.3s ease;
  z-index: 1000;
}

.successIcon {
  color: #0fe0ff;
  font-size: 40px;
  text-shadow: 0 0 10px rgba(15, 224, 255, 0.5);
}

.successText {
  color: #e6e6e6;
  font-size: 32px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -40%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}
