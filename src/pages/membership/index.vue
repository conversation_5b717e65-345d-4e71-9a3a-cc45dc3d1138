<template>
  <view class="membership-page">
    <!-- 头部 -->
    <view class="header">
      <text class="title">会员中心</text>
      <text class="subtitle">解锁更多精彩功能</text>
    </view>

    <!-- 用户状态 -->
    <view v-if="userInfo" class="user-status">
      <view class="status-card">
        <view class="status-info">
          <text class="current-points">当前积分: {{ userInfo.points || 0 }}</text>
          <view v-if="userInfo.isActiveVip" class="vip-status">
            <text class="vip-badge">VIP会员</text>
            <text class="vip-expire">到期时间: {{ formatVipExpire(userInfo.vipExpireAt) }}</text>
          </view>
          <text v-else class="normal-status">普通用户</text>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <Loading v-if="loading" text="加载套餐中..." />

    <!-- 套餐列表 -->
    <view v-else class="packages-section">
      <!-- VIP套餐 -->
      <view v-if="vipPackages.length > 0" class="package-category">
        <view class="category-title">
          <text class="title-text">VIP会员</text>
          <text class="title-desc">无限次数，畅享所有功能</text>
        </view>
        
        <view class="packages-grid">
          <view 
            v-for="pkg in vipPackages" 
            :key="pkg.id"
            class="package-card vip-package"
            :class="{ recommended: pkg.isRecommended }"
            @click="selectPackage(pkg)"
          >
            <view v-if="pkg.isRecommended" class="recommended-badge">推荐</view>
            
            <view class="package-header">
              <text class="package-name">{{ pkg.name }}</text>
              <view class="package-price">
                <text class="price">¥{{ (pkg.price / 100).toFixed(2) }}</text>
                <text v-if="pkg.originalPrice" class="original-price">
                  ¥{{ (pkg.originalPrice / 100).toFixed(2) }}
                </text>
              </view>
            </view>
            
            <view class="package-duration">
              <text>{{ pkg.duration }}天</text>
            </view>
            
            <view class="package-features">
              <text 
                v-for="feature in pkg.features" 
                :key="feature"
                class="feature-item"
              >
                ✓ {{ feature }}
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 积分套餐 -->
      <view v-if="pointPackages.length > 0" class="package-category">
        <view class="category-title">
          <text class="title-text">积分套餐</text>
          <text class="title-desc">按次付费，灵活使用</text>
        </view>
        
        <view class="packages-grid">
          <view 
            v-for="pkg in pointPackages" 
            :key="pkg.id"
            class="package-card point-package"
            :class="{ recommended: pkg.isRecommended }"
            @click="selectPackage(pkg)"
          >
            <view v-if="pkg.isRecommended" class="recommended-badge">推荐</view>
            
            <view class="package-header">
              <text class="package-name">{{ pkg.name }}</text>
              <view class="package-price">
                <text class="price">¥{{ (pkg.price / 100).toFixed(2) }}</text>
                <text v-if="pkg.originalPrice" class="original-price">
                  ¥{{ (pkg.originalPrice / 100).toFixed(2) }}
                </text>
              </view>
            </view>
            
            <view class="package-points">
              <text>{{ pkg.points }}积分</text>
            </view>
            
            <view class="package-features">
              <text 
                v-for="feature in pkg.features" 
                :key="feature"
                class="feature-item"
              >
                ✓ {{ feature }}
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 购买按钮 -->
    <view v-if="selectedPackage" class="purchase-section">
      <button 
        class="purchase-btn"
        :disabled="isPurchasing"
        @click="purchasePackage"
      >
        <text v-if="!isPurchasing">
          立即购买 - ¥{{ (selectedPackage.price / 100).toFixed(2) }}
        </text>
        <view v-else class="loading-container">
          <view class="loading-spinner"></view>
          <text>处理中...</text>
        </view>
      </button>
    </view>

    <!-- 说明文字 -->
    <view class="notice-section">
      <text class="notice-text">• 购买后立即生效</text>
      <text class="notice-text">• 支持微信支付</text>
      <text class="notice-text">• 如有问题请联系客服</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { packagesApi } from '@/api/packages'
import { orderApi } from '@/api/order'
import { Loading } from '@/components'
import type { PackageItem } from '@/types/package'
import { PackageType } from '@/types/package'
import dayjs from 'dayjs'

// 状态管理
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const packages = ref<PackageItem[]>([])
const selectedPackage = ref<PackageItem | null>(null)
const isPurchasing = ref(false)

// 计算属性
const userInfo = computed(() => authStore.userInfo)

const vipPackages = computed(() => 
  packages.value.filter(pkg => pkg.type === PackageType.VIP)
)

const pointPackages = computed(() => 
  packages.value.filter(pkg => pkg.type === PackageType.POINTS)
)

// 方法
const loadPackages = async () => {
  try {
    loading.value = true
    const data = await packagesApi.getPackageList()
    packages.value = data.sort((a, b) => a.sort - b.sort)
  } catch (error) {
    console.error('加载套餐失败:', error)
    uni.showToast({
      title: '加载套餐失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

const selectPackage = (pkg: PackageItem) => {
  selectedPackage.value = pkg
}

const purchasePackage = async () => {
  if (!selectedPackage.value) return
  
  // 检查登录状态
  const isLoggedIn = await authStore.checkLogin()
  if (!isLoggedIn) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }
  
  try {
    isPurchasing.value = true
    
    // 创建订单
    const orderResult = await orderApi.createOrder({
      packageId: selectedPackage.value.id
    })
    
    // 调用微信支付
    await uni.requestPayment({
      ...orderResult.paymentData,
      success: async () => {
        // 支付成功，确认订单
        await orderApi.confirmPayment(orderResult.orderId, orderResult.paymentData)
        
        uni.showToast({
          title: '购买成功',
          icon: 'success'
        })
        
        // 刷新用户信息
        await authStore.refreshUserInfo()
        
        // 清除选中的套餐
        selectedPackage.value = null
      },
      fail: (error) => {
        console.error('支付失败:', error)
        if (error.errMsg !== 'requestPayment:fail cancel') {
          uni.showToast({
            title: '支付失败',
            icon: 'none'
          })
        }
      }
    })
    
  } catch (error) {
    console.error('购买失败:', error)
    uni.showToast({
      title: '购买失败',
      icon: 'none'
    })
  } finally {
    isPurchasing.value = false
  }
}

const formatVipExpire = (expireAt?: string) => {
  if (!expireAt) return ''
  return dayjs(expireAt).format('YYYY-MM-DD')
}

// 生命周期
onMounted(async () => {
  // 检查登录状态
  await authStore.checkLogin()
  
  // 加载套餐列表
  await loadPackages()
})
</script>

<style lang="scss" scoped>
.membership-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0e17 0%, #1a1a2e 100%);
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 24px;
  
  .title {
    font-size: 24px;
    font-weight: bold;
    color: #0fe0ff;
    display: block;
    margin-bottom: 8px;
  }
  
  .subtitle {
    font-size: 14px;
    color: #a3a3a3;
  }
}

.user-status {
  margin-bottom: 24px;
  
  .status-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    
    .status-info {
      .current-points {
        font-size: 16px;
        color: #0fe0ff;
        display: block;
        margin-bottom: 8px;
      }
      
      .vip-status {
        .vip-badge {
          background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
          color: #000;
          font-size: 12px;
          font-weight: 600;
          padding: 4px 8px;
          border-radius: 12px;
          margin-right: 8px;
        }
        
        .vip-expire {
          font-size: 12px;
          color: #a3a3a3;
        }
      }
      
      .normal-status {
        font-size: 14px;
        color: #a3a3a3;
      }
    }
  }
}

.packages-section {
  .package-category {
    margin-bottom: 32px;
    
    .category-title {
      margin-bottom: 16px;
      
      .title-text {
        font-size: 18px;
        font-weight: 600;
        color: #ffffff;
        display: block;
        margin-bottom: 4px;
      }
      
      .title-desc {
        font-size: 12px;
        color: #a3a3a3;
      }
    }
    
    .packages-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
      gap: 12px;
      
      .package-card {
        position: relative;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 16px;
        text-align: center;
        transition: all 0.3s ease;
        
        &:active {
          transform: scale(0.98);
        }
        
        &.recommended {
          border-color: #0fe0ff;
          box-shadow: 0 0 12px rgba(15, 224, 255, 0.2);
        }
        
        &.vip-package {
          background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 237, 78, 0.1) 100%);
        }
        
        .recommended-badge {
          position: absolute;
          top: -8px;
          right: 8px;
          background: #0fe0ff;
          color: #000;
          font-size: 10px;
          font-weight: 600;
          padding: 4px 8px;
          border-radius: 8px;
        }
        
        .package-header {
          margin-bottom: 12px;
          
          .package-name {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            display: block;
            margin-bottom: 8px;
          }
          
          .package-price {
            .price {
              font-size: 20px;
              font-weight: bold;
              color: #0fe0ff;
            }
            
            .original-price {
              font-size: 12px;
              color: #a3a3a3;
              text-decoration: line-through;
              margin-left: 4px;
            }
          }
        }
        
        .package-duration,
        .package-points {
          font-size: 14px;
          color: #ffd700;
          margin-bottom: 12px;
        }
        
        .package-features {
          .feature-item {
            display: block;
            font-size: 10px;
            color: #a3a3a3;
            margin-bottom: 4px;
            text-align: left;
          }
        }
      }
    }
  }
}

.purchase-section {
  margin: 32px 0;
  
  .purchase-btn {
    width: 100%;
    height: 50px;
    background: linear-gradient(135deg, #0fe0ff 0%, #00b4d8 100%);
    border: none;
    border-radius: 25px;
    color: #000;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:disabled {
      background: #333;
      color: #666;
    }
    
    .loading-container {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .loading-spinner {
        width: 20px;
        height: 20px;
        border: 2px solid rgba(0, 0, 0, 0.3);
        border-top: 2px solid #000;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }
}

.notice-section {
  text-align: center;
  
  .notice-text {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
