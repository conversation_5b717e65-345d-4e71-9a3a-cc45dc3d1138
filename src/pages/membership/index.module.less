.container {
  min-height: 100vh;
  background-color: #0a0e17;
  padding: 32px;
  background-image:
    radial-gradient(circle at 10% 20%, rgba(15, 224, 255, 0.1) 0%, transparent 20%),
    radial-gradient(circle at 90% 80%, rgba(144, 89, 231, 0.1) 0%, transparent 20%);
}

.pageHeader {
  margin-bottom: 32px;
}

.title {
  font-size: 42px;
  font-weight: bold;
  color: #e6e6e6;
  margin-bottom: 12px;
  text-shadow: 0 0 10px rgba(15, 224, 255, 0.5);
  position: relative;
  display: inline-block;
}

.title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(to right, #0fe0ff, #03a1ff);
  border-radius: 3px;
}

.subTitle {
  font-size: 26px;
  color: #a3a3a3;
  display: block;
  margin-top: 16px;
  letter-spacing: 1px;
}

.tabContainer {
  display: flex;
  margin-bottom: 32px;
  background: linear-gradient(145deg, #171c26, #1a1f2a);
  border-radius: 20px;
  padding: 6px;
  border: 1px solid #2a2f3a;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.tabContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(15, 224, 255, 0.3), transparent);
}

.tabItem {
  flex: 1;
  text-align: center;
  padding: 18px 0;
  font-size: 30px;
  font-weight: 600;
  color: #a3a3a3;
  border-radius: 16px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  z-index: 2;
}

.tabItem::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(15, 224, 255, 0) 0%, 
    rgba(15, 224, 255, 0.05) 50%, 
    rgba(3, 161, 255, 0) 100%);
  border-radius: 16px;
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: -1;
}

.tabItem:hover::before {
  opacity: 1;
}

.tabItem:hover {
  color: #c9c9c9;
  transform: translateY(-1px);
}

.tabItem.active {
  color: #ffffff;
  background: linear-gradient(135deg, 
    rgba(15, 224, 255, 0.2) 0%, 
    rgba(3, 161, 255, 0.15) 50%, 
    rgba(15, 224, 255, 0.1) 100%);
  text-shadow: 0 0 12px rgba(15, 224, 255, 0.6);
  box-shadow: 
    0 4px 20px rgba(15, 224, 255, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  border: 1px solid rgba(15, 224, 255, 0.3);
}

.tabItem.active::before {
  opacity: 0;
}

.tabItem.active::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #0fe0ff, #03a1ff, #0fe0ff);
  border-radius: 16px 16px 0 0;
  animation: shimmer 2s ease-in-out infinite;
}

.tabItem:active {
  transform: translateY(0);
}

@keyframes shimmer {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.packageGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

.packageCard {
  background: linear-gradient(145deg, #171c26, #1a1f2a);
  border-radius: 20px;
  box-shadow: 
    0 12px 32px rgba(0, 0, 0, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);
  padding: 28px 24px;
  border: 1px solid rgba(42, 47, 58, 0.8);
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.packageCard:hover {
  transform: translateY(-4px);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.7),
    0 0 30px rgba(15, 224, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.12);
  border-color: rgba(15, 224, 255, 0.3);
}

.packageCard:active {
  transform: translateY(-2px);
  box-shadow:
    0 12px 24px rgba(0, 0, 0, 0.6),
    0 0 20px rgba(15, 224, 255, 0.4);
}

.packageCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #0fe0ff, #03a1ff, #0fe0ff);
  border-radius: 20px 20px 0 0;
}

.packageCard::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 35%;
  height: 100%;
  background: linear-gradient(135deg, 
    transparent 85%, 
    rgba(15, 224, 255, 0.08) 95%, 
    rgba(15, 224, 255, 0.15) 100%);
  pointer-events: none;
  border-radius: 0 20px 20px 0;
}

.packageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
}

.packageHeader::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, rgba(15, 224, 255, 0.3), transparent);
}

.packageName {
  font-size: 36px;
  font-weight: bold;
  color: #e6e6e6;
  text-shadow: 0 0 5px rgba(15, 224, 255, 0.3);
  position: relative;
  z-index: 1;
}

.packagePrice {
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 4px;
  font-size: 42px;
  font-weight: bold;
  color: #0fe0ff;
  text-shadow: 0 0 10px rgba(15, 224, 255, 0.5);
  position: relative;
  z-index: 1;
}

.originalPrice {
  font-size: 32px;
  text-decoration: line-through;
  color: #999;
}

.features {
  display: flex;
  flex-direction: column;
  gap: 14px;
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
}

.featureItem {
  display: flex;
  align-items: center;
}

.checkIcon {
  color: #0fe0ff;
  margin-right: 12px;
  font-size: 30px;
  text-shadow: 0 0 5px rgba(15, 224, 255, 0.5);
}

.featureText {
  color: #a3a3a3;
  font-size: 28px;
}

.buyButton {
  width: 60%;
  margin: 0 auto;
  background: linear-gradient(135deg, #0fe0ff, #03a1ff, #0fe0ff);
  color: #0a0e17;
  border-radius: 16px;
  padding: 12px 0;
  font-size: 30px;
  font-weight: 700;
  box-shadow:
    0 6px 20px rgba(15, 224, 255, 0.4),
    0 0 30px rgba(15, 224, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  letter-spacing: 1.2px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
}

.buyButton:hover {
  transform: translateY(-2px);
  box-shadow:
    0 10px 30px rgba(15, 224, 255, 0.5),
    0 0 40px rgba(15, 224, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  background: linear-gradient(135deg, #1aebff, #08a6ff, #1aebff);
}

.buyButton:active {
  transform: translateY(0);
  box-shadow:
    0 4px 15px rgba(15, 224, 255, 0.4),
    0 0 25px rgba(15, 224, 255, 0.2);
}

.buyButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.3), 
    transparent);
  transition: left 0.6s ease;
  z-index: 1;
}

.buyButton:hover::before {
  left: 100%;
}

.privilegesCard {
  background-color: #171c26;
  border-radius: 16px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.5);
  padding: 24px;
  margin-top: 32px;
  border: 1px solid #2a2f3a;
  position: relative;
  overflow: hidden;
}

.privilegesCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(to right, #0fe0ff, #03a1ff);
}

.privilegesCard::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 30%;
  height: 100%;
  background: linear-gradient(45deg, transparent 95%, #0fe0ff 100%);
  pointer-events: none;
}

.privilegesTitle {
  font-size: 36px;
  font-weight: bold;
  color: #e6e6e6;
  margin-bottom: 28px;
  text-shadow: 0 0 10px rgba(15, 224, 255, 0.5);
  position: relative;
  display: inline-block;
}

.privilegesTitle::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 50px;
  height: 3px;
  background: linear-gradient(to right, #0fe0ff, #03a1ff);
  border-radius: 3px;
}

.privilegesList {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.privilegeItem {
  display: flex;
  align-items: flex-start;
  background-color: rgba(15, 224, 255, 0.05);
  padding: 16px;
  border-radius: 12px;
  border-left: 3px solid #0fe0ff;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
  position: relative;
}

.privilegeItem:active {
  transform: translateX(2px);
  box-shadow: 0 0 10px rgba(15, 224, 255, 0.2);
}

.privilegeIcon {
  font-size: 40px;
  margin-right: 20px;
  background: linear-gradient(135deg, #0fe0ff, #03a1ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 5px rgba(15, 224, 255, 0.3);
  position: relative;
}

.privilegeIcon::before {
  content: '';
  position: absolute;
  top: -8px;
  left: -8px;
  width: calc(100% + 16px);
  height: calc(100% + 16px);
  background: rgba(15, 224, 255, 0.1);
  border-radius: 50%;
  z-index: -1;
}

.privilegeContent {
  flex: 1;
}

.privilegeTitle {
  color: #e6e6e6;
  font-weight: 500;
  display: block;
  font-size: 32px;
  margin-bottom: 6px;
  text-shadow: 0 0 5px rgba(15, 224, 255, 0.3);
}

.privilegeDesc {
  color: #a3a3a3;
  font-size: 26px;
}
