import Taro from '@tarojs/taro'
import { useEffect, useState } from 'react'
import { View, Text, Button } from '@tarojs/components'
import { observer, useLocalObservable } from 'mobx-react-lite'

import Store from './Store'
import styles from './index.module.less'

const Membership = observer(() => {
  const { packageList, getPackageList, createOrder } = useLocalObservable(() => new Store())
  const [activeTab, setActiveTab] = useState<'credits' | 'unlimited'>('credits')

  useEffect(() => {
    getPackageList()

    // 设置导航栏颜色
    Taro.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#0a0e17',
    })
  }, [])

  const handleBuyPackage = async (planId: number) => {
    // 付款
    const data = await createOrder(planId, 'wechat')

    if (data) {
      Taro.requestPayment({
        ...data.payment,
        success: () => {
          Taro.showToast({
            title: '开通成功',
            icon: 'success',
            duration: 2000,
          })
        },
        fail: () => {
          Taro.showToast({
            title: '支付取消',
            icon: 'none',
            duration: 2000,
          })
        },
      })
    }
  }

  // 根据tab筛选套餐
  const filteredPackages = packageList.filter(plan => {
    if (activeTab === 'credits') {
      // 次数卡：没有vipDuration的套餐
      return !plan.vipDuration
    } else {
      // 无限会员：有vipDuration的套餐
      return plan.vipDuration
    }
  })

  // 自定义特权图标
  const privilegeIcons = {
    speed: '⚡',
    quality: '🔍',
    count: '🚀',
  }

  return (
    <View className={styles.container}>
      <View className={styles.pageHeader}>
        <View className={styles.title}>会员套餐</View>
        <Text className={styles.subTitle}>解锁高级功能和更多创作权限</Text>
      </View>

      {/* Tab切换 */}
      <View className={styles.tabContainer}>
        <View 
          className={`${styles.tabItem} ${activeTab === 'credits' ? styles.active : ''}`}
          onClick={() => setActiveTab('credits')}
        >
          次数卡
        </View>
        <View 
          className={`${styles.tabItem} ${activeTab === 'unlimited' ? styles.active : ''}`}
          onClick={() => setActiveTab('unlimited')}
        >
          无限次数会员
        </View>
      </View>

      <View className={styles.packageGrid}>
        {filteredPackages.map((plan) => (
          <View key={plan.id} className={styles.packageCard}>
            <View className={styles.packageHeader}>
              <View className={styles.packageName}>{plan.name}</View>
              <View className={styles.packagePrice}>
                <Text className={styles.price}>￥{plan.priceCNY}</Text>
                <Text className={styles.originalPrice}>原价：￥{plan.originalPriceCNY}</Text>
              </View>
            </View>

            <View className={styles.features}>
              {!plan.vipDuration && (
                <>
                  <View className={styles.featureItem}>
                    <Text className={styles.checkIcon}>✓</Text>
                    <Text className={styles.featureText}>积分 {plan.points} </Text>
                  </View>
                  <View className={styles.featureItem}>
                    <Text className={styles.checkIcon}>✓</Text>
                    <Text className={styles.featureText}>
                      包含 {Math.ceil(plan.points / 3)} 次AI转换
                    </Text>
                  </View>
                </>
              )}
              {plan.vipDuration && (
                <View className={styles.featureItem}>
                  <Text className={styles.checkIcon}>✓</Text>
                  <Text className={styles.featureText}>{plan.description}</Text>
                </View>
              )}
              <View className={styles.featureItem}>
                <Text className={styles.checkIcon}>✓</Text>
                <Text className={styles.featureText}>高分辨率图片生成</Text>
              </View>
            </View>

            <Button className={styles.buyButton} onClick={() => handleBuyPackage(plan.id)}>
              开通{plan.name}
            </Button>
          </View>
        ))}
      </View>

      <View className={styles.privilegesCard}>
        <View className={styles.privilegesTitle}>会员特权</View>
        <View className={styles.privilegesList}>
          <View className={styles.privilegeItem}>
            <View className={styles.privilegeIcon}>{privilegeIcons.count}</View>
            <View className={styles.privilegeContent}>
              <Text className={styles.privilegeTitle}>更多使用次数</Text>
              <Text className={styles.privilegeDesc}>会员每月可使用更多AI转换次数</Text>
            </View>
          </View>
          <View className={styles.privilegeItem}>
            <View className={styles.privilegeIcon}>{privilegeIcons.quality}</View>
            <View className={styles.privilegeContent}>
              <Text className={styles.privilegeTitle}>高清图片</Text>
              <Text className={styles.privilegeDesc}>生成更高分辨率的图片</Text>
            </View>
          </View>
          <View className={styles.privilegeItem}>
            <View className={styles.privilegeIcon}>{privilegeIcons.speed}</View>
            <View className={styles.privilegeContent}>
              <Text className={styles.privilegeTitle}>优先处理</Text>
              <Text className={styles.privilegeDesc}>图片转换请求优先处理</Text>
            </View>
          </View>
        </View>
      </View>
    </View>
  )
})

export default Membership
