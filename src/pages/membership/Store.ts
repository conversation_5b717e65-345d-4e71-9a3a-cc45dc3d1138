import { makeAutoObservable } from 'mobx'

import apis from '@/common/apis'
import type { PackageItem } from '@/common/apis/packages/types'

class Store {
  packageList: PackageItem[] = []

  constructor() {
    makeAutoObservable(this)
  }

  getPackageList = async () => {
    const data = await apis.getPackageList()

    this.packageList = data
  }

  createOrder = async (packageId: number, paymentMethod: 'wechat' | 'alipay') => {
    const data = await apis.createOrder({ packageId, paymentMethod })

    return data
  }
}

export default Store
