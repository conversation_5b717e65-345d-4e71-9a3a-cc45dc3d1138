<template>
  <view class="guide-page">
    <!-- 头部 -->
    <view class="header">
      <text class="title">使用指南</text>
      <text class="subtitle">快速上手吉卜力风格AI</text>
    </view>

    <!-- 指南内容 -->
    <view class="guide-content">
      <!-- 步骤1 -->
      <view class="guide-step">
        <view class="step-header">
          <view class="step-number">1</view>
          <text class="step-title">选择风格</text>
        </view>
        <view class="step-content">
          <text class="step-text">在首页选择你喜欢的吉卜力风格，每种风格都有独特的艺术特色。</text>
          <view class="step-image-placeholder">
            <text class="placeholder-text">风格选择示意图</text>
          </view>
        </view>
      </view>

      <!-- 步骤2 -->
      <view class="guide-step">
        <view class="step-header">
          <view class="step-number">2</view>
          <text class="step-title">上传照片</text>
        </view>
        <view class="step-content">
          <text class="step-text">点击上传区域，选择你想要转换的照片。建议使用清晰、光线良好的照片。</text>
          <view class="step-image-placeholder">
            <text class="placeholder-text">照片上传示意图</text>
          </view>
        </view>
      </view>

      <!-- 步骤3 -->
      <view class="guide-step">
        <view class="step-header">
          <view class="step-number">3</view>
          <text class="step-title">开始生成</text>
        </view>
        <view class="step-content">
          <text class="step-text">点击"开始生成"按钮，AI将为你创造独特的吉卜力风格图片。</text>
          <image src="/static/guide/step3.png" class="step-image" mode="aspectFit" />
        </view>
      </view>

      <!-- 步骤4 -->
      <view class="guide-step">
        <view class="step-header">
          <view class="step-number">4</view>
          <text class="step-title">查看结果</text>
        </view>
        <view class="step-content">
          <text class="step-text">在"我的画廊"中查看生成的作品，可以保存、分享或重新编辑。</text>
          <image src="/static/guide/step4.png" class="step-image" mode="aspectFit" />
        </view>
      </view>
    </view>

    <!-- 使用技巧 -->
    <view class="tips-section">
      <view class="section-title">
        <text class="title-text">使用技巧</text>
      </view>
      
      <view class="tips-list">
        <view class="tip-item">
          <view class="tip-icon">💡</view>
          <view class="tip-content">
            <text class="tip-title">选择合适的照片</text>
            <text class="tip-desc">人物清晰、背景简洁的照片效果更好</text>
          </view>
        </view>
        
        <view class="tip-item">
          <view class="tip-icon">🎨</view>
          <view class="tip-content">
            <text class="tip-title">尝试不同风格</text>
            <text class="tip-desc">每种风格都有独特的艺术表现，多尝试找到最适合的</text>
          </view>
        </view>
        
        <view class="tip-item">
          <view class="tip-icon">⚡</view>
          <view class="tip-content">
            <text class="tip-title">耐心等待</text>
            <text class="tip-desc">AI生成需要一些时间，请耐心等待最佳效果</text>
          </view>
        </view>
        
        <view class="tip-item">
          <view class="tip-icon">🔄</view>
          <view class="tip-content">
            <text class="tip-title">重新生成</text>
            <text class="tip-desc">如果效果不满意，可以重新生成或调整提示词</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 常见问题 -->
    <view class="faq-section">
      <view class="section-title">
        <text class="title-text">常见问题</text>
      </view>
      
      <view class="faq-list">
        <view class="faq-item" @click="toggleFaq(0)">
          <view class="faq-question">
            <text class="question-text">生成失败怎么办？</text>
            <text class="question-arrow" :class="{ expanded: expandedFaq === 0 }">></text>
          </view>
          <view v-if="expandedFaq === 0" class="faq-answer">
            <text class="answer-text">
              生成失败可能是由于网络问题或照片质量问题。请检查网络连接，确保照片清晰，然后点击重试按钮。
            </text>
          </view>
        </view>
        
        <view class="faq-item" @click="toggleFaq(1)">
          <view class="faq-question">
            <text class="question-text">如何获得更多积分？</text>
            <text class="question-arrow" :class="{ expanded: expandedFaq === 1 }">></text>
          </view>
          <view v-if="expandedFaq === 1" class="faq-answer">
            <text class="answer-text">
              可以通过购买积分套餐或开通VIP会员来获得更多使用次数。VIP会员享有无限次数使用权限。
            </text>
          </view>
        </view>
        
        <view class="faq-item" @click="toggleFaq(2)">
          <view class="faq-question">
            <text class="question-text">生成的图片可以商用吗？</text>
            <text class="question-arrow" :class="{ expanded: expandedFaq === 2 }">></text>
          </view>
          <view v-if="expandedFaq === 2" class="faq-answer">
            <text class="answer-text">
              生成的图片仅供个人使用和分享，商业用途请联系客服咨询相关授权事宜。
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 联系我们 -->
    <view class="contact-section">
      <view class="section-title">
        <text class="title-text">需要帮助？</text>
      </view>
      
      <view class="contact-buttons">
        <button class="contact-btn" @click="contactService">
          <text class="btn-icon">💬</text>
          <text class="btn-text">联系客服</text>
        </button>
        
        <button class="contact-btn" @click="joinGroup">
          <text class="btn-icon">👥</text>
          <text class="btn-text">加入群聊</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 响应式数据
const expandedFaq = ref<number | null>(null)

// 方法
const toggleFaq = (index: number) => {
  expandedFaq.value = expandedFaq.value === index ? null : index
}

const contactService = () => {
  uni.showModal({
    title: '联系客服',
    content: '微信号：service123\n工作时间：9:00-18:00',
    showCancel: false
  })
}

const joinGroup = () => {
  uni.showModal({
    title: '加入群聊',
    content: '扫描二维码加入官方交流群，获取最新资讯和使用技巧。',
    showCancel: false
  })
}
</script>

<style lang="scss" scoped>
.guide-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0e17 0%, #1a1a2e 100%);
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 32px;
  
  .title {
    font-size: 24px;
    font-weight: bold;
    color: #0fe0ff;
    display: block;
    margin-bottom: 8px;
  }
  
  .subtitle {
    font-size: 14px;
    color: #a3a3a3;
  }
}

.guide-content {
  margin-bottom: 32px;
  
  .guide-step {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    
    .step-header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      
      .step-number {
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, #0fe0ff 0%, #00b4d8 100%);
        color: #000;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        margin-right: 12px;
      }
      
      .step-title {
        font-size: 18px;
        font-weight: 600;
        color: #ffffff;
      }
    }
    
    .step-content {
      .step-text {
        font-size: 14px;
        color: #a3a3a3;
        line-height: 1.6;
        display: block;
        margin-bottom: 12px;
      }
      
      .step-image {
        width: 100%;
        height: 120px;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.05);
      }
    }
  }
}

.tips-section,
.faq-section,
.contact-section {
  margin-bottom: 32px;
  
  .section-title {
    margin-bottom: 16px;
    
    .title-text {
      font-size: 20px;
      font-weight: 600;
      color: #ffffff;
    }
  }
}

.tips-list {
  .tip-item {
    display: flex;
    align-items: flex-start;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    
    .tip-icon {
      font-size: 24px;
      margin-right: 12px;
      margin-top: 2px;
    }
    
    .tip-content {
      flex: 1;
      
      .tip-title {
        font-size: 16px;
        font-weight: 600;
        color: #ffffff;
        display: block;
        margin-bottom: 4px;
      }
      
      .tip-desc {
        font-size: 14px;
        color: #a3a3a3;
        line-height: 1.5;
      }
    }
  }
}

.faq-list {
  .faq-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    margin-bottom: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
    
    .faq-question {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      cursor: pointer;
      
      .question-text {
        font-size: 16px;
        color: #ffffff;
      }
      
      .question-arrow {
        font-size: 16px;
        color: #a3a3a3;
        transition: transform 0.3s ease;
        
        &.expanded {
          transform: rotate(90deg);
        }
      }
    }
    
    .faq-answer {
      padding: 0 16px 16px;
      border-top: 1px solid rgba(255, 255, 255, 0.05);
      
      .answer-text {
        font-size: 14px;
        color: #a3a3a3;
        line-height: 1.6;
      }
    }
  }
}

.contact-buttons {
  display: flex;
  gap: 12px;
  
  .contact-btn {
    flex: 1;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .btn-icon {
      font-size: 24px;
      margin-bottom: 8px;
    }
    
    .btn-text {
      font-size: 14px;
      color: #ffffff;
    }
  }
}
</style>
