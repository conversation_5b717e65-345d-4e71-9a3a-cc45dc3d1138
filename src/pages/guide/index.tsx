import { View, Text } from '@tarojs/components'
import Taro from '@tarojs/taro'
import styles from './index.module.less'

const Guide = () => {
  const steps = [
    {
      title: '选择适合的风格',
      description: '从多种风格中选择你喜欢的风格',
    },
    {
      title: '上传照片',
      description: '点击你喜爱的风格并上传一张你想要转换的照片',
    },
    {
      title: '等待转换',
      description: '系统会自动处理你的照片，请耐心等待',
    },
  ]

  const handleStart = () => {
    Taro.switchTab({ url: '/pages/index/index' })
  }

  return (
    <View className={styles.container}>
      <View className={styles.header}>
        <Text className={styles.subtitle}>简单三步，开启你的吉卜力风格之旅</Text>
      </View>

      <View className={styles.steps}>
        {steps.map((step, index) => (
          <View key={index} className={styles.step}>
            <View className={styles.stepNumber}>{index + 1}</View>
            <View className={styles.stepContent}>
              <Text className={styles.stepTitle}>{step.title}</Text>
              <Text className={styles.stepDescription}>{step.description}</Text>
            </View>
          </View>
        ))}
      </View>

      <View className={styles.footer}>
        <View className={styles.startButton} onClick={handleStart}>
          <Text className={styles.buttonText}>开始体验</Text>
        </View>
      </View>
    </View>
  )
}

export default Guide
