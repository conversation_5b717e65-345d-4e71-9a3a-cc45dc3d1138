.container {
  min-height: 100vh;
  background-color: #000;
  padding: 40px 24px;
}

.header {
  text-align: center;
  margin-bottom: 48px;
}

.title {
  font-size: 64px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 12px;
  display: block;
}

.subtitle {
  font-size: 32px;
  color: rgba(255, 255, 255, 0.7);
  display: block;
}

.steps {
  margin-bottom: 48px;
}

.step {
  display: flex;
  margin-bottom: 32px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(10px);
}

.stepNumber {
  width: 64px;
  height: 64px;
  background: #4a90e2;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  font-weight: bold;
  margin-right: 32px;
  flex-shrink: 0;
}

.stepContent {
  flex: 1;
}

.stepTitle {
  font-size: 36px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 16px;
  display: block;
}

.stepDescription {
  font-size: 28px;
  color: rgba(255, 255, 255, 0.7);
  display: block;
}

.stepImage {
  width: 100%;
  height: 160px;
  border-radius: 8px;
}

.footer {
  padding: 24px 0;
}

.startButton {
  background: #4a90e2;
  border-radius: 48px;
  padding: 24px 0;
  text-align: center;
}

.buttonText {
  color: #fff;
  font-size: 28px;
  font-weight: bold;
}
