export default defineAppConfig({
  pages: [
    'pages/index/index',
    'pages/user/index',
    'pages/membership/index',
    'pages/guide/index',
    'pages/task/index',
    'pages/task/edit/index',
    'pages/discount/index',
  ],
  window: {
    navigationBarTitleText: '吉卜力风格AI',
    navigationBarTextStyle: 'white',
    navigationStyle: 'default',
  },
  tabBar: {
    color: '#a3a3a3',
    selectedColor: '#0fe0ff',
    backgroundColor: '#0a0e17',
    borderStyle: 'black',
    list: [
      {
        pagePath: 'pages/index/index',
        text: '首页',
      },
      {
        pagePath: 'pages/task/index',
        text: '画廊',
      },
      {
        pagePath: 'pages/user/index',
        text: '我的',
      },
    ],
  },
  permission: {
    'scope.writePhotosAlbum': {
      desc: '你的小程序需要使用保存图片到相册功能',
    },
    'scope.camera': {
      desc: '需要使用相机拍摄照片',
    },
    'scope.album': {
      desc: '需要访问相册选择照片',
    },
  },
})
