import Taro from '@tarojs/taro'
import { FC, PropsWithChildren, useEffect, useRef } from 'react'

import authStore from '@/common/store/auth'
import { initSentry, setupRouteTracking, captureException } from '@/common/utils'

import './app.less'

const App: FC<PropsWithChildren> = ({ children }) => {
  const { inviteCode } = Taro.getCurrentInstance().router?.params || {}
  const isInitializedRef = useRef(false)

  // 初始化认证状态
  const initializeAuth = async () => {
    try {
      // 检查登录状态
      const isLoggedIn = await authStore.checkLogin(true)

      if (!isLoggedIn) {
        // 如果未登录，则进行登录
        await authStore.login(inviteCode)
      }
    } catch (error) {
      console.error('初始化认证失败:', error)

      // 上报错误到 Sentry
      captureException(error as Error, {
        type: 'auth_init_error',
        inviteCode,
      })

      Taro.showToast({
        title: `登录失败，${error}`,
        icon: 'none',
      })
    }
  }

  useEffect(() => {
    const init = async () => {
      // 防止重复初始化
      if (isInitializedRef.current) {
        return
      }
      isInitializedRef.current = true

      // 初始化 Sentry（从环境变量读取配置）
      const sentryInitialized = initSentry()

      try {
        if (sentryInitialized) {
          // 设置路由追踪
          setupRouteTracking()
        }

        // 首次初始化认证状态
        await initializeAuth()

        // 监听应用生命周期
        Taro.onAppShow(() => {
          // 应用从后台恢复时，检查登录状态
          authStore.checkLogin()
        })

        Taro.onAppHide(() => {
          // 应用进入后台时，记录时间戳
          console.log('应用进入后台')
        })

        // 监听网络状态变化
        Taro.onNetworkStatusChange((res) => {
          if (res.isConnected) {
            // 网络连接恢复时，检查登录状态
            console.log('网络连接恢复')
            authStore.checkLogin()
          }
        })
      } catch (error) {
        console.error('应用启动时初始化失败:', error)

        // 上报错误到 Sentry（仅在初始化成功时）
        if (sentryInitialized) {
          captureException(error as Error, {
            type: 'app_init_error',
            inviteCode,
          })
        }

        Taro.showToast({
          title: `初始化失败，${error}`,
          icon: 'none',
        })
      }
    }

    init()
  }, [])

  return children
}

export default App
