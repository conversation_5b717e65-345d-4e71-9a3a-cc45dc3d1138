const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')
const dotenv = require('dotenv')

/**
 * 加载环境变量文件
 * @param {string} environment - 环境名称 (dev, prod)
 */
function loadEnvironmentVariables(environment = 'production') {
  console.log(`🔧 加载 ${environment} 环境配置...`)
  
  // 按优先级顺序加载环境变量文件
  const envFiles = [
    '.env', // 基础配置
    `.env.${environment}`, // 环境特定配置
  ]

  // 依次加载环境变量文件
  envFiles.forEach((file) => {
    const envPath = path.resolve(__dirname, '..', file)
    
    if (fs.existsSync(envPath)) {
      console.log(`📄 加载环境文件: ${file}`)
      dotenv.config({ path: envPath })
    } else {
      console.log(`⚠️  环境文件不存在: ${file}`)
    }
  })
}

/**
 * 验证必需的环境变量
 */
function validateSentryConfig() {
  const requiredVars = [
    'TARO_APP_SENTRY_ORG',
    'TARO_APP_SENTRY_PROJECT', 
    'TARO_APP_SENTRY_AUTH_TOKEN'
  ]

  const missing = requiredVars.filter(varName => !process.env[varName])
  
  if (missing.length > 0) {
    console.error('❌ 缺少必需的环境变量:')
    missing.forEach(varName => console.error(`  - ${varName}`))
    console.error('\n请检查你的环境变量文件 (.env, .env.development, .env.production)')
    process.exit(1)
  }

  // 显示配置信息（隐藏敏感信息） 
  console.log('✅ Sentry 配置验证通过:')
  console.log(`  - Organization: ${process.env.TARO_APP_SENTRY_ORG}`)
  console.log(`  - Project: ${process.env.TARO_APP_SENTRY_PROJECT}`)
  console.log(`  - Environment: ${process.env.TARO_APP_SENTRY_ENVIRONMENT || 'production'}`)
  console.log(`  - Release: ${process.env.TARO_APP_SENTRY_RELEASE || '未设置'}`)
  console.log(`  - Auth Token: ${process.env.TARO_APP_SENTRY_AUTH_TOKEN ? '已设置' : '未设置'}`)
}

/**
 * 获取 release 版本号
 */
function getReleaseVersion() {
  // 优先级: 环境变量 > package.json version > 默认值
  if (process.env.TARO_APP_SENTRY_RELEASE) {
    return process.env.TARO_APP_SENTRY_RELEASE
  }

  // 从 package.json 读取版本号
  try {
    const packagePath = path.resolve(__dirname, '..', 'package.json')
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
    return packageJson.version || '1.0.0'
  } catch (error) {
    console.warn('⚠️ 无法读取 package.json 版本号，使用默认版本')
    return '1.0.0'
  }
}

/**
 * 上传 Source Maps 到 Sentry
 */
function uploadSourceMaps() {
  console.log('📦 开始上传 Source Maps 到 Sentry...\n')
  
  // 1. 确定环境
  const environment = 'production'
  
  // 2. 加载环境变量
  loadEnvironmentVariables(environment)
  
  // 3. 验证配置
  validateSentryConfig()
  
  // 4. 获取配置值
  const SENTRY_ORG = process.env.TARO_APP_SENTRY_ORG
  const SENTRY_PROJECT = process.env.TARO_APP_SENTRY_PROJECT
  const SENTRY_AUTH_TOKEN = process.env.TARO_APP_SENTRY_AUTH_TOKEN
  const SENTRY_ENVIRONMENT = process.env.TARO_APP_SENTRY_ENVIRONMENT || 'production'
  const SENTRY_RELEASE = getReleaseVersion()
  
  console.log(`\n🎯 准备上传到 Release: ${SENTRY_RELEASE}`)
  console.log(`🌍 环境: ${SENTRY_ENVIRONMENT}\n`)

  const distPath = path.join(__dirname, '../dist')

  // 5. 检查 dist 目录
  if (!fs.existsSync(distPath)) {
    console.error('❌ dist 目录不存在，请先运行构建命令')
    console.error('   例如: npm run build:weapp')
    process.exit(1)
  }

  // 6. 检查 source maps 文件
  const mapFiles = fs.readdirSync(distPath, { recursive: true })
    .filter(file => file.toString().endsWith('.map'))
  
  if (mapFiles.length === 0) {
    console.error('❌ 没有找到 source maps 文件')
    console.error('   请确保构建时启用了 source map 生成')
    process.exit(1)
  }

  console.log(`📄 找到 ${mapFiles.length} 个 source maps 文件:`)
  mapFiles.forEach(file => console.log(`  - ${file}`))

  try {
    // 7. 设置环境变量
    const env = {
      ...process.env,
      SENTRY_AUTH_TOKEN: SENTRY_AUTH_TOKEN,
      SENTRY_ORG: SENTRY_ORG,
      SENTRY_PROJECT: SENTRY_PROJECT
    }

    // 8. 创建 release
    console.log(`\n🚀 创建 release: ${SENTRY_RELEASE}`)
    execSync(`npx @sentry/cli releases new ${SENTRY_RELEASE}`, { 
      stdio: 'inherit', 
      env 
    })

    // 9. 上传 source maps
    console.log('\n📤 上传 source maps...')
    execSync(`npx @sentry/cli releases files ${SENTRY_RELEASE} upload-sourcemaps dist --url-prefix "~/" --validate`, { 
      stdio: 'inherit', 
      env 
    })

    // 10. 完成 release (可选)
    try {
      console.log('\n✅ 完成 release')
      execSync(`npx @sentry/cli releases finalize ${SENTRY_RELEASE}`, { 
        stdio: 'inherit', 
        env 
      })
    } catch (error) {
      console.warn('⚠️ 完成 release 失败，但不影响 source maps 功能:', error.message)
    }

    // 11. 设置部署信息 (可选)
    try {
      console.log('\n📝 设置部署信息')
      execSync(`npx @sentry/cli releases deploys ${SENTRY_RELEASE} new -e ${SENTRY_ENVIRONMENT}`, { 
        stdio: 'inherit', 
        env 
      })
    } catch (error) {
      console.warn('⚠️ 设置部署信息失败，但不影响 source maps 功能:', error.message)
    }

    console.log('\n🎉 Source Maps 上传完成！')
    console.log(`📊 查看 Sentry 控制台: https://sentry.io/${SENTRY_ORG}/${SENTRY_PROJECT}/releases/${SENTRY_RELEASE}/`)

  } catch (error) {
    console.error('\n❌ 上传 source maps 失败:', error.message)
    console.error('\n💡 常见解决方案:')
    console.error('  1. 检查 TARO_APP_SENTRY_AUTH_TOKEN 是否有效')
    console.error('  2. 检查网络连接')
    console.error('  3. 检查 Sentry 项目权限')
    console.error('  4. 确认 @sentry/cli 已安装: npm install -g @sentry/cli')
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  uploadSourceMaps()
}

module.exports = { uploadSourceMaps } 